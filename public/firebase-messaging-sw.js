importScripts('https://www.gstatic.com/firebasejs/10.11.0/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.11.0/firebase-messaging-compat.js');
// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDZ5pCdhexWe2aKSJxQm4-43NlcAk-FIIE",
  authDomain: "ruhai-464104.firebaseapp.com",
  projectId: "ruhai-464104",
  storageBucket: "ruhai-464104.firebasestorage.app",
  messagingSenderId: "840416260778",
  appId: "1:840416260778:web:09bd79078e92b1de4656d3",
  measurementId: "G-GJ7373HHMR"
};

firebase.initializeApp(firebaseConfig);

const messaging = firebase.messaging();


messaging.onBackgroundMessage(function(payload) {
  const notificationTitle = payload.notification?.title || 'Notification';
  const notificationOptions = {
    body: payload.notification?.body || '',
    icon: '/assets/logos/ruh-logo-no-text.svg' // Change to your app icon if needed
  };
  self.registration.showNotification(notificationTitle, notificationOptions);
  // Notify all open clients (tabs) so the app can invalidate queries / update UI
  if (self.clients && self.clients.matchAll) {
    self.clients
      .matchAll({ type: 'window', includeUncontrolled: true })
      .then((clientList) => {
        clientList.forEach((client) => {
          try {
            client.postMessage({
              type: 'BACKGROUND_NOTIFICATION',
              payload,
            });
          } catch (err) {
            // no-op
          }
        });
      });
  }
});

self.addEventListener('notificationclick', function(event) {
  event.notification.close();
  // Customize the URL as needed
  event.waitUntil(
    clients.openWindow('/')
  );
});