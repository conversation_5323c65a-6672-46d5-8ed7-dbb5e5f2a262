import React from "react";

type PromptAtIconProps = {
  width?: number | string;
  height?: number | string;
  className?: string;
};

const PromptAtIcon: React.FC<PromptAtIconProps> = ({
  width = 20,
  height = 20,
  className = "",
  ...props
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 20 20"
    fill="none"
    className={className}
    {...props}
  >
    <path
      d="M6.54859 2.74431C8.15938 1.9906 9.97791 1.80252 11.7087 2.21111C13.4393 2.61973 14.9812 3.60101 16.0847 4.99529C17.1884 6.38979 17.7888 8.11632 17.7888 9.8947V10.634C17.7888 11.3549 17.5027 12.047 16.9929 12.5568C16.4832 13.0664 15.7918 13.3526 15.0711 13.3527C14.3501 13.3527 13.658 13.0666 13.1482 12.5568C12.963 12.3716 12.8085 12.1617 12.6853 11.9357C12.0561 12.7947 11.0415 13.3536 9.89527 13.3537C7.9856 13.3537 6.43735 11.8053 6.43726 9.89568C6.43726 7.98596 7.98554 6.43767 9.89527 6.43767C10.8561 6.43771 11.7256 6.82941 12.3523 7.46209V6.9367C12.3524 6.66068 12.5763 6.43671 12.8523 6.4367C13.1284 6.4367 13.3522 6.66067 13.3523 6.9367V9.85955C13.3524 9.87158 13.3533 9.88362 13.3533 9.89568C13.3533 9.90742 13.3524 9.91913 13.3523 9.93084V10.634C13.3523 11.0897 13.533 11.5275 13.8552 11.8498C14.1775 12.172 14.6153 12.3527 15.0711 12.3527C15.5266 12.3526 15.9637 12.1719 16.2859 11.8498C16.6082 11.5275 16.7888 11.0897 16.7888 10.634V9.8947C16.7888 8.3416 16.2644 6.83424 15.3005 5.61638C14.3367 4.3985 12.9899 3.54158 11.4783 3.18474C9.96682 2.82799 8.37906 2.99145 6.97242 3.64959C5.56565 4.30785 4.4219 5.42252 3.7273 6.8117C3.03286 8.20076 2.82788 9.78368 3.14527 11.3039C3.46272 12.8242 4.28388 14.1935 5.47633 15.1886C6.66877 16.1838 8.16295 16.7469 9.71558 16.7873C11.2681 16.8276 12.7886 16.3421 14.031 15.4103L14.3308 15.8107L14.6316 16.2101C13.2089 17.2772 11.467 17.8325 9.68922 17.7863C7.91143 17.7401 6.20106 17.0957 4.8357 15.9562C3.47031 14.8167 2.53025 13.2489 2.16676 11.508C1.80338 9.76733 2.03762 7.95492 2.83277 6.36443C3.62811 4.77376 4.93779 3.49805 6.54859 2.74431ZM14.031 15.4103C14.2519 15.2446 14.5655 15.29 14.7312 15.5109C14.8966 15.7317 14.8521 16.0444 14.6316 16.2101L14.031 15.4103ZM9.89527 7.43767C8.53782 7.43767 7.43726 8.53825 7.43726 9.89568C7.43735 11.253 8.53787 12.3537 9.89527 12.3537C11.2464 12.3536 12.3422 11.263 12.3523 9.91423V9.86834C12.3376 8.52355 11.2435 7.43774 9.89527 7.43767Z"
      fill="black"
    />
  </svg>
);

export default PromptAtIcon;
