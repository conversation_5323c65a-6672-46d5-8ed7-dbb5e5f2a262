import React from "react";

export type BuildingIconProps = {
  width?: string | number;
  height?: string | number;
} & React.SVGProps<SVGSVGElement>;

export default function BuildingIcon({
  width = 16,
  height = 17,
  ...props
}: BuildingIconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 16 17"
      fill="none"
      {...props}
    >
      <path
        d="M7.53516 0.413086C7.58784 0.419898 7.63789 0.441588 7.67871 0.475586L10.4346 2.77344H10.4355C10.4637 2.797 10.4869 2.82587 10.5039 2.8584C10.521 2.89114 10.531 2.92707 10.5342 2.96387L11.6836 16.2881C11.6869 16.3267 11.6814 16.3657 11.6689 16.4023C11.6565 16.439 11.6365 16.4725 11.6104 16.501C11.5842 16.5295 11.5529 16.5528 11.5176 16.5684C11.4821 16.584 11.4431 16.5918 11.4043 16.5918H7.49902C7.42493 16.5917 7.35416 16.5622 7.30176 16.5098C7.24937 16.4574 7.21981 16.3866 7.21973 16.3125V0.69043C7.21977 0.637323 7.23517 0.584848 7.26367 0.540039C7.29219 0.495442 7.33291 0.459952 7.38086 0.4375C7.42896 0.415003 7.48249 0.406293 7.53516 0.413086ZM7.7793 16.0322H11.0996L9.98633 3.12695L7.7793 1.28711V16.0322Z"
        fill="black"
        stroke="black"
        strokeWidth="0.1"
      />
      <path
        d="M12.5107 9.11914C12.5834 9.11297 12.6556 9.1354 12.7119 9.18164C12.7541 9.21633 12.7856 9.26209 12.8018 9.31348L12.8125 9.36719V9.36816L12.8701 10.0342C12.8765 10.108 12.8533 10.1816 12.8057 10.2383C12.7579 10.295 12.6891 10.3295 12.6152 10.3359L12.6162 10.3369C12.6079 10.3377 12.5992 10.3379 12.5908 10.3379C12.521 10.3377 12.4538 10.3118 12.4023 10.2646C12.3508 10.2173 12.3185 10.1517 12.3125 10.082L12.2549 9.41602V9.41504C12.2504 9.34217 12.2746 9.27015 12.3223 9.21484C12.3699 9.15954 12.438 9.12542 12.5107 9.11914Z"
        fill="black"
        stroke="black"
        strokeWidth="0.1"
      />
      <path
        d="M12.957 13.6777C12.9753 13.6796 12.9931 13.684 13.0107 13.6895C13.046 13.7004 13.0792 13.7175 13.1074 13.7412C13.1356 13.765 13.159 13.7943 13.1758 13.8271C13.1925 13.8599 13.2023 13.8959 13.2051 13.9326L13.4072 16.2852C13.4105 16.3237 13.4061 16.3628 13.3936 16.3994C13.381 16.436 13.3602 16.4695 13.334 16.498C13.3078 16.5265 13.2766 16.5499 13.2412 16.5654C13.2059 16.581 13.1675 16.5888 13.1289 16.5889H11.4053C11.3313 16.5888 11.2604 16.5591 11.208 16.5068C11.1555 16.4544 11.126 16.3828 11.126 16.3086C11.1261 16.2345 11.1556 16.1637 11.208 16.1113C11.2604 16.0589 11.3312 16.0294 11.4053 16.0293H12.8232L12.6475 13.9805C12.6412 13.9066 12.6651 13.8331 12.7129 13.7764C12.7606 13.7198 12.8286 13.6841 12.9023 13.6777H12.957Z"
        fill="black"
        stroke="black"
        strokeWidth="0.1"
      />
      <path
        d="M12.7031 11.3848C12.7402 11.3815 12.778 11.3861 12.8135 11.3975C12.8488 11.4088 12.882 11.427 12.9102 11.4512C12.9382 11.4753 12.9609 11.505 12.9775 11.5381C12.994 11.571 13.0042 11.6068 13.0068 11.6436L13.0664 12.3252C13.0696 12.3618 13.0657 12.3995 13.0547 12.4346C13.0436 12.4695 13.0255 12.5022 13.002 12.5303C12.9784 12.5583 12.9495 12.5817 12.917 12.5986C12.9007 12.6071 12.8837 12.614 12.8662 12.6191L12.8125 12.6289C12.8044 12.6297 12.7962 12.6289 12.7881 12.6289V12.6299C12.7182 12.6298 12.6501 12.6039 12.5986 12.5566C12.5472 12.5095 12.5159 12.4445 12.5098 12.375L12.4492 11.6934V11.6377C12.4509 11.6193 12.4546 11.6008 12.46 11.583C12.4706 11.5475 12.4881 11.514 12.5117 11.4854C12.5353 11.4567 12.5648 11.4333 12.5977 11.416C12.6304 11.3988 12.6662 11.388 12.7031 11.3848Z"
        fill="black"
        stroke="black"
        strokeWidth="0.1"
      />
      <path
        d="M10.3691 4.29492C10.4429 4.28838 10.5164 4.311 10.5732 4.3584L12.4297 5.90723C12.4578 5.93075 12.481 5.95968 12.498 5.99219C12.5151 6.02488 12.5261 6.06092 12.5293 6.09766L12.6709 7.76172C12.674 7.79822 12.6703 7.8352 12.6592 7.87012C12.6481 7.90507 12.6301 7.93777 12.6064 7.96582C12.5829 7.99372 12.5539 8.01635 12.5215 8.0332C12.4889 8.05011 12.4526 8.06132 12.416 8.06445H12.3936C12.3234 8.06471 12.2559 8.03856 12.2041 7.99121C12.1524 7.94391 12.1194 7.8794 12.1133 7.80957L11.9805 6.26172L10.2148 4.78809C10.1579 4.74053 10.1219 4.67254 10.1152 4.59863C10.1086 4.52488 10.1314 4.45145 10.1787 4.39453C10.2263 4.33756 10.2952 4.30158 10.3691 4.29492Z"
        fill="black"
        stroke="black"
        strokeWidth="0.1"
      />
      <path
        d="M7.46289 0.40918C7.51559 0.402273 7.56901 0.411153 7.61719 0.433594C7.66553 0.456177 7.70677 0.492056 7.73535 0.537109C7.76388 0.582113 7.77944 0.634216 7.7793 0.6875V16.3086C7.7793 16.3827 7.74965 16.4544 7.69727 16.5068C7.64481 16.5593 7.57321 16.5889 7.49902 16.5889H3.59375C3.55509 16.589 3.51689 16.5809 3.48145 16.5654C3.44604 16.55 3.41393 16.5274 3.3877 16.499C3.36138 16.4704 3.34068 16.4362 3.32812 16.3994C3.31561 16.3627 3.31114 16.3238 3.31445 16.2852L4.46289 2.96094C4.46607 2.92414 4.47705 2.88821 4.49414 2.85547C4.51123 2.82276 4.53415 2.79316 4.5625 2.76953L7.31934 0.472656L7.38672 0.430664C7.41077 0.420093 7.43658 0.412656 7.46289 0.40918ZM5.01074 3.12402L3.89941 16.0293H7.21973V1.28418L5.01074 3.12402Z"
        fill="black"
        stroke="black"
        strokeWidth="0.1"
      />
      <path
        d="M4.38379 7.34766C4.42015 7.35285 4.4557 7.36507 4.4873 7.38379C4.51873 7.40243 4.5464 7.42685 4.56836 7.45605C4.59035 7.48538 4.60611 7.51919 4.61523 7.55469C4.62435 7.59027 4.62629 7.6277 4.62109 7.66406C4.61587 7.70032 4.60362 7.73508 4.58496 7.7666C4.56631 7.79808 4.54193 7.82566 4.5127 7.84766L2.82031 9.11621L1.74023 16.0332H3.5957C3.66989 16.0332 3.74149 16.0628 3.79395 16.1152C3.84612 16.1676 3.87496 16.2386 3.875 16.3125C3.875 16.3867 3.84637 16.4583 3.79395 16.5107C3.74149 16.5632 3.66989 16.5928 3.5957 16.5928H1.41309C1.37278 16.5927 1.33251 16.5833 1.2959 16.5664C1.25943 16.5495 1.22731 16.5247 1.20117 16.4941C1.17495 16.4635 1.15555 16.4275 1.14453 16.3887C1.13357 16.3499 1.13052 16.3093 1.13672 16.2695L2.28516 8.91797C2.29642 8.84595 2.33622 8.78105 2.39453 8.7373L4.17676 7.40039C4.20614 7.37835 4.23981 7.36263 4.27539 7.35352C4.31083 7.34445 4.34758 7.34253 4.38379 7.34766Z"
        fill="black"
        stroke="black"
        strokeWidth="0.1"
      />
      <path
        d="M7.56055 12.5742C7.63293 12.5902 7.69641 12.6338 7.73633 12.6963C7.77624 12.7588 7.78944 12.8348 7.77344 12.9072C7.75743 12.9796 7.71387 13.0431 7.65137 13.083L3.83496 15.5176C3.79024 15.5461 3.73858 15.5614 3.68555 15.5615C3.62509 15.5616 3.56601 15.542 3.51758 15.5059C3.46911 15.4696 3.43301 15.4185 3.41602 15.3604C3.39912 15.3023 3.40161 15.2402 3.42285 15.1836C3.44414 15.127 3.48317 15.0784 3.53418 15.0459L7.35059 12.6113C7.413 12.5716 7.48828 12.5583 7.56055 12.5742Z"
        fill="black"
        stroke="black"
        strokeWidth="0.1"
      />
      <path
        d="M7.56055 10.1094C7.63272 10.1254 7.69551 10.1692 7.73535 10.2314C7.77524 10.294 7.7894 10.37 7.77344 10.4424C7.75744 10.5148 7.71293 10.5783 7.65039 10.6182L4.05957 12.9072C4.01494 12.936 3.96326 12.9518 3.91016 12.9521H3.90918C3.84875 12.9521 3.78956 12.9327 3.74121 12.8965C3.69288 12.8602 3.65757 12.809 3.64062 12.751C3.62368 12.6929 3.62622 12.6308 3.64746 12.5742C3.66874 12.5176 3.70779 12.4691 3.75879 12.4365L7.34961 10.1465C7.41214 10.1066 7.48812 10.0934 7.56055 10.1094Z"
        fill="black"
        stroke="black"
        strokeWidth="0.1"
      />
      <path
        d="M7.55762 7.64941C7.62712 7.66661 7.68697 7.71021 7.72559 7.77051C7.76426 7.831 7.77834 7.90413 7.76465 7.97461C7.75092 8.04502 7.7108 8.10784 7.65234 8.14941L7.65039 8.15039L4.28223 10.2988L4.28125 10.2979C4.23679 10.3265 4.1857 10.3425 4.13281 10.3428C4.07234 10.3428 4.01328 10.3233 3.96484 10.2871C3.91638 10.2508 3.88028 10.1997 3.86328 10.1416C3.84639 10.0836 3.84888 10.0214 3.87012 9.96484C3.89141 9.90822 3.93045 9.85969 3.98145 9.82715L7.34961 7.67969L7.35156 7.67871C7.41389 7.64307 7.4879 7.63226 7.55762 7.64941Z"
        fill="black"
        stroke="black"
        strokeWidth="0.1"
      />
      <path
        d="M7.55957 5.17969C7.59544 5.18761 7.63005 5.20161 7.66016 5.22266C7.69021 5.24368 7.71562 5.27085 7.73535 5.30176C7.75505 5.33263 7.76798 5.36727 7.77441 5.40332C7.78082 5.43949 7.78038 5.47683 7.77246 5.5127C7.76455 5.5485 7.7495 5.58224 7.72852 5.6123C7.70747 5.64241 7.68038 5.66871 7.64941 5.68848L4.50684 7.69336C4.46211 7.72188 4.41047 7.73717 4.35742 7.7373C4.29694 7.73737 4.23789 7.71785 4.18945 7.68164C4.14099 7.64536 4.10488 7.59424 4.08789 7.53613C4.07102 7.47813 4.07349 7.41593 4.09473 7.35938C4.11602 7.30278 4.15507 7.25421 4.20605 7.22168L7.34863 5.2168C7.37949 5.1971 7.41416 5.18319 7.4502 5.17676C7.48635 5.17035 7.52372 5.17178 7.55957 5.17969Z"
        fill="black"
        stroke="black"
        strokeWidth="0.1"
      />
      <path
        d="M7.55957 2.71875C7.5952 2.72668 7.62924 2.74083 7.65918 2.76172C7.68921 2.78271 7.71463 2.80996 7.73438 2.84082C7.75409 2.87171 7.76799 2.9063 7.77441 2.94238C7.78082 2.97852 7.78036 3.01592 7.77246 3.05176C7.76457 3.08747 7.74941 3.12136 7.72852 3.15137C7.70747 3.18148 7.68038 3.20778 7.64941 3.22754L4.73145 5.08789C4.68662 5.11656 4.63427 5.13092 4.58105 5.13086C4.5208 5.13081 4.46234 5.11223 4.41406 5.07617C4.3656 5.03989 4.32949 4.98877 4.3125 4.93066C4.29557 4.87261 4.29808 4.81052 4.31934 4.75391C4.34062 4.69723 4.37963 4.64877 4.43066 4.61621L7.34863 2.75586C7.37958 2.73612 7.41405 2.72222 7.4502 2.71582C7.48632 2.70944 7.52375 2.71084 7.55957 2.71875Z"
        fill="black"
        stroke="black"
        strokeWidth="0.1"
      />
    </svg>
  );
}
