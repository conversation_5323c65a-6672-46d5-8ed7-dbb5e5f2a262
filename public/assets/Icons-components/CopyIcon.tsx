import React from "react";

export type CopyIconProps = {
  width?: number | string;
  height?: number | string;
  className?: string;
};

const CopyIcon = ({ width = 24, height = 24, className }: CopyIconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    className={className}
  >
    <rect width="24" height="24" rx="6" fill="#F4F4F4" />
    <g clipPath="url(#clip0_2369_21220)">
      <path
        d="M17.3335 9.33301H10.6668C9.93045 9.33301 9.3335 9.92996 9.3335 10.6663V17.333C9.3335 18.0694 9.93045 18.6663 10.6668 18.6663H17.3335C18.0699 18.6663 18.6668 18.0694 18.6668 17.333V10.6663C18.6668 9.92996 18.0699 9.33301 17.3335 9.33301Z"
        stroke="#313131"
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.66683 14.6663C5.9335 14.6663 5.3335 14.0663 5.3335 13.333V6.66634C5.3335 5.93301 5.9335 5.33301 6.66683 5.33301H13.3335C14.0668 5.33301 14.6668 5.93301 14.6668 6.66634"
        stroke="#313131"
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_2369_21220">
        <rect width="16" height="16" fill="white" transform="translate(4 4)" />
      </clipPath>
    </defs>
  </svg>
);

export default CopyIcon;
