import React from "react";

export type MdcDotsIconProps = {
  width?: string | number;
  height?: string | number;
} & React.SVGProps<SVGSVGElement>;

const MdcDotsIcon = ({ width = 14, height = 14, ...props }: MdcDotsIconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 14 14"
    fill="none"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.91667 4.08333C2.91667 3.439 3.439 2.91667 4.08333 2.91667C4.72767 2.91667 5.25 3.439 5.25 4.08333C5.25 4.72767 4.72767 5.25 4.08333 5.25C3.439 5.25 2.91667 4.72767 2.91667 4.08333ZM4.08333 1.75C2.79467 1.75 1.75 2.79467 1.75 4.08333C1.75 5.372 2.79467 6.41667 4.08333 6.41667C5.372 6.41667 6.41667 5.372 6.41667 4.08333C6.41667 2.79467 5.372 1.75 4.08333 1.75ZM8.75 4.08333C8.75 3.439 9.27232 2.91667 9.91667 2.91667C10.561 2.91667 11.0833 3.439 11.0833 4.08333C11.0833 4.72767 10.561 5.25 9.91667 5.25C9.27232 5.25 8.75 4.72767 8.75 4.08333ZM9.91667 1.75C8.62802 1.75 7.58333 2.79467 7.58333 4.08333C7.58333 5.372 8.62802 6.41667 9.91667 6.41667C11.2053 6.41667 12.25 5.372 12.25 4.08333C12.25 2.79467 11.2053 1.75 9.91667 1.75ZM4.08333 8.75C3.439 8.75 2.91667 9.27232 2.91667 9.91667C2.91667 10.561 3.439 11.0833 4.08333 11.0833C4.72767 11.0833 5.25 10.561 5.25 9.91667C5.25 9.27232 4.72767 8.75 4.08333 8.75ZM1.75 9.91667C1.75 8.62802 2.79467 7.58333 4.08333 7.58333C5.372 7.58333 6.41667 8.62802 6.41667 9.91667C6.41667 11.2053 5.372 12.25 4.08333 12.25C2.79467 12.25 1.75 11.2053 1.75 9.91667ZM8.75 9.91667C8.75 9.27232 9.27232 8.75 9.91667 8.75C10.561 8.75 11.0833 9.27232 11.0833 9.91667C11.0833 10.561 10.561 11.0833 9.91667 11.0833C9.27232 11.0833 8.75 10.561 8.75 9.91667ZM9.91667 7.58333C8.62802 7.58333 7.58333 8.62802 7.58333 9.91667C7.58333 11.2053 8.62802 12.25 9.91667 12.25C11.2053 12.25 12.25 11.2053 12.25 9.91667C12.25 8.62802 11.2053 7.58333 9.91667 7.58333Z"
      fill="#1D1F1B"
    />
  </svg>
);

export default MdcDotsIcon; 