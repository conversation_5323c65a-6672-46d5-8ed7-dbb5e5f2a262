import * as React from "react";

function TokenInfoIcon(props: any) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={28}
      height={28}
      viewBox="0 0 28 28"
      fill="none"
      {...props}
    >
      <g clipPath="url(#clip0_3355_10480)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0 13.992C0 6.282 6.282 0 13.992 0 21.718 0 28 6.282 28 13.992 28 21.718 21.718 28 13.992 28 6.282 28 0 21.718 0 13.992zm27.016 0c0-7.168-5.84-13.008-13.024-13.008C6.824.984.984 6.824.984 13.992c0 7.184 5.84 13.024 13.008 13.024 7.184 0 13.024-5.84 13.024-13.024zm-4.888-5.25a9.827 9.827 0 00-2.887-2.87l-.886 1.493c-.328.558-1.165.05-.837-.509l.886-1.476a9.536 9.536 0 00-3.92-1.05v1.74c0 .655-.984.655-.984 0V4.33a9.53 9.53 0 00-3.904 1.05l.886 1.476c.328.558-.509 1.067-.837.509l-.902-1.493a9.581 9.581 0 00-2.87 2.87l1.492.903c.558.328.033 1.165-.509.837L5.38 9.596A9.53 9.53 0 004.33 13.5h1.74c.655 0 .655.984 0 .984H3.821a.493.493 0 01-.492-.492A10.64 10.64 0 0113.992 3.33c5.954 0 10.727 4.855 10.662 10.678-.033.263-.164.476-.492.476H21.93c-.656 0-.656-.984 0-.984h1.739a9.792 9.792 0 00-1.05-3.904l-1.477.886c-.54.328-1.082-.509-.508-.837l1.493-.902zm-3.15 14.435H8.858c-.656 0-.656-.984 0-.984h10.12c.64 0 .64.984 0 .984zM15.96 25.59h-4.035c-.656 0-.656-.984 0-.984h4.035c.656 0 .656.984 0 .984z"
          fill="url(#paint0_linear_3355_10480)"
        />
        <g filter="url(#filter0_d_3355_10480)">
          <path
            d="M13.869 11.387l-1.1 5.02c-1.541 1.394-.64 4.1 1.575 4.1 1.329 0 2.362-1.033 2.362-2.345 0-.705-.295-1.329-.77-1.755l-1.1-5.02c-.114-.524-.853-.508-.967 0z"
            fill="#7B5AFF"
          />
        </g>
      </g>
      <defs>
        <filter
          id="filter0_d_3355_10480"
          x={12}
          y={11}
          width={4.70642}
          height={10.5078}
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB"
        >
          <feFlood floodOpacity={0} result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy={1} />
          <feComposite in2="hardAlpha" operator="out" />
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0" />
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_3355_10480"
          />
          <feBlend
            in="SourceGraphic"
            in2="effect1_dropShadow_3355_10480"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_3355_10480"
          x1={-1.82539e-7}
          y1={14}
          x2={28.3153}
          y2={14}
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#AE00D0" />
          <stop offset={1} stopColor="#7B5AFF" />
        </linearGradient>
        <clipPath id="clip0_3355_10480">
          <path fill="#fff" d="M0 0H28V28H0z" />
        </clipPath>
      </defs>
    </svg>
  );
}

export default TokenInfoIcon;
