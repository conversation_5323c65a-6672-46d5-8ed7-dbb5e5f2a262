import React from "react";

export type BookOutlineIconProps = {
  width?: number | string;
  height?: number | string;
} & React.SVGProps<SVGSVGElement>;

const BookOutlineIcon = ({
  width = 24,
  height = 24,
  ...props
}: BookOutlineIconProps) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <path
      d="M21.75 4.125H15C14.4377 4.12482 13.8814 4.23995 13.3654 4.46328C12.8494 4.68662 12.3847 5.0134 12 5.42344C11.6153 5.0134 11.1506 4.68662 10.6346 4.46328C10.1186 4.23995 9.56226 4.12482 9 4.125H2.25C1.95163 4.125 1.66548 4.24353 1.4545 4.4545C1.24353 4.66548 1.125 4.95163 1.125 5.25V18.75C1.125 19.0484 1.24353 19.3345 1.4545 19.5455C1.66548 19.7565 1.95163 19.875 2.25 19.875H9C9.49728 19.875 9.97419 20.0725 10.3258 20.4242C10.6775 20.7758 10.875 21.2527 10.875 21.75C10.875 22.0484 10.9935 22.3345 11.2045 22.5455C11.4155 22.7565 11.7016 22.875 12 22.875C12.2984 22.875 12.5845 22.7565 12.7955 22.5455C13.0065 22.3345 13.125 22.0484 13.125 21.75C13.125 21.2527 13.3225 20.7758 13.6742 20.4242C14.0258 20.0725 14.5027 19.875 15 19.875H21.75C22.0484 19.875 22.3345 19.7565 22.5455 19.5455C22.7565 19.3345 22.875 19.0484 22.875 18.75V5.25C22.875 4.95163 22.7565 4.66548 22.5455 4.4545C22.3345 4.24353 22.0484 4.125 21.75 4.125ZM9 17.625H3.375V6.375H9C9.49728 6.375 9.97419 6.57254 10.3258 6.92418C10.6775 7.27581 10.875 7.75272 10.875 8.25V18.0759C10.2947 17.7789 9.65194 17.6243 9 17.625ZM20.625 17.625H15C14.3479 17.6246 13.7051 17.7798 13.125 18.0778V8.25C13.125 7.75272 13.3225 7.27581 13.6742 6.92418C14.0258 6.57254 14.5027 6.375 15 6.375H20.625V17.625Z"
      fill="#313131"
    />
  </svg>
);

export default BookOutlineIcon;
