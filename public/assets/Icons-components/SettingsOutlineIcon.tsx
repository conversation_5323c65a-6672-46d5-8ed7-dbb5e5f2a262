import React from "react";

export type SettingsOutlineIconProps = {
  width?: string | number;
  height?: string | number;
} & React.SVGProps<SVGSVGElement>;

export default function SettingsOutlineIcon({
  width = 20,
  height = 21,
  ...props
}: SettingsOutlineIconProps) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 20 21"
      fill="none"
      {...props}
    >
      <g clipPath="url(#clip0_4291_11784)">
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M8.92973 3.59821C8.92973 3.00648 9.40945 2.52679 10.0012 2.52679C10.5929 2.52679 11.0726 3.00648 11.0726 3.59821C11.0726 4.18995 10.5929 4.66964 10.0012 4.66964C9.40945 4.66964 8.92973 4.18995 8.92973 3.59821ZM10.0012 1.8125C9.01493 1.8125 8.21545 2.61199 8.21545 3.59821C8.21545 4.46212 8.82893 5.18275 9.64402 5.34821V7.04121H3.81069C3.08746 7.04121 2.50116 7.62755 2.50116 8.35074V9.92764C2.43116 9.90855 2.35749 9.89836 2.28143 9.89836H0.852863C0.392627 9.89836 0.0195312 10.2715 0.0195312 10.7317V15.4936C0.0195312 15.9538 0.392627 16.3269 0.852863 16.3269H2.28143C2.35749 16.3269 2.43116 16.3167 2.50116 16.2976V17.8745C2.50116 18.5978 3.08746 19.1841 3.81069 19.1841H16.1916C16.9149 19.1841 17.5012 18.5978 17.5012 17.8745V16.2977C17.5712 16.3168 17.6448 16.327 17.7209 16.327H19.1495C19.6097 16.327 19.9828 15.9539 19.9828 15.4936V10.7317C19.9828 10.2715 19.6097 9.89841 19.1495 9.89841H17.7209C17.6448 9.89841 17.5712 9.9086 17.5012 9.92769V8.35074C17.5012 7.62755 16.9149 7.04121 16.1916 7.04121H10.3583V5.34821C11.1734 5.18275 11.7869 4.46212 11.7869 3.59821C11.7869 2.61199 10.9874 1.8125 10.0012 1.8125ZM0.852863 10.6126C0.787115 10.6126 0.733815 10.6659 0.733815 10.7317V15.4936C0.733815 15.5594 0.787115 15.6126 0.852863 15.6126H2.28143C2.34719 15.6126 2.40048 15.5594 2.40048 15.4936V10.7317C2.40048 10.6659 2.34719 10.6126 2.28143 10.6126H0.852863ZM17.7209 10.6127C17.6552 10.6127 17.6019 10.666 17.6019 10.7317V15.4936C17.6019 15.5594 17.6552 15.6127 17.7209 15.6127H19.1495C19.2152 15.6127 19.2685 15.5594 19.2685 15.4936V10.7317C19.2685 10.666 19.2152 10.6127 19.1495 10.6127H17.7209ZM3.81069 7.7555C3.48194 7.7555 3.21545 8.02202 3.21545 8.35074V17.8745C3.21545 18.2033 3.48194 18.4698 3.81069 18.4698H16.1916C16.5204 18.4698 16.7869 18.2033 16.7869 17.8745V8.35074C16.7869 8.02202 16.5204 7.7555 16.1916 7.7555H3.81069ZM7.14402 13.1127C7.14402 13.5072 6.82421 13.827 6.42973 13.827C6.03521 13.827 5.71545 13.5072 5.71545 13.1127C5.71545 12.7182 6.03521 12.3984 6.42973 12.3984C6.82421 12.3984 7.14402 12.7182 7.14402 13.1127ZM7.85831 13.1127C7.85831 13.9016 7.21869 14.5413 6.42973 14.5413C5.64073 14.5413 5.00116 13.9016 5.00116 13.1127C5.00116 12.3237 5.64073 11.6841 6.42973 11.6841C7.21869 11.6841 7.85831 12.3237 7.85831 13.1127ZM13.5726 13.827C13.9671 13.827 14.2869 13.5072 14.2869 13.1127C14.2869 12.7182 13.9671 12.3984 13.5726 12.3984C13.1781 12.3984 12.8583 12.7182 12.8583 13.1127C12.8583 13.5072 13.1781 13.827 13.5726 13.827ZM13.5726 14.5413C14.3615 14.5413 15.0012 13.9016 15.0012 13.1127C15.0012 12.3237 14.3615 11.6841 13.5726 11.6841C12.7836 11.6841 12.144 12.3237 12.144 13.1127C12.144 13.9016 12.7836 14.5413 13.5726 14.5413Z"
          fill="black"
          stroke="black"
          strokeWidth={0.2}
        />
      </g>
      <defs>
        <clipPath id="clip0_4291_11784">
          <rect
            width="20"
            height="20"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
}
