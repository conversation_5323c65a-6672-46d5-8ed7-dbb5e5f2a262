name: Build and Deploy to Cloud Run
on:
  push:
    branches:
      - dev
      - main
jobs:
  build:
    uses: ruh-ai/reusable-workflows-and-charts/.github/workflows/reusable-build.yaml@workflow/v1.0.0
    with:
      gcp_repository: "ruh-ai"
      gcp_artifact_registry_host: "us-central1-docker.pkg.dev"
      slack_channel: "ruh-cicd"
    secrets: inherit
  deploy:
    needs: build
    uses: ruh-ai/reusable-workflows-and-charts/.github/workflows/reusable-deploy-cloudrun.yaml@workflow/v1.0.0
    with:
      region: "us-central1"
      port: "3000"
      memory: "1Gi"
      cpu: "1"
      min_instances: "1"
      slack_channel: "ruh-cicd"
    secrets: inherit
