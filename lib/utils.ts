import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import {
  TASK_MODE_PLACEHOLDER_TEXT,
  SEARCH_MODE_PLACEHOLDER_TEXT,
} from "@/shared/constants";
import { AIChatMode, TaskStatus } from "@/shared/enums";
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

// Export GCS utility functions
export { uploadToGCS } from "@/app/api/gcs";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function capitalizeFirstLetter(name: string): string {
  if (!name) return "";
  return name.charAt(0).toUpperCase() + name.slice(1);
}

export function capitalizeFirstCharacter(name: string) {
  if (!name?.length) return "";
  return name
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");
}

export function resetEmployeeCreateState() {
  localStorage.removeItem("employeeToolsFormData");
  useEmployeeCreateStore.getState().reset();
}

export function getTimeAgo(dateString: string) {
  const rtf = new Intl.RelativeTimeFormat("en", { numeric: "auto" });
  const date = new Date(dateString);
  const now = new Date();
  const diff = (date.getTime() - now.getTime()) / 1000; // in seconds
  if (Math.abs(diff) < 60) {
    return "just now";
  }
  const divisions = [
    { amount: 60, name: "seconds" },
    { amount: 60, name: "minutes" },
    { amount: 24, name: "hours" },
    { amount: 7, name: "days" },
    { amount: 4.34524, name: "weeks" },
    { amount: 12, name: "months" },
    { amount: Number.POSITIVE_INFINITY, name: "years" },
  ];

  let duration = diff;
  for (const division of divisions) {
    if (Math.abs(duration) < division.amount) {
      return rtf.format(Math.round(duration), division.name as any);
    }
    duration /= division.amount;
  }
}

export const getStatus = (status: string) => {
  switch (status) {
    case TaskStatus.TASK_STATUS_UNSPECIFIED:
      return "Unspecified";
    case TaskStatus.TASK_STATUS_RUNNING:
      return "Running";
    case TaskStatus.TASK_STATUS_COMPLETED:
      return "Completed";
    case TaskStatus.TASK_STATUS_FAILED:
      return "Failed";
    case TaskStatus.TASK_STATUS_PAUSED:
      return "Paused";
    case TaskStatus.TASK_STATUS_CANCELLED:
      return "Cancelled";
  }
};

export const getContextWindowPercentage = (
  contextWindow: number,
  inputTokens: number,
  outputTokens: number
) => {
  const totalTokens = inputTokens + outputTokens;
  const remainingTokens = contextWindow - totalTokens;
  return ((remainingTokens / contextWindow) * 100).toFixed(2);
};
export function globalAgentPlaceholderText(
  setPlaceholderText: (text: string) => void,
  setSlide: (slide: boolean) => void,
  chatMode: string
) {
  const arr =
    chatMode === AIChatMode.ACT
      ? TASK_MODE_PLACEHOLDER_TEXT
      : SEARCH_MODE_PLACEHOLDER_TEXT;
  let index = 0;
  setPlaceholderText(arr[index]);
  return setInterval(() => {
    setSlide(true);
    setTimeout(() => {
      index = (index + 1) % arr.length;
      setPlaceholderText(arr[index]);
      setSlide(false);
    }, 600); // 600ms matches your animation duration
  }, 5000); // 5 seconds for each placeholder
}

/**
 * Returns a payload with only the changed fields between original and updated agent data.
 * For arrays, if changed, returns the full new array. For objects/scalars, returns only changed values.
 */
export function getAgentEditPayload<T extends Record<string, any>>(
  original: T,
  updated: T
): Partial<T> {
  if (
    updated.agent_capabilities &&
    updated.agent_capabilities.capabilities?.length == 0 &&
    original.agent_capabilities.capabilities == null
  ) {
    updated.agent_capabilities.capabilities = null;
  }
  const prevValues = {
    ...original,
    agent_capabilities: {
      capabilities: original?.agent_capabilities?.capabilities,
    },
  };
  const diff: Partial<T> = {};
  for (const key in updated) {
    if (!Object.prototype.hasOwnProperty.call(updated, key)) continue;
    const origVal = prevValues[key];
    const newVal = updated[key];
    if (Array.isArray(origVal) && Array.isArray(newVal)) {
      // Compare arrays deeply
      if (
        origVal.length !== newVal.length ||
        origVal.some(
          (v: unknown, i: number) =>
            JSON.stringify(v) !== JSON.stringify(newVal[i])
        )
      ) {
        diff[key] = newVal;
      }
    } else if (
      typeof origVal === "object" &&
      typeof newVal === "object" &&
      origVal !== null &&
      newVal !== null
    ) {
      // Deep compare objects
      if (JSON.stringify(origVal) !== JSON.stringify(newVal)) {
        diff[key] = newVal;
      }
    } else {
      if (origVal !== newVal) {
        diff[key] = newVal;
      }
    }
  }
  return diff;
}
