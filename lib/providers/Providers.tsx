"use client";

import React from "react";
import { Theme<PERSON>rovider } from "./ThemeProvider";
import { Toaster } from "sonner";
import QueryProvider from "./QueryProvider";
import { SessionInitializer } from "@/components/auth/SessionInitializer";
import { OutOfCreditsModal } from "@/components/modals/OutOfCreditsModal";
import { TourProvider } from "./TourProvider";
import {
  useFirebaseMessaging,
  requestFirebaseNotificationPermission,
} from "@/hooks/useFirebaseMessaging";
import { useCallback, useEffect } from "react";
import { useNotificationStore } from "@/hooks/use-notification";
import { useQueryClient } from "@tanstack/react-query";
import { userApi } from "@/app/api/user";

function FirebaseNotificationHandler() {
  const setHasUnread = useNotificationStore((s) => s.setHasUnread);
  const queryClient = useQueryClient();
  // Request notification permission and log FCM token on mount
  useEffect(() => {
    // Only run on client (browser)
    if (typeof window !== "undefined") {
      requestFirebaseNotificationPermission().then(async (token) => {
        if (token) {
          await userApi.updateUserDetails({ fcm_token: token });
        } else {
          console.warn("Notification permission denied or error occurred");
        }
      });
    }
  }, []);

  // Listen to service worker messages for background notifications
  useEffect(() => {
    if (typeof window === "undefined") return;
    async function handleMessage(event: MessageEvent) {
      if (event?.data?.type === "BACKGROUND_NOTIFICATION") {
        await queryClient.refetchQueries({
          queryKey: ["notifications"],
          type: "all",
        });
      }
      if (!useNotificationStore.getState().isNotificationOpen) {
        setHasUnread(true);
      }
    }
    navigator.serviceWorker?.addEventListener?.("message", handleMessage);
    return () => {
      navigator.serviceWorker?.removeEventListener?.("message", handleMessage);
    };
  }, [queryClient, setHasUnread]);

  // Listen for foreground FCM notifications and show a toast
  const handleForegroundMessage = useCallback(
    (payload: any) => {
      queryClient.refetchQueries({ queryKey: ["notifications"], type: "all" });
      if (!useNotificationStore.getState().isNotificationOpen) {
        setHasUnread(true);
      }
    },
    [queryClient, setHasUnread]
  );

  useFirebaseMessaging(handleForegroundMessage);

  return null;
}

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <SessionInitializer>
      <ThemeProvider
        attribute="class"
        defaultTheme="light"
        enableSystem={false}
        themes={["light"]}
        disableTransitionOnChange
      >
        <QueryProvider>
          <FirebaseNotificationHandler />
          <TourProvider>
            <Toaster />
            {children}
            <OutOfCreditsModal />
          </TourProvider>
        </QueryProvider>
      </ThemeProvider>
    </SessionInitializer>
  );
}
