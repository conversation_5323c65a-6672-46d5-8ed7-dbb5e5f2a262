"use client";

import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from "react";
import { TourConfig, TourCallbacks } from "@/shared/interfaces";
import Tour from "@/components/shared/Tour";

interface TourContextType {
  isActive: boolean;
  startTour: (config: TourConfig, callbacks?: TourCallbacks) => void;
  endTour: () => void;
  updateTourConfig: (config: TourConfig) => void;
}

const TourContext = createContext<TourContextType | undefined>(undefined);

interface TourProviderProps {
  children: ReactNode;
}

export const TourProvider: React.FC<TourProviderProps> = ({ children }) => {
  const [isActive, setIsActive] = useState(false);
  const [currentConfig, setCurrentConfig] = useState<TourConfig | null>(null);
  const [currentCallbacks, setCurrentCallbacks] = useState<
    TourCallbacks | undefined
  >();

  const startTour = useCallback(
    (config: TourConfig, callbacks?: TourCallbacks) => {
      setCurrentConfig(config);
      setCurrentCallbacks(callbacks);
      setIsActive(true);
    },
    []
  );

  const endTour = useCallback(() => {
    setIsActive(false);
    setCurrentConfig(null);
    setCurrentCallbacks(undefined);
  }, []);

  const updateTourConfig = useCallback((config: TourConfig) => {
    setCurrentConfig(config);
  }, []);

  const handleTourClose = useCallback(() => {
    endTour();
  }, [endTour]);

  return (
    <TourContext.Provider
      value={{
        isActive,
        startTour,
        endTour,
        updateTourConfig,
      }}
    >
      {children}
      {currentConfig && (
        <Tour
          config={currentConfig}
          callbacks={currentCallbacks}
          isActive={isActive}
          onClose={handleTourClose}
        />
      )}
    </TourContext.Provider>
  );
};

export const useTourContext = () => {
  const context = useContext(TourContext);
  if (context === undefined) {
    throw new Error("useTourContext must be used within a TourProvider");
  }
  return context;
};
