import {
  EmployeeDepartment,
  EmployeeTone,
  EmployeeVisibility,
  ModelProvider,
} from "@/shared/enums";
import { z } from "zod";

export const createEmployeeProfileSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(20, "Name must be less than 20 characters"),
  // agent_topic_type: z
  //   .string()
  //   .min(1, "Role is required")
  //   .max(20, "Role must be less than 20 characters"),
  agent_topic_type: z.string().optional(),
  description: z
    .string()
    .min(10, "Description must be at least 10 characters")
    .max(500, "Description must be less than 500 characters"),
  avatar: z.string(),
  // department: z.nativeEnum(EmployeeDepartment),
  department: z.string(),
  category: z.nativeEnum(EmployeeDepartment),
  ruh_credentials: z.boolean(),
  visibility: z.nativeEnum(EmployeeVisibility),
  // model_provider: z.nativeEnum(ModelProvider).optional(),
  model_provider: z.string().optional(),
  model_name: z.string().optional(),
  system_message: z
    .string()
    .min(10, "Instructions must be at least 10 characters")
    .max(10000, "Instructions must be less than 10000 characters"),
  tone: z.nativeEnum(EmployeeTone),
  files: z
    .array(
      z.object({
        file: z.string(),
        name: z.string().optional(),
        size: z.number().optional(),
        created_at: z.string().optional(),
      })
    )
    .optional(),
  urls: z.array(z.string().url("Invalid URL format")).optional(),
  mcp_server_ids: z.array(z.string()).optional(),
  workflow_ids: z.array(z.string()).optional(),
  agent_capabilities: z.object({
    capabilities: z
      .array(
        z.object({
          title: z.string(),
          description: z.string(),
        })
      )
      .optional(),
  }),
  variables: z
    .array(
      z.object({
        name: z.string(),
        description: z.string(),
        type: z.string(),
        default_value: z.string(),
      })
    )
    .optional(),
  temperature: z.number().optional(),
  max_tokens: z.number().optional(),
});

export type CreateEmployeeProfileSchema = z.infer<
  typeof createEmployeeProfileSchema
>;
