import { EmployeeTone } from "@/shared/enums";
import {
  ModelProvider,
  OpenAIModelName,
  AnthropicModelName,
  MetaModelName,
} from "@/shared/enums";
import { z } from "zod";

// Base schema for employee profile validation
const baseEmployeeProfileSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Name must be at least 2 characters" })
    .max(20, { message: "Name must not exceed 20 characters" }),

  agent_topic_type: z
    .string()
    .min(2, { message: "Role must be at least 2 characters" })
    .max(20, { message: "Role must not exceed 20 characters" }),

  description: z
    .string()
    .min(10, { message: "Description must be at least 10 characters" })
    .max(500, { message: "Description must not exceed 500 characters" }),

  avatar: z.string().optional(),
});

// Combined model name schema
const modelNameSchema = z.union([
  z.nativeEnum(OpenAIModelName),
  z.nativeEnum(AnthropicModelName),
  z.nativeEnum(MetaModelName),
]);

// Schema for when using Ruh AI configuration
const ruhConfigSchema = baseEmployeeProfileSchema.extend({
  model_provider: z.nativeEnum(ModelProvider).optional(),
  model_name: modelNameSchema.optional(),
});

// Schema for when using custom configuration
const customConfigSchema = baseEmployeeProfileSchema.extend({
  model_provider: z.nativeEnum(ModelProvider, {
    errorMap: () => ({ message: "Please select a valid provider" }),
  }),
  model_name: modelNameSchema.refine((val) => val !== undefined, {
    message: "Please select a valid model",
  }),
});

// Export the schema that handles both cases
export const employeeProfileSchema = z.union([
  ruhConfigSchema,
  customConfigSchema,
]);

// Schema for employee guideline validation
export const employeeGuidelineSchema = z.object({
  system_message: z
    .string()
    .min(10, { message: "Instructions are required" })
    .max(1500, { message: "Instructions must not exceed 1500 characters" }),

  tone: z
    .nativeEnum(EmployeeTone, {
      errorMap: () => ({ message: "Please select a valid tone" }),
    })
    .optional(),
});

export type EmployeeProfileFormData = z.infer<typeof employeeProfileSchema>;
export type EmployeeGuidelineFormData = z.infer<typeof employeeGuidelineSchema>;
