import mixpanel from "mixpanel-browser";

const MIXPANEL_TOKEN = "8c5a5cb474aa4921c6f588f5edb381fb";

if (MIXPANEL_TOKEN) {
  mixpanel.init(MIXPANEL_TOKEN, {
    debug: process.env.NODE_ENV === "development",
    track_pageview: false,
    record_mask_text_selector: "", // Remove default text masking
    record_block_selector: "", // Remove default element blocking
    record_sessions_percent: 15, // Session Replay enabled, recording 15% of all sessions
    record_idle_timeout_ms: 10 * 60 * 1000, // 10 minutes
    record_heatmap_data: true, // Enable heatmap data collection
  } as any);
}

export const Mixpanel = {
  identify: (id: string) => {
    mixpanel.identify(id);
  },
  alias: (id: string) => {
    mixpanel.alias(id);
  },
  track: (name: string, props: Record<string, any>) => {
    mixpanel.track(name, props);
  },
  people: {
    set: (props: Record<string, any>) => {
      mixpanel.people.set(props);
    },
  },
};
