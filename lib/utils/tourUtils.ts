import React from "react";
import { TourConfig, TourStep } from "@/shared/interfaces";

/**
 * Default tour configuration that can be used as a base for all modules
 */
export const defaultTourConfig: Partial<TourConfig> = {
  showProgress: true,
  showSkipButton: true,
  continuous: true,
  disableOverlay: false,
  disableScrolling: false,
  hideBackButton: false,
  hideCloseButton: false,
  scrollToFirstStep: true,
  spotlightClicks: false,
  spotlightPadding: 12, // Increased padding to match Figma
  styles: {
    options: {
      primaryColor: "#ae00d0", // Figma primary color
      backgroundColor: "#ae00d0", // Purple background for tooltip
      textColor: "#ffffff", // White text on purple background
      overlayColor: "rgba(0, 0, 0, 0.4)", // Softer overlay
      spotlightShadow: "0 0 20px rgba(174, 0, 208, 0.3)", // Softer purple glow
      zIndex: 9999,
    },
  },
};

/**
 * Creates a tour configuration with default settings
 */
export const createTourConfig = (
  steps: TourStep[],
  overrides?: Partial<TourConfig>
): TourConfig => {
  return {
    ...defaultTourConfig,
    ...overrides,
    steps,
  } as TourConfig;
};

/**
 * Creates a tour step with default settings
 */
export const createTourStep = (
  id: string,
  target: string,
  title: string,
  content: string | React.ReactNode,
  overrides?: Partial<TourStep>
): TourStep => {
  return {
    id,
    target,
    title,
    content,
    placement: "bottom",
    showSkip: true,
    showPrevious: true,
    showNext: true,
    spotlightPadding: 8,
    ...overrides,
  };
};

/**
 * Checks if a tour has been completed for a specific module
 */
export const isTourCompleted = (moduleKey: string): boolean => {
  if (typeof window === "undefined") return true;
  return localStorage.getItem(moduleKey) === "true";
};

/**
 * Marks a tour as completed for a specific module
 */
export const markTourCompleted = (moduleKey: string): void => {
  if (typeof window === "undefined") return;
  localStorage.setItem(moduleKey, "true");
};
