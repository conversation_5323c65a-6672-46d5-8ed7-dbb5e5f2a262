import {
  Mode,
  SenderType,
  StreamMessageSource,
  MessageType,
} from "@/shared/enums";
import {
  EmployeeChatMessage,
  DelegationChatMessage,
} from "@/hooks/useEmployeeManagementStore";
import { WorkflowInDB, Employee } from "@/shared/interfaces";

// Utility to filter and transform messages for chat components
export async function filterAndTransformMessages(
  rawMessages: any[],
  workflows: WorkflowInDB[] = []
): Promise<EmployeeChatMessage[]> {
  return rawMessages
    .filter((msg) => {
      // Allow workflow messages through
      if (msg.type === MessageType.WORKFLOW) {
        return true;
      }

      if (msg.senderType === SenderType.ASSISTANT) {
        return (
          msg?.data?.mode === Mode.RESPONSE ||
          msg?.data?.mode === Mode.SEARCHING ||
          msg?.data?.mode === "delegated"
        );
      }
      return true;
    })
    .map((msg) => {
      // Handle workflow messages
      if (msg.type === MessageType.WORKFLOW) {
        // Find the matching workflow to get display names
        const matchingWorkflow = workflows.find(
          (workflow) => workflow.id === msg.workflowId
        );

        // Transform workflow response to include display names
        const transformedAvailableNodes =
          msg.workflowResponse?.map((step: any) => {
            // Find matching node in workflow's available_nodes by node_id or transition_id
            const matchingNode = matchingWorkflow?.available_nodes?.find(
              (node: any) => node.transition_id === step.transition_id
            );

            return {
              ...step,
              display_name: matchingNode?.display_name,
              label: matchingNode?.label,
            };
          }) || [];

        return {
          id: msg.id,
          content: "", // Workflow messages don't have text content
          attachments: [],
          senderType: msg.senderType,
          timestamp: new Date(msg.createdAt),
          type: "workflow" as const,
          isWorkflowEvent: true,
          workflowData: {
            workflowId: msg.workflowId,
            workflowResponse: msg.workflowResponse,
            status: msg.status,
            selectedWorkflowId: msg.workflowId,
            // Transform workflow steps for rendering with display names
            available_nodes: transformedAvailableNodes,
            stepStatus: msg.workflowResponse?.reduce((acc: any, step: any) => {
              if (step.transition_id) {
                acc[step.transition_id] = step.status;
              }
              return acc;
            }, {}),
            approvalRequired: false,
            approved: false,
          },
        };
      }

      // Handle delegated messages
      if (msg.data?.mode === "delegated" && msg.data?.agent_data) {
        const employee = {
          id: msg.data.agent_data.id,
          name: msg.data.agent_data.name,
          description: msg.data.agent_data.description,
          avatar: msg.data.agent_data.avatar,
          designation: msg.data.agent_data.department || "AI Assistant",
          assigned_task: msg.data.query,
        } as Employee;

        return {
          id: msg.id,
          content: "", // Delegation messages don't have text content
          attachments: [],
          senderType: msg.senderType,
          timestamp: new Date(msg.createdAt),
          type: "delegation" as const,
          delegationData: {
            employee,
            isApproved: true, // Already delegated from history
            isApproveLoading: false,
            isTaskStarted: true, // Task has been started
            taskData: {
              task_id: msg.id, // Use message ID as task ID
              agent_id: msg.data.agent_data.id,
              conversation_id: msg.data.agent_conversation_id,
              agent_session_id: msg.data.agent_conversation_id, // Using same as conversation_id
              title: msg.data.query,
            },
          },
        };
      }

      // Special handling for WebSearchEmployee searching mode
      if (
        msg.data?.mode === Mode.SEARCHING &&
        msg.data?.source === StreamMessageSource.WEB_SEARCH_EMPLOYEE &&
        typeof msg.data?.message === "string"
      ) {
        let parsed: any = {};
        try {
          parsed = JSON.parse(msg.data.message);
        } catch {
          parsed = {};
        }
        return {
          id: msg.id,
          content: "",
          attachments: [],
          senderType: msg.senderType,
          timestamp: new Date(msg.createdAt),
          searchSources: parsed.sources || [],
          employeeSearchType: msg.data.source,
          isSearchMessage: true,
        };
      }
      // Default transformation
      return {
        id: msg.id,
        content: msg.data?.message ?? "",
        attachments: msg.data?.attachments ?? [],
        senderType: msg.senderType,
        timestamp: new Date(msg.createdAt),
      };
    })
    .reverse();
}
