import { createTourConfig, createTourStep } from "@/lib/utils/tourUtils";
import { NavigationTourGuideText } from "../../app/(platform)/chat/_components/NavigationTourGuideText";
import React from "react";

export const globalAgentTourConfig = createTourConfig([
  createTourStep(
    "chat-input",
    ".tour-chat-input",
    "Chat Input",
    "Enter your message here to assign tasks, give instructions to the agent, or communicate any relevant details clearly.",
    {
      placement: "bottom",
      spotlightPadding: { top: 8, right: 12, bottom: 8, left: 12 },
      tooltipOffset: { x: 0, y: -50 },
    }
  ),
  createTourStep(
    "task-search-dropdown",
    ".tour-task-search-dropdown",
    "Task & Search Mode",
    `Use this dropdown to switch between <strong>Task Mode</strong> to get things done or <strong>Search Mode</strong> to find detailed answers for your queries`,
    {
      placement: "right",
      spotlightPadding: 0,
      tooltipOffset: { x: 0, y: 10 },
    }
  ),
  createTourStep(
    "left-bar-items",
    ".navbar-items",
    "Left Bar Items",
    React.createElement(NavigationTourGuideText),
    {
      placement: "right",
      spotlightPadding: 6,
      tooltipOffset: { x: 0, y: -40 },
    }
  ),
  createTourStep(
    "Profile",
    ".profile-dropdown",
    "Profile",
    "Finally, click here to open your profile settings and update your personal information as needed.",
    {
      placement: "right-bottom",
      spotlightPadding: 10,
      tooltipOffset: { x: 0, y: 30 },
    }
  ),
]);

export const employeeTourConfig = createTourConfig([
  createTourStep(
    "employee-sidebar-view-all-employees",
    ".tour-employee-sidebar-view-all-employees",
    "View All Employees",
    `Click "View All Employees" to browse all your AI-powered workers. Access "Employee Bench" to see unpublished AI employees.`,
    {
      placement: "left-center",
      spotlightPadding: { top: 15, right: 10, bottom: 15, left: 12 },
      tooltipOffset: { x: -10, y: 0 },
    }
  ),
  createTourStep(
    "employee-sidebar-add-ai-employee",
    ".tour-employee-sidebar-add-ai-employee",
    "Add AI Employee",
    "To add a new AI employee, visit the Marketplace or configure one via the Developer Portal.",
    {
      placement: "left-center",
      spotlightPadding: { top: 15, right: 10, bottom: 15, left: 12 },
      tooltipOffset: { x: -10, y: 0 },
    }
  ),
  createTourStep(
    "employee-sidebar-create-employee-button",
    ".tour-employee-sidebar-create-employee-button",
    "Create Employee",
    "Ready to create an AI employee yourself? Click on this button and get started!",
    {
      placement: "right-top",
      spotlightPadding: 10,
      tooltipOffset: { x: 0, y: 0 },
    }
  ),
]);

export const adminSettingsTourConfig = createTourConfig([
  createTourStep(
    "admin-settings-departments",
    ".tour-admin-settings-departments",
    "Departments",
    `Manage your organizational departments—create, edit, or organize them as needed.`,
    {
      placement: "left-top",
      spotlightPadding: 0,
      tooltipOffset: { x: 0, y: -20 },
    }
  ),
  createTourStep(
    "admin-settings-members",
    ".tour-admin-settings-members",
    "Members",
    `Invite team members to collaborate and expand your workspace.`,
    {
      placement: "left-top",
      spotlightPadding: 0,
      tooltipOffset: { x: 0, y: -20 },
    }
  ),
  createTourStep(
    "admin-settings-knowledge",
    ".tour-admin-settings-knowledge",
    "Knowledge",
    `Add and update organizational context for platform-wide knowledge sharing.`,
    {
      placement: "left-top",
      spotlightPadding: 0,
      tooltipOffset: { x: 0, y: -20 },
    }
  ),
  createTourStep(
    "admin-settings-workspace-settings",
    ".tour-admin-settings-workspace-settings",
    "Workspace Settings",
    `Adjust essential workspace settings to customize your environment.`,
    {
      placement: "left-top",
      spotlightPadding: 0,
      tooltipOffset: { x: 0, y: -20 },
    }
  ),
  createTourStep(
    "admin-settings-token-usage",
    ".tour-admin-settings-token-usage",
    "Token Usage",
    `View and manage your token usage to optimize your workspace.`,
    {
      placement: "left-top",
      spotlightPadding: 0,
      tooltipOffset: { x: 0, y: -20 },
    }
  ),
  createTourStep(
    "admin-settings-payment-history",
    ".tour-admin-settings-payment-history",
    "Payment History",
    `View and manage your payment history to optimize your workspace.`,
    {
      placement: "left-top",
      spotlightPadding: 0,
      tooltipOffset: { x: 0, y: -20 },
    }
  ),
]);

export const userSettingsTourConfig = createTourConfig([
  createTourStep(
    "user-settings-profile",
    ".tour-user-settings-profile",
    "Profile",
    "Update your personal details, including name, phone number, and more.",
    {
      placement: "left-top",
      spotlightPadding: 0,
      tooltipOffset: { x: 0, y: -20 },
    }
  ),
  createTourStep(
    "user-settings-password",
    ".tour-user-settings-password",
    "Password",
    "Strengthen your account security by setting a strong, unique password.",
    {
      placement: "left-top",
      spotlightPadding: 0,
      tooltipOffset: { x: 0, y: -20 },
    }
  ),
  createTourStep(
    "user-settings-preferences",
    ".tour-user-settings-preferences",
    "Preferences",
    "Customize your model preferences and manage platform integrations to suit your workflow.",
    {
      placement: "left-top",
      spotlightPadding: 0,
      tooltipOffset: { x: 0, y: -20 },
    }
  ),
]);

export const createEmployeeTourConfig = createTourConfig([
  createTourStep(
    "create-employee-profile",
    ".tour-create-employee-profile",
    "Create Employee",
    "Navigate through these sections to customize settings and create the best AI employee for your work.",
    {
      placement: "left-center",
      spotlightPadding: { top: 17, right: 12, bottom: 15, left: 12 },
      tooltipOffset: { x: 0, y: 0 },
    }
  ),
]);
