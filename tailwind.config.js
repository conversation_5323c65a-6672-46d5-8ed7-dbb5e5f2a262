/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}", // adjust this to match your project structure
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      // You can extend custom widths if you prefer predefined values too
    },
  },
  safelist: [
    "md:w-[calc(100vw-295px)]",
    "md:w-[calc(100vw-300px)]",
    "md:w-[calc(100vw-320px)]",
    "md:w-[calc(100vw-367px)]",
    "w-[calc(100vw-295px)]",
    "w-[calc(100vw-300px)]",
    "w-[calc(100vw-320px)]",
    "w-[calc(100vw-367px)]",
  ],
  plugins: [],
};
