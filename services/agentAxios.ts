import axios, { AxiosError, AxiosRequestConfig } from "axios";
import {
  clearAuthCookies,
  getAccessToken,
  getRefreshToken,
} from "./authCookies";
import { clearUserStore, clearUserStoreAndLogout, getAuthApi } from "./helper";
import { getAccessTokenRoute } from "@/shared/routes";

// Create a separate axios instance for SSE-related API calls
const sseBaseURL = process.env.NEXT_PUBLIC_AGENT_URL;

const agent_api = axios.create({
  baseURL: sseBaseURL,
});

// Request interceptor to add authorization header for SSE API
agent_api.interceptors.request.use(
  async (config) => {
    const token = await getAccessToken();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    config.headers["ngrok-skip-browser-warning"] = "true";
    config.headers["accept"] = "application/json";
    config.headers["Content-Type"] = "application/json";
    return config;
  },
  (error: any) => {
    return Promise.reject(
      new Error(
        `SSE API request interceptor error: ${error.message || "Unknown error"}`
      )
    );
  }
);

agent_api.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & {
      _retry?: boolean;
    };

    if (originalRequest.url?.includes(getAccessTokenRoute)) {
      await clearUserStoreAndLogout();
      return Promise.reject(new Error("Invalid or expired refresh token."));
    }

    // Check if the error is due to an expired token (401 Unauthorized or 403 Forbidden)
    if (
      (error.response?.status === 401 || error.response?.status === 403) &&
      !originalRequest._retry
    ) {
      // Mark this request as retried to prevent infinite loops
      originalRequest._retry = true;

      try {
        // Get the refresh token
        const refreshToken = await getRefreshToken();

        if (!refreshToken) {
          await clearAuthCookies();
          clearUserStore();

          return Promise.reject(new Error("No refresh token available"));
        }

        // Get the auth API and generate a new access token
        const authApi = await getAuthApi();
        try {
          const tokenResponse = await authApi.generateAccessToken(refreshToken);

          if (tokenResponse.success && tokenResponse.access_token) {
            // Update the authorization header with the new token
            originalRequest.headers = {
              ...originalRequest.headers,
              Authorization: `Bearer ${tokenResponse.access_token}`,
            };

            // Retry the original request with the new token
            return agent_api(originalRequest);
          }
        } catch (tokenError: any) {
          console.error("Token refresh failed:", tokenError);
          await clearAuthCookies();
          clearUserStore();
          return Promise.reject(tokenError);
        }

        await clearAuthCookies();
        clearUserStore();

        return Promise.reject(new Error("Token refresh failed"));
      } catch (refreshError) {
        // Clear cookies and redirect to login on refresh error
        console.error("Error during refresh token process:", refreshError);
        await clearAuthCookies();
        clearUserStore();
        return Promise.reject(refreshError);
      }
    }

    // For other errors, just reject the promise
    return Promise.reject(error);
  }
);

export default agent_api;
