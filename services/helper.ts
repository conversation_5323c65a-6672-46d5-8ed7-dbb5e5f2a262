import { useUserStore } from "@/hooks/use-user";
import { PaginationMetadata } from "@/shared/interfaces";
import { clearAuthCookies } from "./authCookies";
import { loginRoute } from "@/shared/routes";

export function sanitizeString(text: string): string {
  if (!text) {
    return "";
  }

  // Replace underscores and hyphens with spaces
  let formattedText = text.replace(/[_-]/g, " ");

  // Convert to lowercase and then capitalize the first letter of each word
  formattedText = formattedText
    .toLowerCase()
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");

  return formattedText;
}

/**
 * Generates an array of pagination items based on the current page and total pages
 * @param metadata - The pagination metadata containing currentPage, totalPages, etc.
 * @returns An array of numbers and strings, where numbers represent page numbers and strings represent ellipsis
 */
export function generatePaginationItems(
  metadata: PaginationMetadata | null
): (number | string)[] {
  if (!metadata) return [];

  const items = [];
  const { currentPage, totalPages } = metadata;

  // Always show first page
  if (totalPages > 0) {
    items.push(1);
  }

  // Add ellipsis if there's a gap
  if (currentPage > 3) {
    items.push("ellipsis-start");
  }

  // Add pages around current page
  for (
    let i = Math.max(2, currentPage - 1);
    i <= Math.min(totalPages - 1, currentPage + 1);
    i++
  ) {
    if (!items.includes(i)) {
      items.push(i);
    }
  }

  // Add ellipsis if there's a gap
  if (currentPage < totalPages - 2) {
    items.push("ellipsis-end");
  }

  // Always show last page if it's different from first
  if (totalPages > 1 && !items.includes(totalPages)) {
    items.push(totalPages);
  }

  return items;
}

/**
 * Creates a streaming text effect by breaking content into chunks
 * @param content - The full text content to stream
 * @param chunkSize - Number of characters per chunk (default: 3)
 * @param interval - Milliseconds between chunks (default: 50)
 * @returns An object with methods to control the streaming
 */
export function createTextStreamer(
  content: string,
  chunkSize: number = 3,
  interval: number = 50
) {
  let currentIndex = 0;
  let streamingText = "";
  let isStreaming = false;
  let streamInterval: NodeJS.Timeout | null = null;

  const callbacks: {
    onUpdate?: (text: string) => void;
    onComplete?: () => void;
    onStop?: () => void;
  } = {};

  const start = (
    onUpdate?: (text: string) => void,
    onComplete?: () => void
  ) => {
    if (isStreaming) return;

    callbacks.onUpdate = onUpdate;
    callbacks.onComplete = onComplete;
    isStreaming = true;
    currentIndex = 0;
    streamingText = "";

    streamInterval = setInterval(() => {
      if (currentIndex >= content.length) {
        stop();
        callbacks.onComplete?.();
        return;
      }

      const chunk = content.slice(currentIndex, currentIndex + chunkSize);
      streamingText += chunk;
      currentIndex += chunkSize;

      callbacks.onUpdate?.(streamingText);
    }, interval);
  };

  const stop = () => {
    if (streamInterval) {
      clearInterval(streamInterval);
      streamInterval = null;
    }
    isStreaming = false;
    callbacks.onStop?.();
  };

  const reset = (newContent?: string) => {
    stop();
    if (newContent !== undefined) {
      content = newContent;
    }
    currentIndex = 0;
    streamingText = "";
  };

  const getCurrentText = () => streamingText;
  const getIsStreaming = () => isStreaming;
  const getProgress = () =>
    content.length > 0 ? currentIndex / content.length : 0;

  return {
    start,
    stop,
    reset,
    getCurrentText,
    getIsStreaming,
    getProgress,
  };
}

// Helper function to clear user store
export const clearUserStoreAndLogout = async () => {
  useUserStore.getState().clearUser();
  await clearAuthCookies();
  // Redirect to login page
  // Ensure this runs only on the client-side
  if (typeof window !== "undefined") {
    // Check if already on login page to prevent redirect loop if login page itself makes an API call
    if (window.location.pathname !== loginRoute) {
      window.location.href = loginRoute;
    }
  }
};

// Helper function to clear user store
export const clearUserStore = () => {
  useUserStore.getState().clearUser();
};

// Import the auth API dynamically to avoid circular dependency
export const getAuthApi = async () => {
  const { authApi } = await import("@/app/api/auth");
  return authApi;
};
