import { NextRequest, NextResponse } from "next/server";
import { clearAuthCookies, setAuthCookies } from "@/services/authCookies";
import api from "@/services/axios";

export async function POST(request: NextRequest) {
  try {
    const { organisation_id } = await request.json();

    if (!organisation_id) {
      return NextResponse.json(
        { error: "Organisation ID is required" },
        { status: 400 }
      );
    }

    // Get current access token from cookies to make the API call
    const accessToken = request.cookies.get("accessToken")?.value;

    if (!accessToken) {
      return NextResponse.json(
        { error: "No access token found" },
        { status: 401 }
      );
    }

    // Call the organization switch API with current token
    const response = await api.post(
      "/auth/organisation-token",
      { organisation_id },
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    );

    const { access_token, refresh_token } = response.data;

    if (access_token && refresh_token) {
      // Clear existing cookies
      await clearAuthCookies();

      // Set new cookies with proper expiration
      const accessTokenAge = 7 * 24 * 60 * 60; // 1 week in seconds
      const refreshTokenAge = 30 * 24 * 60 * 60; // 1 month in seconds

      await setAuthCookies(
        access_token,
        refresh_token,
        accessTokenAge,
        refreshTokenAge
      );
    }

    return NextResponse.json(response.data);
  } catch (error: any) {
    console.error("Error switching organization:", error);
    return NextResponse.json(
      {
        error:
          error.response?.data?.message ||
          error.message ||
          "Failed to switch organization",
      },
      { status: 500 }
    );
  }
}
