import api from "@/services/axios";

export const subscriptionApi = {
  getSubscription: async (organisation_id: string) => {
    try {
      const response = await api.get(
        `/payments/subscription/${organisation_id}`
      );
      return response.data;
    } catch (error: any) {
      return error.response.data;
    }
  },
  getPlans: async () => {
    try {
      const response = await api.get(`/payments/plans`);
      return response.data;
    } catch (error: any) {
      return error.response.data;
    }
  },
  getSubscriptionDetails: async (organisation_id: string) => {
    try {
      const response = await api.get(
        `/payments/subscription/${organisation_id}`
      );
      return response.data;
    } catch (error: any) {
      return error.response.data;
    }
  },
  activateDefaultPlan: async (org_id: string) => {
    try {
      const response = await api.post(
        `/payments/activate-default-plan/${org_id}`
      );
      return response.data;
    } catch (error: any) {
      return error.response.data;
    }
  },
  createCheckoutSession: async (payload: any) => {
    try {
      const response = await api.post(`/payments/checkout`, payload);
      return response.data;
    } catch (error: any) {
      return error.response.data;
    }
  },

  listTopupPlans: async () => {
    try {
      const response = await api.get(`/payments/topup-plans`);
      return response.data;
    } catch (error: any) {
      return error.response.data;
    }
  },
  createTopupCheckoutSession: async (payload: any) => {
    try {
      const response = await api.post(`/payments/topup/checkout`, payload);
      return response.data;
    } catch (error: any) {
      return error.response.data;
    }
  },
  getAggregatedTokenUsageLogs: async (organisation_id: string) => {
    try {
      const response = await api.get(
        `/payments/token-usage/aggregated/${organisation_id}`
      );
      return response.data;
    } catch (error: any) {
      return error.response.data;
    }
  },
  getTokenUsageLogs: async (
    organisation_id: string,
    user_id?: string,
    month?: string,
    year?: string
  ) => {
    try {
      let url = `/payments/token-usage/${organisation_id}`;
      const params = new URLSearchParams();

      if (user_id) {
        params.append("user_id", user_id);
      }
      if (month && month !== "all") {
        params.append("month", month);
      }
      if (year && year !== "all") {
        params.append("year", year);
      }

      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await api.get(url);
      return response.data;
    } catch (error: any) {
      return error.response.data;
    }
  },
  getPaymentHistory: async (organisation_id: string) => {
    try {
      const response = await api.get(
        `/payments/transactions/${organisation_id}`
      );
      return response.data;
    } catch (error: any) {
      return error.response.data;
    }
  },
};
