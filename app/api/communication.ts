import agent_api from "@/services/agentAxios";
import {
  ConversationCreate,
  ConversationResponse,
  ConversationList,
  GetConversationsParams,
  MessageList,
  GetMessagesParams,
  ChannelType,
  ChatType,
} from "@/shared/interfaces";

import { AIChatMode, SearchModeResources, TaskStatus } from "@/shared/enums";
import api from "@/services/axios";
import { subscriptionApi } from "./subscription";
import { useOrgStore } from "@/hooks/use-organization";

export const communicationApi = {
  /**
   * Check if user has sufficient credits before sending a message
   * @returns Promise resolving to true if user has credits, false otherwise
   */
  checkCreditsBeforeSending: async (): Promise<boolean> => {
    try {
      // Get organization details from store
      const { currentOrganization } = useOrgStore.getState();
      const org = currentOrganization;

      if (!org || !org.id) {
        console.error("No organization found in store");
        return false;
      }

      // Get subscription details
      const subscriptionResponse = await subscriptionApi.getSubscriptionDetails(
        org.id
      );

      if (!subscriptionResponse.success || !subscriptionResponse.subscription) {
        console.error("No subscription found");
        return false;
      }

      const { current_credits } = subscriptionResponse.subscription;

      // Check if user has sufficient credits (more than 0)
      return current_credits > 0;
    } catch (error: any) {
      console.error("Error checking credits:", error);
      // In case of error, allow the message to be sent to avoid blocking users
      return true;
    }
  },

  /**
   * Create a new conversation
   * @param agentId The ID of the agent to create conversation with
   * @returns Promise resolving to the conversation ID
   */
  createConversation: async (agentId?: string): Promise<string> => {
    try {
      const payload: ConversationCreate = {
        chatType: agentId ? ChatType.AGENT : ChatType.GLOBAL,
      };
      if (agentId) {
        payload.agentId = agentId;
      }
      const response = await agent_api.post<ConversationResponse>(
        "/communication/conversation",
        payload
      );

      return response.data.id;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to create conversation"
      );
    }
  },

  /**
   * Get conversation details by ID
   * @param conversationId The ID of the conversation to retrieve
   * @returns Promise resolving to the conversation details
   */
  getConversation: async (
    conversationId: string
  ): Promise<ConversationResponse> => {
    try {
      const response = await agent_api.get<ConversationResponse>(
        `/communication/conversation/${conversationId}`
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get conversation"
      );
    }
  },

  /**
   * Delete a conversation by ID
   * @param conversationId The ID of the conversation to delete
   * @returns Promise resolving when the conversation is deleted
   */
  deleteConversation: async (conversationId: string): Promise<void> => {
    try {
      await agent_api.delete(`/communication/conversation/${conversationId}`);
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to delete conversation"
      );
    }
  },

  /**
   * Get paginated list of conversations
   * @param params Parameters for filtering and pagination
   * @returns Promise resolving to the paginated list of conversations
   */
  getConversations: async (
    params: GetConversationsParams
  ): Promise<ConversationList> => {
    try {
      const { agentId, page = 1, limit = 10, chatType, search } = params;

      // Build params object conditionally - only include chatType if explicitly provided
      const requestParams: any = {
        agentId,
        page,
        limit,
      };

      // Only include chatType if it's explicitly provided (not undefined)
      if (chatType !== undefined) {
        requestParams.chatType = chatType;
      }

      // Only include search if it's provided and not empty
      if (search && search.trim()) {
        requestParams.search = search.trim();
      }

      const response = await agent_api.get<ConversationList>(
        "/communication/conversations",
        {
          params: requestParams,
        }
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get conversations"
      );
    }
  },

  /**
   * Get paginated list of messages for a conversation
   * @param params Parameters for filtering and pagination
   * @returns Promise resolving to the paginated list of messages
   */
  getMessages: async (params: GetMessagesParams): Promise<MessageList> => {
    try {
      const { conversationId, page = 1, limit = 10, index } = params;

      // Only include index if it is defined (not undefined/null)
      const requestParams: Record<string, any> = {
        page,
        limit,
      };
      if (typeof index !== "undefined") {
        requestParams.index = index;
      }

      const response = await agent_api.get<MessageList>(
        `/communication/messages/${conversationId}`,
        {
          params: requestParams,
        }
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get messages"
      );
    }
  },

  /**
   * Create a new session for an existing conversation
   * @param conversationId The ID of the conversation to create session for
   * @param use_orchestration_team Whether to use orchestration team
   * @param use_knowledge Whether to use knowledge base
   * @returns Promise resolving to the session ID
   */
  createSession: async (
    conversationId: string,
    use_orchestration_team?: boolean,
    use_knowledge?: boolean,
    agentId?: string
  ): Promise<string> => {
    try {
      const payload: any = {
        conversation_id: conversationId,
      };

      if (use_orchestration_team !== undefined) {
        payload.use_orchestration_team = use_orchestration_team;
      }

      if (use_knowledge !== undefined) {
        payload.use_knowledge = use_knowledge;
      }

      if (agentId !== undefined) {
        payload.agent_id = agentId;
      }

      const response = await agent_api.post<{ session_id: string }>(
        "agents/sessions",
        payload
      );

      return response.data.session_id;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to create session"
      );
    }
  },

  sendMessage: async (
    message: string,
    session_id: string,
    attachments: any[],
    mode?: AIChatMode,
    tools?: any[],
    resource?: SearchModeResources
  ): Promise<string> => {
    try {
      const response = await agent_api.post(
        `agents/sessions/${session_id}/chat/quick`,
        {
          message,
          attachments,
          mode,
          tools,
          resource,
        }
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to send message"
      );
    }
  },

  stopStream: async (session_id: string): Promise<void> => {
    try {
      await agent_api.post(`/agents/sessions/${session_id}/chat/stop-stream`);
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to stop stream"
      );
    }
  },

  /**
   * Fetch quick tools (MCPs) by IDs
   * @param ids Array of MCP IDs
   * @returns Promise resolving to the MCPs array
   */
  fetchQuickTools: async (ids: string[]): Promise<any[]> => {
    try {
      const response = await api.post("mcps/by-ids", { ids });
      if (response.data && response.data.success) {
        return response.data.mcps;
      }
      return [];
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to fetch quick tools"
      );
    }
  },

  getAgentTasks: async (params: {
    agentId: string;
    search?: string;
    page?: number;
    limit?: number;
    taskStatus?: TaskStatus;
  }): Promise<{
    data: any[];
    metadata: {
      total: number;
      totalPages: number;
      currentPage: number;
      pageSize: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  }> => {
    try {
      const response = await agent_api.get("communication/tasks", {
        params,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to fetch agent tasks"
      );
    }
  },

  getAgentConversations: async (params: {
    agentId: string;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{
    data: any[];
    metadata: {
      total: number;
      totalPages: number;
      currentPage: number;
      pageSize: number;
      hasNextPage: boolean;
      hasPreviousPage: boolean;
    };
  }> => {
    try {
      const response = await agent_api.get("communication/conversations", {
        params,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to fetch agent conversations"
      );
    }
  },

  deleteTask: async (taskId: string): Promise<void> => {
    try {
      await agent_api.delete(`/communication/task/${taskId}`);
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to delete task"
      );
    }
  },

  /**
   * Delegate a task to an agent (global chat)
   * @param params { title, globalChatConversationId, agentId }
   * @returns Promise resolving to the delegate response
   */
  delegateTask: async (params: {
    title: string;
    globalChatConversationId: string;
    agentId: string;
    globalSessionId?: string;
  }): Promise<any> => {
    try {
      const response = await agent_api.post(
        "/communication/task/delegate",
        params
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to delegate task"
      );
    }
  },

  /**
   * Directly execute a workflow (bypassing chat/session flow)
   * @param params Object containing sessionId, workflowId, correlationId, message, conversationId
   * @returns Promise resolving to the API response
   */
  executeDirectWorkflow: async (params: {
    sessionId: string;
    workflowId: string;
    correlationId: string;
    message: string;
    conversationId: string;
  }): Promise<any> => {
    try {
      const response = await agent_api.post(
        "/communication/direct-workflow-execution",
        params
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to execute direct workflow"
      );
    }
  },
};
