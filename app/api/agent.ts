import api from "@/services/axios";
import {
  AgentDetailsResponse,
  AgentUpdateRequest,
  AgentUpdateResponse,
  UserAgentsResponse,
  AgentCreateRequest,
  AgentCreateResponse,
  AgentSettingsUpdatePayload,
  AgentPartUpdateResponseAPI,
  AgentCoreDetailsUpdatePayload,
  AgentKnowledgeUpdatePayload,
  AgentToolsUpdatePayload,
  AgentWorkflowsUpdatePayload,
  ListAgentAvatarsResponse,
  AgentCapabilitiesPayload,
  AgentVariablesPayload,
} from "@/shared/interfaces";

/**
 * Agent API functions for interacting with AI agents/employees
 */
export const agentApi = {
  /**
   * Get all agents/employees for the current user with pagination
   * @param page The page number for pagination (default: 1)
   * @param pageSize The number of agents per page (default: 10)
   * @returns Promise resolving to the paginated list of agents
   */
  getAgents: async (
    page: number = 1,
    pageSize: number = 10,
    isBenchEmployee: boolean = false,
    include_task_counts?: boolean
  ): Promise<UserAgentsResponse> => {
    try {
      // Make the API request to the new endpoint with pagination parameters
      const response = await api.get<UserAgentsResponse>(`/agents`, {
        params: {
          page,
          page_size: pageSize,
          is_bench_employee: isBenchEmployee,
          include_task_counts,
        },
      });

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get agents"
      );
    }
  },

  /**
   * Get details for a specific agent/employee
   * @param agentId The ID of the agent to get details for
   * @returns Promise resolving to the agent details
   */
  getAgentDetails: async (
    agentId: string,
    include_mcps?: boolean,
    include_workflows?: boolean
  ): Promise<AgentDetailsResponse> => {
    try {
      const params = {
        include_mcps,
        include_workflows,
      };
      // Make the API request to the new endpoint
      const response = await api.get<AgentDetailsResponse>(
        `/agents/${agentId}`,
        {
          params,
        }
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get agent details"
      );
    }
  },

  /**
   * Update an agent/employee
   * @param agentId The ID of the agent to update
   * @param updateData The data to update the agent with
   * @returns Promise resolving to the update response
   */
  updateAgent: async (
    agentId: string,
    updateData: AgentUpdateRequest
  ): Promise<AgentUpdateResponse> => {
    try {
      // Make the API request to the new endpoint with PUT method
      const response = await api.put<AgentUpdateResponse>(
        `/agents/${agentId}`,
        updateData
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to update agent"
      );
    }
  },

  /**
   * Create a new agent/employee
   * @param createData The data for creating the new agent
   * @returns Promise resolving to the creation response
   */
  createAgent: async (
    createData: AgentCreateRequest
  ): Promise<AgentCreateResponse> => {
    try {
      // Make the API request to the endpoint with POST method
      // Assuming `/agents` maps to `/api/v1/agents` via axios base URL
      const response = await api.post<AgentCreateResponse>(
        "/agents",
        createData
      );
      return response.data;
    } catch (error: any) {
      // Consistent error handling
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to create agent"
      );
    }
  },

  /**
   * Update agent settings
   * @param agentId The ID of the agent to update settings for
   * @param settings The settings data to update
   * @returns Promise resolving to the update response
   */
  updateAgentSettings: async (
    agentId: string,
    settings: AgentSettingsUpdatePayload
  ): Promise<AgentPartUpdateResponseAPI> => {
    try {
      const response = await api.patch<AgentPartUpdateResponseAPI>(
        `/agents/${agentId}/settings`,
        settings
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to update agent settings"
      );
    }
  },

  /**
   * Update agent profile and guidelines
   * @param agentId The ID of the agent to update core details for
   * @param details The settings data to update
   * @returns Promise resolving to the update response
   */
  updateAgentCoreDetails: async (
    agentId: string,
    details: AgentCoreDetailsUpdatePayload
  ): Promise<AgentPartUpdateResponseAPI> => {
    try {
      const response = await api.patch<AgentPartUpdateResponseAPI>(
        `/agents/${agentId}/core-details`,
        details
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to update agent core details"
      );
    }
  },

  updateAgentCapabilitiesDetails: async (
    agentId: string,
    details: AgentCapabilitiesPayload
  ): Promise<AgentPartUpdateResponseAPI> => {
    try {
      const response = await api.patch<AgentPartUpdateResponseAPI>(
        `/agents/${agentId}/capabilities`,
        details
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to update agent capabilities details"
      );
    }
  },

  updateAgentVariablesDetails: async (
    agentId: string,
    details: AgentVariablesPayload
  ): Promise<AgentPartUpdateResponseAPI> => {
    try {
      const response = await api.patch<AgentPartUpdateResponseAPI>(
        `/agents/${agentId}/variables`,
        details
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to update agent variables details"
      );
    }
  },

  /**
   * Update agent knowledge
   * @param agentId The ID of the agent to update knowledge for
   * @param details The settings data to update
   * @returns Promise resolving to the update response
   */
  updateAgentKnowledge: async (
    agentId: string,
    details: AgentKnowledgeUpdatePayload
  ): Promise<AgentPartUpdateResponseAPI> => {
    try {
      const response = await api.patch<AgentPartUpdateResponseAPI>(
        `/agents/${agentId}/knowledge`,
        details
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to update agent knowledge"
      );
    }
  },

  /**
   * Update agent tools
   * @param agentId The ID of the agent to update tools for
   * @param details The settings data to update
   * @returns Promise resolving to the update response
   */
  updateAgentTools: async (
    agentId: string,
    details: AgentToolsUpdatePayload
  ): Promise<AgentPartUpdateResponseAPI> => {
    try {
      const response = await api.put<AgentPartUpdateResponseAPI>(
        `/agents/${agentId}/mcp-servers`,
        details
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to update agent tools"
      );
    }
  },

  /**
   * Update agent workflows
   * @param agentId The ID of the agent to update workflows for
   * @param details The settings data to update
   * @returns Promise resolving to the update response
   */
  updateAgentWorkflows: async (
    agentId: string,
    details: AgentWorkflowsUpdatePayload
  ): Promise<AgentPartUpdateResponseAPI> => {
    try {
      const response = await api.put<AgentPartUpdateResponseAPI>(
        `/agents/${agentId}/workflows`,
        details
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to update agent workflows"
      );
    }
  },

  /**
   * Toggle agent visibility
   * @param agentId The ID of the agent to toggle visibility for
   * @returns Promise resolving to the update response
   */
  toggleAgentVisibility: async (
    agentId: string
  ): Promise<AgentPartUpdateResponseAPI> => {
    try {
      const response = await api.post<AgentPartUpdateResponseAPI>(
        `/agents/${agentId}/toggle-visibility`
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to toggle agent visibility"
      );
    }
  },

  /**
   * Update agent with combined data (single PATCH endpoint)
   * @param agentId The ID of the agent to update
   * @param payload The changed fields to update
   * @returns Promise resolving to the update response
   */
  updateAgentCombined: async (agentId: string, payload: any): Promise<any> => {
    try {
      const response = await api.patch<any>(
        `/agents/${agentId}/combined`,
        payload
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to update agent (combined)"
      );
    }
  },

  /**
   * List all agent avatars.
   * Fetches all available avatars by using page 1 and a limit of 1000.
   * @returns Promise resolving to a list of avatars with pagination details.
   */
  listAgentAvatars: async (): Promise<ListAgentAvatarsResponse> => {
    try {
      const response = await api.get<ListAgentAvatarsResponse>(
        `/agent-avatars`, // Assuming axios base URL handles /api/v1
        {
          params: {
            page: 1,
            limit: 100,
          },
        }
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to list agent avatars"
      );
    }
  },

  /**
   * Delete an agent/employee
   * @param agentId The ID of the agent to delete
   * @returns Promise resolving to the delete response
   */
  deleteAgent: async (agentId: string): Promise<AgentPartUpdateResponseAPI> => {
    try {
      const response = await api.delete<AgentPartUpdateResponseAPI>(
        `/agents/${agentId}`
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to delete agent"
      );
    }
  },

  //get departments
  getDepartments: async (
    page: number = 1,
    pageSize: number = 60
  ): Promise<any> => {
    try {
      const response = await api.get<any>(`/organisations/getDepartments`, {
        params: {
          page,
          page_size: pageSize,
        },
      });
      return response?.data?.departments;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get departments"
      );
    }
  },
};
