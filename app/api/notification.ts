import api from "@/services/axios";

export const notificationApi = {
  /**
   * Get notifications for the current user with pagination
   * @param page The page number for pagination (default: 1)
   * @param pageSize The number of notifications per page (default: 10)
   * @returns Promise resolving to the paginated list of notifications
   */
  getNotifications: async (
    page: number = 1,
    pageSize: number = 10
  ): Promise<any> => {
    try {
      const response = await api.get<any>("/notifications", {
        params: {
          page,
          page_size: pageSize,
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get notifications"
      );
    }
  },

  /**
   * Mark a notification as seen
   */
  markAsSeen: async (notificationId: string): Promise<any> => {
    try {
      const response = await api.post<any>(
        `/notifications/${notificationId}/seen`
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to mark notification as seen"
      );
    }
  },

  /**
   * Mark all notifications as seen
   */
  markAllAsSeen: async (): Promise<any> => {
    try {
      const response = await api.get<any>(`/notifications/mark-all-as-seen`);
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to mark all notifications as seen"
      );
    }
  },
};
