import api from "@/services/axios";
import {
  UserInfo,
  UserProfileUpdateRequest,
  ProfileUpdateResponse,
  UserUpdate,
  UserResponse,
  UserGeneralSettingsResponse,
} from "@/shared/interfaces";

export const userApi = {
  /**
   * Get current user information
   * @description Requires valid JWT Bearer token in Authorization header
   */
  getCurrentUser: async (): Promise<UserInfo> => {
    try {
      const response = await api.get<UserInfo>("/users/me");
      return response.data;
    } catch (error: any) {
      // Let axios interceptor handle auth errors (401/403) for token refresh
      // Only handle non-auth errors here
      switch (error.response?.status) {
        case 404:
          throw new Error("User not found");
        case 500:
          throw new Error("Internal server error");
        default:
          throw new Error(
            error.response?.data?.detail || "Failed to fetch user information"
          );
      }
    }
  },

  /**
   * Update current user profile details (company, department, job role)
   * @description Requires valid J<PERSON><PERSON> Bearer token in Authorization header
   */
  updateUserProfile: async (
    data: UserProfileUpdateRequest
  ): Promise<ProfileUpdateResponse> => {
    try {
      const response = await api.put<ProfileUpdateResponse>(
        "/users/profile-details",
        data
      );
      return response.data;
    } catch (error: any) {
      // Let axios interceptor handle auth errors (401/403) for token refresh
      // Only handle non-auth errors here
      switch (error.response?.status) {
        case 422:
          const details = error.response?.data?.detail;
          // Base validation message that will be enhanced with specific field errors
          // if they exist in the API response. The final message will be in the format:
          // "Validation failed. fieldName1: errorMsg1, fieldName2: errorMsg2"
          let validationMsg = "Validation failed.";
          if (Array.isArray(details)) {
            validationMsg +=
              " " +
              details.map((d: any) => `${d.loc?.[1]}: ${d.msg}`).join(", ");
          }
          throw new Error(validationMsg);
        case 500:
          throw new Error("Internal server error during profile update");
        default:
          throw new Error(
            error.response?.data?.detail || "Failed to update user profile"
          );
      }
    }
  },

  /**
   * Update current user details (full name, phone number, profile image)
   * @description Requires valid JWT Bearer token in Authorization header
   */
  updateUserDetails: async (data: UserUpdate): Promise<UserResponse> => {
    try {
      const response = await api.put<UserResponse>("/users/me", data);
      return response.data;
    } catch (error: any) {
      // Let axios interceptor handle auth errors (401/403) for token refresh
      // Only handle non-auth errors here
      switch (error.response?.status) {
        case 422:
          const details = error.response?.data?.detail;
          let validationMsg = "Validation failed.";
          if (Array.isArray(details)) {
            validationMsg +=
              " " +
              details.map((d: any) => `${d.loc?.[1]}: ${d.msg}`).join(", ");
          }
          throw new Error(validationMsg);
        case 500:
          throw new Error("Internal server error during user update");
        default:
          throw new Error(
            error.response?.data?.detail || "Failed to update user details"
          );
      }
    }
  },

  /**
   * Delete current user account
   * @description Requires valid JWT Bearer token in Authorization header
   */
  deleteCurrentUser: async (): Promise<void> => {
    try {
      await api.delete("/users/me");
    } catch (error: any) {
      // Let axios interceptor handle auth errors (401/403) for token refresh
      // Only handle non-auth errors here
      switch (error.response?.status) {
        case 404:
          throw new Error("User not found");
        case 500:
          throw new Error("Internal server error during account deletion");
        default:
          throw new Error(
            error.response?.data?.detail || "Failed to delete user account"
          );
      }
    }
  },

  /**
   * Update current user password
   * @description Requires valid JWT Bearer token in Authorization header
   */
  updatePassword: async (data: {
    current_password: string;
    new_password: string;
    confirm_new_password: string;
  }): Promise<{ message: string }> => {
    try {
      const response = await api.post<{ message: string }>(
        "/users/reset-password",
        data
      );
      return response.data;
    } catch (error: any) {
      console.error(error);
      throw new Error(
        error.response?.data?.detail || "Failed to update password"
      );
    }
  },

  getUserGeneralSettings: async (): Promise<UserGeneralSettingsResponse> => {
    try {
      const response =
        await api.get<UserGeneralSettingsResponse>("/users/preferences");
      return response.data;
    } catch (error: any) {
      console.error(error);
      throw new Error(
        error.response?.data?.detail || "Failed to get user general settings"
      );
    }
  },
  updateUserGeneralSettings: async (data: {
    provider: string;
    model: string;
    temperature: number;
    max_output_tokens: number;
  }): Promise<UserGeneralSettingsResponse> => {
    try {
      const response = await api.put<UserGeneralSettingsResponse>(
        "/users/preferences",
        data
      );
      return response.data;
    } catch (error: any) {
      console.error(error);
      throw new Error(
        error.response?.data?.detail || "Failed to update user general settings"
      );
    }
  },
  getUserDetailsById: async (userId: string): Promise<any> => {
    try {
      const response: any = await api.get(`/users/get-user-details/${userId}`);
      return response.data;
    } catch (error: any) {
      console.error(error);
      throw new Error(
        error.response?.data?.detail || "Failed to get user details"
      );
    }
  },
};
