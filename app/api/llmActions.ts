import api from "@/services/axios";
import {
  UserPromptImprovementRequest,
  UserPromptImprovementResponse,
} from "@/shared/interfaces";
import { EMPLOYEE_INSTRUCTIONS_PROMPT } from "@/shared/prompt";

/**
 * LLM Actions API functions for interacting with OpenAI through Requesty
 */
export const llmActionsApi = {
  /**
   * Improve a user prompt using OpenAI through Requesty
   * @param originalPrompt The original user prompt to improve
   * @param mode The mode of the prompt: 'ACT' for task/action prompts, 'ASK' for question/search prompts
   * @returns Promise resolving to the improved prompt response
   */
  improveUserPrompt: async (
    originalPrompt: string,
    mode: "ACT" | "ASK"
  ): Promise<UserPromptImprovementResponse> => {
    try {
      const payload: UserPromptImprovementRequest = {
        original_prompt: originalPrompt,
        mode,
      };

      const response = await api.post<UserPromptImprovementResponse>(
        "/prompts/improve-user-prompt",
        payload
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to improve prompt"
      );
    }
  },
};

/**
 * Generate or enhance employee instructions using LLM
 * @param description The employee description (required)
 * @param currentInstruction The current instruction (optional, for enhancement)
 * @returns Promise resolving to the generated/enhanced instruction string
 */
export const generateEmployeeInstructions = async (
  description: string,
  currentInstruction?: string
): Promise<string> => {
  try {
    const prompt = (EMPLOYEE_INSTRUCTIONS_PROMPT as string).replace(
      "{AGENT_DESCRIPTION}",
      description
    );
    const payload: {
      original_prompt: string;
      agent_context?: {
        additionalProp1: string;
      };
    } = {
      original_prompt: prompt,
    };
    if (currentInstruction) {
      payload.agent_context = {
        additionalProp1: currentInstruction,
      };
    }
    const response = await api.post<{ improved_prompt: string }>(
      "/prompts/improve",
      payload
    );
    return response.data.improved_prompt || "";
  } catch (error: any) {
    throw new Error(
      error.response?.data?.detail ||
        error.response?.data?.message ||
        error.message ||
        "Failed to generate instructions"
    );
  }
};
