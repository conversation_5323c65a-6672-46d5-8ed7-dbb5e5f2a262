import api from "@/services/axios";
import { DepartmentList } from "@/shared/interfaces";

export const departmentApi = {
  /* =================== Organization Details ================== */

  /**
   * Get Department List
   * @description Requires valid JWT Bearer token in Authorization header
   */
  getAllDepartment: async (): Promise<DepartmentList[]> => {
    try {
      const response = await api.get(`/organisations/getDepartments`);
      return response?.data?.departments || [];
    } catch (error: any) {
      console.error("error=>", error);
      return [];
    }
  },

  getDepartmentMembers: async (
    departmentId: string,
    page: number = 1,
    pageSize: number = 10
  ): Promise<any> => {
    try {
      const response = await api.get(
        `/organisations/users?department_id=${departmentId}&page=${page}&page_size=${pageSize}`
      );
      return response?.data;
    } catch (error: any) {
      console.error("error=>", error);
      return {};
    }
  },

  inviteMemberToDepartment: async (
    organisation_id: string,
    department_id: string,
    data: any
  ) => {
    try {
      const response = await api.post(
        `/api/v1/organisations/${organisation_id}/departments/${department_id}/members`,
        data
      );
      return response?.data;
    } catch (error: any) {
      console.error("error=>", error);
    }
  },
};
