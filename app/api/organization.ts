import api from "@/services/axios";
import { Organization, OrganizationDetailsResponse } from "@/shared/interfaces";

interface Department {
  name: string;
  role: string;
  permission: string;
}

interface Member {
  id: string;
  name: string;
  email: string;
  departments: Department[];
}

// Define the department agents response structure based on the actual API response
export interface DepartmentAgent {
  id: string;
  name: string;
  description: string;
  department: string;
  owner_id: string;
  owner_name: string;
  user_ids: string[];
  created_at: string;
  updated_at: string;
  visibility: string;
  status: string;
  creator_role: string;
}

// Define the admin user interface based on actual API response
export interface AdminUser {
  adminId: string;
  email: string;
  fullName: string;
  createdAt: string;
  updatedAt: string;
}

export interface DepartmentAgentsResponse {
  success: boolean;
  message: string;
  agents: DepartmentAgent[];
  total: number;
}

export const organizationApi = {
  /* =================== Organization Details ================== */

  /**
   * Get User Organization information
   * @description Requires valid JWT Bearer token in Authorization header
   */
  getOrganizationDetails: async (): Promise<Organization[]> => {
    try {
      const response = await api.get("/organisations/getUserOrganisations");
      return response?.data?.organisations || [];
    } catch (error: any) {
      console.error("error=>", error);
      return [];
    }
  },

  /**
   * Get Current Organization Details with MCP information
   * @description Requires valid JWT Bearer token in Authorization header
   * @returns Promise resolving to organization details with MCP key information
   */
  getCurrentOrganizationDetails:
    async (): Promise<OrganizationDetailsResponse | null> => {
      try {
        const response = await api.get<OrganizationDetailsResponse>(
          "/organisations/details"
        );
        return response?.data || null;
      } catch (error: any) {
        console.error("error=>", error);
        throw new Error(
          error.response?.data?.detail ||
            error.response?.data?.message ||
            "Failed to get organization details"
        );
      }
    },

  /**
   * Update Organization information
   * @description Requires valid JWT Bearer token in Authorization header
   */
  updateOrganizationDetails: async (orgId: string, data: any) => {
    try {
      const response = await api.put(`/organisations/${orgId}`, data);
      return response?.data?.users || [];
    } catch (error: any) {
      console.error("error=>", error);
    }
  },

  /**
   * Get All Users of Organization
   * @description Requires valid JWT Bearer token in Authorization header
   */
  getAllActiveOrgUsers: async (
    page: number = 1,
    pageSize: number = 10
  ): Promise<{
    users: Member[];
    totalCount: number;
    page: number;
    pageSize: number;
  }> => {
    try {
      const response = await api.get(
        `/organisations/users?page=${page}&page_size=${pageSize}`
      );
      return {
        users: response?.data?.users || [],
        totalCount: response?.data?.totalCount || 0,
        page: response?.data?.page || page,
        pageSize: response?.data?.pageSize || pageSize,
      };
    } catch (error: any) {
      console.error("error=>", error);
      return {
        users: [],
        totalCount: 0,
        page: page,
        pageSize: pageSize,
      };
    }
  },

  /**
   * Get All Users of Organization
   * @description Requires valid JWT Bearer token in Authorization header
   */
  getAllInvitedOrgUsers: async (type: string): Promise<any> => {
    try {
      const response = await api.get(
        `/organisations/invites?invite_type=${type}`
      );
      return response?.data?.invites || [];
    } catch (error: any) {
      console.error("error=>", error);
      return [];
    }
  },

  /**
   * Get all agents for a specific department
   * @param departmentId The department ID to get agents for
   * @returns Promise resolving to the department agents response
   */
  getDepartmentAgents: async (
    departmentId: string
  ): Promise<DepartmentAgentsResponse> => {
    try {
      // Make the API request to the new endpoint
      const response = await api.get<DepartmentAgentsResponse>(
        `/agent-graph/departments/${departmentId}/agents`
      );

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get agents"
      );
    }
  },

  /**
   * Get Organization Admin
   * @description Requires valid JWT Bearer token in Authorization header
   * @param orgId The organization ID to get admin for
   * @returns Promise resolving to admin user or null
   */
  getOrganizationAdmins: async (): Promise<AdminUser | null> => {
    try {
      const response = await api.get(`/organisations/admin`);
      return response?.data?.admin || null;
    } catch (error: any) {
      console.error("error=>", error);
      return null;
    }
  },

  /**
   * Switch Organization with Cookie Management
   * @description Clear existing cookies and set new ones after organization switch
   * @param organisationId The organization ID to switch to
   */
  switchOrganizationWithCookies: async (organisationId: string) => {
    try {
      // Call our internal API route that handles cookie management
      const response = await fetch("/api/switch-organization", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ organisation_id: organisationId }),
        credentials: "include", // Important: include cookies in the request
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to switch organization");
      }

      const data = await response.json();
      return data;
    } catch (error: any) {
      throw new Error(error.message || "Failed to switch organization");
    }
  },
};
