import api from "@/services/axios";
import workflow_execution_api from "@/services/workflowExecutionAxios";
import {
  PaginatedWorkflowResponse,
  WorkflowsByIdsResponse,
} from "@/shared/interfaces";

export const workflowApi = {
  /**
   * Get workflows for the current user with pagination
   * @param page The page number for pagination (default: 1)
   * @param pageSize The number of workflows per page (default: 10)
   * @returns Promise resolving to the paginated list of workflows
   */
  getWorkflowsByUser: async (
    page?: number,
    pageSize?: number,
    searchText?: string
  ): Promise<PaginatedWorkflowResponse> => {
    try {
      const response = await api.get<PaginatedWorkflowResponse>("/workflows", {
        params: {
          page,
          page_size: pageSize,
          search: searchText,
        },
      });

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get workflows"
      );
    }
  },

  /**
   * Get multiple Workflows by their IDs
   * @param ids Array of Workflows IDs to retrieve
   * @returns Promise resolving to the MCPs matching the provided IDs
   */
  getWorkflowsByIds: async (ids: string[]): Promise<WorkflowsByIdsResponse> => {
    try {
      const response = await api.post<WorkflowsByIdsResponse>(
        "/workflows/by-ids",
        {
          ids,
        }
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to get Workflows by IDs"
      );
    }
  },

  executeWorkflow: async (payload: any): Promise<{ correlationId: string }> => {
    try {
      const response = await workflow_execution_api.post(
        "/workflow-execute/execute",
        payload
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to execute workflow"
      );
    }
  },

  executeWorkflowApprove: async (
    payload: any
  ): Promise<{ correlationId: string }> => {
    try {
      const response = await workflow_execution_api.post(
        "/workflow-execute/approve",
        payload
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to execute workflow"
      );
    }
  },

  // This api is used to re-execute a workflow
  executeWorkflowReExecute: async (
    payload: any
  ): Promise<{ correlationId: string }> => {
    try {
      const response = await workflow_execution_api.post(
        "/workflow-execute/re-execute",
        payload
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to re-execute workflow"
      );
    }
  },

  stopWorkflow: async (payload: any): Promise<{ correlationId: string }> => {
    try {
      payload.decision = "reject";
      const response = await workflow_execution_api.post(
        "/workflow-execute/approve",
        payload
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to stop workflow"
      );
    }
  },

  /**
   * Fetch marketplace workflows (page 1, page_size 10, sort_by NEWEST)
   */
  getMarketplaceWorkflows: async (page?: number, pageSize?: number) => {
    try {
      const response = await api.get("/marketplace/workflows", {
        params: { page: page, page_size: pageSize, sort_by: "MOST_POPULAR" },
      });
      return response.data.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to fetch marketplace workflows"
      );
    }
  },
};
