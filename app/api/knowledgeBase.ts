import api from "@/services/axios";

export const knowledgeBaseApi = {
  /* =================== Organization Details ================== */

  /**
   * Get KnowledgeBase List
   * @description Requires valid JW<PERSON>er token in Authorization header
   */
  getKnowledgeBaseList: async (): Promise<any> => {
    try {
      const response = await api.get(`/organisations/sources`);
      return response?.data || {};
    } catch (error: any) {
      console.error("error=>", error);
      return {};
    }
  },

  getKnowledgeBaseFolders: async (orgId: string) => {
    const data = {
      organisation_id: orgId,
    };
    try {
      const response = await api.post(`/google-drive/top-level-folders`, data);
      return response?.data?.folders || [];
    } catch (error: any) {
      console.error("error=>", error);
      return [];
    }
  },

  getFolderAccessDepartment: async (orgId: string, departmentIds: string[]) => {
    const data = {
      organisationId: orgId,
      departmentIds: departmentIds,
    };
    try {
      const response = await api.post(
        `/organisations/departments/folders`,
        data
      );
      return response?.data?.departmentFolders || [];
    } catch (error: any) {
      console.error("error=>", error);
      return [];
    }
  },

  syncKnowledgeBaseFolder: async (
    user_id: string,
    orgId: string,
    full_sync: boolean
  ) => {
    const data = {
      user_id: user_id,
      organisation_id: orgId,
      full_sync: full_sync,
    };
    try {
      const response = await api.post(`/google-drive/sync`, data);
      return response;
    } catch (error: any) {
      console.error("error=>", error);
      return [];
    }
  },

  /**
   * Batch Grant Access to Departments
   * @description Grant access to multiple departments for specific files and folders
   */
  batchGrantAccess: async (data: {
    departmentData: Array<{
      departmentId: string;
      fileIds?: string[];
      folderIds?: string[];
    }>;
  }): Promise<{
    success: boolean;
    message: string;
    failedDepartmentIds: string[];
  }> => {
    try {
      const response = await api.post(
        `/organisations/departments/batch-grant-access`,
        data
      );
      return (
        response?.data || {
          success: false,
          message: "Unknown error occurred",
          failedDepartmentIds: [],
        }
      );
    } catch (error: any) {
      console.error("error=>", error);
      throw error;
    }
  },
};
