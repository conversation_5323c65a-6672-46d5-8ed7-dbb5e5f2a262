import api from "@/services/axios";
import {
  clearAuthCookies,
  setAuthCookies,
  getAccessToken,
  checkAccessToken,
} from "@/services/authCookies";
import { userApi } from "@/app/api/user";
import { organizationApi } from "./organization";
import { useUserStore } from "@/hooks/use-user";
import { useOrgStore } from "@/hooks/use-organization";
import { loginRoute } from "@/shared/routes";
import { TokenResponse } from "@/shared/interfaces";

/**
 * Initializes the user session from an access token cookie.
 * Fetches user details and populates the user store.
 * This function is intended to be called from a client-side component.
 * @returns {Promise<boolean>} True if session was successfully initialized or already active in store, false otherwise.
 */
export async function initializeSessionFromCookie(): Promise<boolean> {
  // Get the user store state once at the beginning
  const userStoreState = useUserStore.getState();
  const orgStoreState = useOrgStore.getState();

  // 1. If user data is already in the store and we have a valid access token cookie, session is considered initialized
  if (userStoreState.user) {
    const hasAccessToken = await checkAccessToken();
    if (hasAccessToken) {
      // Update the store state using the reference we already have
      if (userStoreState.isLoadingAuth) {
        userStoreState.setIsLoadingAuth(false);
      }
      return true;
    }
  }

  try {
    // 2. Try to get the access token from cookies.
    const accessToken = await getAccessToken();

    if (!accessToken) {
      // No token in cookie, so no session to initialize from cookie.
      // The calling component (SessionInitializer) will handle redirection or public access.
      userStoreState.setIsLoadingAuth(false); // Done checking
      return false;
    }

    // 3. Token found in cookie, try to fetch user details.
    // userApi.getCurrentUser() should internally use this token via the axios interceptor.
    const userDetails = await userApi.getCurrentUser();

    if (!userDetails) {
      // User details could not be fetched, even with a token.
      await clearAuthCookies(); // Clear potentially invalid cookies
      userStoreState.clearUser(); // Clear store
      userStoreState.setIsLoadingAuth(false); // Done checking
      orgStoreState.clearOrganization();
      return false;
    }

    // 4. Successfully fetched user details, populate the user store.

    userStoreState.setUser({
      id: userDetails.id,
      fullName: userDetails.fullName,
      email: userDetails.email,
      company: userDetails.company,
      department: userDetails.department,
      jobRole: userDetails.jobRole,
      phoneNumber: userDetails.phoneNumber,
      profileImage: userDetails.profileImage,
      isFirstLogin: userDetails.isFirstLogin,
      accessToken: accessToken,
    });

    const currentOrgDetails =
      await organizationApi.getCurrentOrganizationDetails();
    orgStoreState.setCurrentOrganization(currentOrgDetails);

    return true; // Session initialized successfully
  } catch (error: any) {
    // This catch block handles errors from getAccessToken() or userApi.getCurrentUser()
    console.error("Error initializing session from cookie:", error);
    await clearAuthCookies();
    userStoreState.clearUser();
    return false;
  }
}

export const authApi = {
  /**
   * Logout user
   */
  logout: async (): Promise<void> => {
    try {
      // Clear auth cookies
      await clearAuthCookies();

      // Clear user store (including accessToken)
      useUserStore.getState().clearUser();
      useOrgStore.getState().clearOrganization();

      // Redirect to login page
      window.location.href = loginRoute;
    } catch (error: any) {
      // Even if there's an error, try to clear the user store
      useUserStore.getState().clearUser();
      throw new Error(error.response?.data?.message || "Logout failed");
    }
  },

  /**
   * Generate new access token using refresh token
   * @param refreshToken The refresh token to use for generating a new access token
   */
  generateAccessToken: async (refreshToken: string): Promise<TokenResponse> => {
    try {
      const response = await api.post<TokenResponse>(
        "/auth/access-token",
        {},
        {
          params: { refresh_token: refreshToken },
        }
      );

      // Update the access token in cookies if successful
      if (response.data.success && response.data.access_token) {
        // Calculate token age in seconds from tokenExpireAt
        const expireAt = new Date(response.data.tokenExpireAt).getTime();
        const now = new Date().getTime();
        const accessTokenAge = Math.floor((expireAt - now) / 1000);

        await setAuthCookies(
          response.data.access_token,
          refreshToken,
          accessTokenAge > 0 ? accessTokenAge : 3600, // Default to 1 hour if calculation is negative
          null // Don't update refresh token age
        );

        // Also update the access token in the user store
        const currentUser = useUserStore.getState().user;
        if (currentUser) {
          useUserStore.getState().setUser({
            ...currentUser,
            accessToken: response.data.access_token,
          });
        }
      }

      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          error.response?.data?.message ||
          "Failed to generate new access token"
      );
    }
  },
};
