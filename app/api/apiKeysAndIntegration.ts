import api from "@/services/axios";

export const apiKeyAndIntegration = {
  /**
   * Get current user information
   * @description Requires valid JWT Bearer token in Authorization header
   */
  getListOfIntegrations: async (
    page = 1,
    limit = 60,
    isEnabled = true,
    search = ""
  ): Promise<any> => {
    try {
      const response = await api.get<any>("/integrations", {
        params: {
          page,
          page_size: limit,
          is_enabled: isEnabled,
          search: search,
          unconnected_only: true,
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          "Failed to fetch api keys and integrations"
      );
    }
  },
  /**
   * Get user connected integrations
   * @returns Promise resolving to the API response
   */
  getUserConnectedIntegrations: async (
    page = 1,
    page_size = 60,
    search = ""
  ): Promise<any> => {
    try {
      const response = await api.get<any>("/integrations/user/connected", {
        params: { page, page_size, search: search || undefined },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail ||
          "Failed to get user connected integrations"
      );
    }
  },

  /**
   * Add API Key credentials to an integration
   * @param integrationId The integration's ID
   * @param credentials The credentials object (e.g., { api_key, secret })
   * @returns Promise resolving to the API response
   */
  addApiKeyCredentials: async (
    integrationId: string,
    credentials: Record<string, string>
  ): Promise<any> => {
    try {
      const response = await api.post<any>(
        `/integrations/${integrationId}/api-key/credentials`,
        { credentials }
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || "Failed to add API key credentials"
      );
    }
  },
  /**
   * Authorize OAuth integration
   * @param integrationId The integration's ID
   * @returns Promise resolving to the API response
   */
  authorizeOauthIntegration: async (
    integrationId: string,
    user_id?: string
  ): Promise<any> => {
    try {
      const response = await api.get<any>(
        `/integrations/${integrationId}/oauth/authorize`,
        {
          params: {
            user_id: user_id,
          },
        }
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || "Failed to authorize OAuth integration"
      );
    }
  },
  /**
   * Get saved API Key credentials for an integration
   * @param integrationId The integration's ID
   * @returns Promise resolving to the saved credentials object
   */
  getApiKeyCredentials: async (integrationId: string): Promise<any> => {
    try {
      const response = await api.get<any>(
        `/integrations/${integrationId}/api-key/credentials`
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || "Failed to fetch API key credentials"
      );
    }
  },
  /**
   * Get Oauth credentials for an integration
   * @param integrationId The integration's ID
   * @returns Promise resolving to the saved credentials object
   */
  getOauthCredentials: async (integrationId: string): Promise<any> => {
    try {
      const response = await api.get<any>(
        `/integrations/${integrationId}/oauth/credentials`
      );
      return response?.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || "Failed to fetch Oauth credentials"
      );
    }
  },
  /**
   * Disconnect OAuth credentials for an integration
   * @param integrationId The integration's ID
   * @returns Promise resolving to the API response
   */
  disconnectOauthCredentials: async (integrationId: string): Promise<any> => {
    try {
      const response = await api.delete<any>(
        `/integrations/${integrationId}/oauth/credentials`
      );
      return response.data;
    } catch (error: any) {
      throw new Error(
        error.response?.data?.detail || "Failed to disconnect OAuth credentials"
      );
    }
  },
};
