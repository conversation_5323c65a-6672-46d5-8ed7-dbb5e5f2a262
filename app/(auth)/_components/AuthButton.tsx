"use client";

import { useSearchParams } from "next/navigation";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { authRoute } from "@/shared/routes";
import { useRouter } from "next/navigation";

export const AuthButton = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const inviteToken = searchParams.get("token");

  const handleLogin = () => {
    if (inviteToken) {
      router.push(
        `${process.env.NEXT_PUBLIC_AUTH_URL}?redirect_url=${process.env.NEXT_PUBLIC_APP_URL}&invitedToken=${inviteToken}`
      );
    } else {
      router.push(authRoute);
    }
  };

  return (
    <div>
      <PrimaryButton onClick={handleLogin} className="w-full">
        Login
      </PrimaryButton>
    </div>
  );
};
