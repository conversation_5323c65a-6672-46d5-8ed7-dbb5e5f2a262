import { Metadata } from "next";
import { PricingModal } from "@/components/modals/PricingModal";
import { DashboardSidebar } from "./_components/DashboardSidebar";
import { ConditionalContent } from "./_components/ConditionalContent";
import { MobileSidebarToggle } from "./_components/MobileSidebarToggle";

// To Show the current page in the browser tab along with application name.
export const metadata: Metadata = {
  title: "Dashboard",
};

export default async function MainLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <main className="flex min-h-screen bg-brand-background flex-row overflow-hidden max-w-full">
      <MobileSidebarToggle />
      <DashboardSidebar />
      <div className="flex-1 min-w-0 overflow-hidden">
        <ConditionalContent>{children}</ConditionalContent>
      </div>
      <PricingModal />
    </main>
  );
}
