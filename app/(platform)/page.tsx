"use client";
import { useEffect, useState, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import { communicationApi } from "@/app/api/communication";
import ChatWelcomeScreen from "./chat/_components/ChatWelcomeScreen";
import EmployeeSelector from "./chat/_components/EmployeeSelector";
import ChatInput from "./_components/ChatInput";
import { useEmployeeManagementStore } from "@/hooks/useEmployeeManagementStore";
import { GLOBAL_AGENT, LOCAL_STORAGE_KEYS } from "@/shared/constants";
import { Employee } from "@/shared/interfaces";
import { AIChatMode, SenderType } from "@/shared/enums";
import { globalAgentTourConfig } from "@/lib/utils/tourConfig";
import { useTourContext } from "@/lib/providers/TourProvider";
import { isTourCompleted, markTourCompleted } from "@/lib/utils/tourUtils";
import { employeeChatRoute } from "@/shared/routes";

export default function HomePage() {
  const [conversationId, setConversationId] = useState<string | null>(null);
  const router = useRouter();
  const pendingSend = useRef<{
    message: string;
    attachments: any[];
    tools: string[];
    selectedMode: AIChatMode;
  } | null>(null);
  const { startTour } = useTourContext();

  useEffect(() => {
    async function fetchConversationId() {
      const convId = await communicationApi.createConversation();
      setConversationId(convId);
    }
    fetchConversationId();
    if (!isTourCompleted(LOCAL_STORAGE_KEYS.RUH_HOME_PAGE_TOUR_COMPLETED)) {
      handleStartTour();
    }
  }, []);

  const {
    setSelectedEmployeeId,
    initializeChatSession,
    resetStream,
    addMessage,
  } = useEmployeeManagementStore();

  const handleSend = useCallback(
    async ({
      message,
      attachments,
      tools,
      selectedMode,
    }: {
      message: string;
      attachments: any[];
      tools: string[];
      selectedMode: AIChatMode;
    }) => {
      if (!conversationId) {
        pendingSend.current = { message, attachments, tools, selectedMode };
        return;
      }
      const employeeKey = GLOBAL_AGENT.id + conversationId;
      setSelectedEmployeeId(employeeKey);
      initializeChatSession(employeeKey);
      resetStream();
      addMessage({
        content: message,
        senderType: SenderType.USER,
        attachments,
      });
      localStorage.setItem(
        LOCAL_STORAGE_KEYS.RUH_GLOBAL_AGENT_PENDING_MESSAGE,
        JSON.stringify({ message, attachments, tools, selectedMode })
      );
      router.push(`/chat/${conversationId}`);
    },
    [
      conversationId,
      router,
      setSelectedEmployeeId,
      initializeChatSession,
      resetStream,
      addMessage,
    ]
  );

  useEffect(() => {
    if (conversationId && pendingSend.current) {
      const { message, attachments, tools, selectedMode } = pendingSend.current;
      pendingSend.current = null;
      handleSend({ message, attachments, tools, selectedMode });
    }
  }, [conversationId, handleSend]);

  const handleStartTour = useCallback(() => {
    startTour(globalAgentTourConfig, {
      onComplete: () => {
        markTourCompleted(LOCAL_STORAGE_KEYS.RUH_HOME_PAGE_TOUR_COMPLETED);
      },
      onSkip: () => {
        markTourCompleted(LOCAL_STORAGE_KEYS.RUH_HOME_PAGE_TOUR_COMPLETED);
      },
    });
  }, [startTour]);

  const handleMentionHandoff = useCallback(
    ({
      message,
      attachments,
      mentionedEmployee,
    }: {
      message: string;
      attachments: any[];
      mentionedEmployee: Employee;
    }) => {
      localStorage.setItem(
        "RUH_GLOBAL_MENTION_HANDOFF",
        JSON.stringify({
          message,
          attachments,
          employeeId: mentionedEmployee.id,
        })
      );
      // Store employee in localStorage cache
      const cacheKey = LOCAL_STORAGE_KEYS.AGENT_DETAILS_CACHE;
      const cache = JSON.parse(localStorage.getItem(cacheKey) || "{}");
      cache[mentionedEmployee.id] = mentionedEmployee;
      localStorage.setItem(cacheKey, JSON.stringify(cache));
      localStorage.setItem(LOCAL_STORAGE_KEYS.IS_NEW_CHAT, "true");
      router.push(`${employeeChatRoute}/${mentionedEmployee.id}`);
    },
    []
  );

  return (
    <div className="flex h-full w-full flex-col bg-brand-background overflow-hidden">
      <div className="flex-1 flex flex-col items-center justify-start p-4 md:p-2 lg:p-8 overflow-y-auto max-h-[100vh]">
        <div className="w-full max-w-4xl space-y-4 md:space-y-6 pt-10 md:pt-18 m-auto">
          <ChatWelcomeScreen />
          <ChatInput
            chatStarted={false}
            employee={GLOBAL_AGENT as Employee}
            conversationId={conversationId}
            sessionId={null}
            workflows={[]}
            showAIChatModeDropdown={true}
            onSend={handleSend}
            onMentionHandoff={handleMentionHandoff}
          />
          <EmployeeSelector />
        </div>
      </div>
    </div>
  );
}
