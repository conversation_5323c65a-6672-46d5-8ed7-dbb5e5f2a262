"use client";

import React, { useState, useRef, useEffect } from "react";
import { cn, globalAgentPlaceholderText } from "@/lib/utils";
import { useEmployeeManagementStore } from "@/hooks/useEmployeeManagementStore";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Paperclip,
  Mic,
  FileText,
  X,
  Check,
  ChevronDown,
  Building2Icon,
  SendHorizonalIcon,
  Package2,
  PackageIcon,
  InfoIcon,
  Square,
  Globe,
  AtSignIcon,
  PlusIcon,
  Trash2Icon,
  Loader2,
  Cable,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Command,
  CommandEmpty,
  CommandInput,
} from "@/components/ui/command";

import { Badge } from "@/components/ui/badge";
import { Employee, WorkflowInDB } from "@/shared/interfaces";
import { communicationApi } from "@/app/api/communication";
import {
  AIChatMode,
  SenderType,
  SearchModeResources,
  AnalyticsEvents,
} from "@/shared/enums";
import { useWorkflowStore } from "@/hooks/use-workflow";
import QuickToolsIcon from "@/public/assets/Icons-components/QuickToolsIcon";
import { useMultiFileUpload } from "@/hooks/useMultiFileUpload";
import SpeechRecognition, {
  useSpeechRecognition,
} from "react-speech-recognition";
import SettingsOutlineIcon from "@/public/assets/Icons-components/SettingsOutlineIcon";
import BookOutlineIcon from "@/public/assets/Icons-components/BookOutlineIcon";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import EnhancePromptButton from "@/components/shared/EnhancePromptButton";
import MaximizeTextareaModal from "@/components/modals/MaximizeTextareaModal";
import { ItemSelectionDialog } from "@/components/shared/ItemSelectionDialog";
import { mcpApi } from "@/app/api/mcp";
import Image from "next/image";
import { agentApi } from "@/app/api/agent";
import { sanitizeString } from "@/services/helper";
import { LOCAL_STORAGE_KEYS, marketplaceMcpsUrl } from "@/shared/constants";
import { workflowApi } from "@/app/api/workflow";
import { useOutOfCreditsModalStore } from "@/hooks/use-out-of-credits-modal";
import { useIsMobile } from "@/hooks/use-mobile";
import { CustomSeparator } from "@/components/shared/CustomSeparator";
import { toast } from "sonner";
import CustomTooltip from "@/components/shared/CustomTooltip";
import { useRouter } from "next/navigation";
import { LoadingSpinner } from "@/components/shared/LoadingSpinner";
import { useMixpanelTrack } from "@/hooks/useMixPanelTrack";

interface EmployeeEnhancedChatInputProps {
  employee?: Employee;
  conversationId?: string | null;
  sessionId?: string | null;
  className?: string;
  workflows?: WorkflowInDB[];
  showAIChatModeDropdown?: boolean;
  onMentionHandoff?: (data: {
    message: string;
    attachments: Array<{
      file_name: string;
      file_type: string;
      file_size: number;
      file_url: string;
    }>;
    mentionedEmployee: Employee;
  }) => void;
  chatStarted?: boolean;
  onSend?: (data: {
    message: string;
    attachments: any[];
    tools: string[];
    selectedMode: AIChatMode;
  }) => void;
}

export default function ChatInput({
  employee,
  conversationId,
  sessionId,
  className,
  workflows = [],
  showAIChatModeDropdown = false,
  onMentionHandoff,
  chatStarted,
  onSend,
}: EmployeeEnhancedChatInputProps) {
  const {
    employee: { selectedId },
    chat: { sessions },
    addMessage,
    setTyping,
    isWorkflowStarted,
    setThinkingStatusText,
    clearDiscoveredEmployees,
    removeWorkflowStepsWithoutOutput,
    setWorkflowCancelled,
    setIsWorkflowStarted,
  } = useEmployeeManagementStore();
  const session = selectedId ? sessions[selectedId] : null;
  const isTyping = session?.isTyping || false;
  const isStreaming = session?.streamState?.isStreaming || false;
  const inputDisabled = isTyping || isStreaming; // Disable when typing or streaming
  const isMobile = useIsMobile();

  const [input, setInput] = useState("");
  const [isPaletteOpen, setIsPaletteOpen] = useState(false);
  const [filteredWorkflows, setFilteredWorkflows] = useState<WorkflowInDB[]>(
    []
  );
  const [selectedWorkflowIndex, setSelectedWorkflowIndex] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [selectedMode, setSelectedMode] = useState<AIChatMode>(() => {
    if (typeof window !== "undefined") {
      const stored = localStorage.getItem(
        LOCAL_STORAGE_KEYS.SELECTED_MODE
      ) as AIChatMode;
      if (stored) return stored;
    }
    return AIChatMode.ACT;
  });
  const [selectedSource, setSelectedSourceState] =
    useState<SearchModeResources>(() => {
      if (typeof window !== "undefined") {
        const stored = localStorage.getItem(
          LOCAL_STORAGE_KEYS.SELECTED_SOURCE
        ) as SearchModeResources;
        if (
          stored &&
          Object.values(SearchModeResources).includes(stored as any)
        ) {
          return stored as SearchModeResources;
        }
      }
      return SearchModeResources.ALL;
    });
  const [isMaximizeModalOpen, setIsMaximizeModalOpen] = useState(false);

  // All Tools Modal
  const [addToolsModal, setAddToolsModal] = useState(false);
  const [toolsSearchQuery, setToolsSearchQuery] = useState("");

  // Out of Credits Modal store
  const { openModal: openOutOfCreditsModal } = useOutOfCreditsModalStore();

  // State for pagination and search
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreItems, setHasMoreItems] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [allFetchedData, setAllFetchedData] = useState<any[]>([]);
  const [debouncedToolsSearchQuery, setDebouncedToolsSearchQuery] =
    useState("");

  const queryClient = useQueryClient();

  // Use react-query for MCP tools, but only fetch when modal is open
  const {
    data: mcpData,
    isLoading: isInitialLoading,
    refetch: refetchMcpTools,
  } = useQuery({
    queryKey: ["mcps", currentPage, debouncedToolsSearchQuery],
    queryFn: () =>
      mcpApi.getMcpServersByUser(
        currentPage,
        20,
        false,
        debouncedToolsSearchQuery
      ),
    enabled: addToolsModal, // Enable when modal is open
  });

  // Only show loading state for initial load (page 1), not for subsequent pages
  const isLoadingTools = currentPage === 1 ? isInitialLoading : false;

  // Handle successful data fetching
  useEffect(() => {
    if (mcpData) {
      // When we get new data, append it to our allFetchedData if it's not the first page
      if (currentPage > 1) {
        setAllFetchedData((prev) => [...prev, ...mcpData.data]);
      } else {
        // If it's the first page, just set the data directly
        setAllFetchedData(mcpData.data || []);
      }

      // Update loading state and check if there are more items
      setIsLoadingMore(false);
      setHasMoreItems(mcpData.metadata?.hasNextPage || false);
    }
  }, [mcpData, currentPage]);

  // Debounce search term to prevent excessive API calls
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedToolsSearchQuery(toolsSearchQuery);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [toolsSearchQuery]);

  // Reset pagination when debounced search term changes
  useEffect(() => {
    setCurrentPage(1);
    // Reset the query to fetch with the new search term
    if (addToolsModal) {
      queryClient.invalidateQueries({
        queryKey: ["mcps", currentPage, debouncedToolsSearchQuery],
      });
    }
  }, [debouncedToolsSearchQuery, queryClient]);
  const {
    data: quickToolsData,
    isLoading: isQuickToolsLoading,
  } = useQuery({
    queryKey: ["quicktool"],
    queryFn: () => mcpApi.getMcpServersByUser(1, 60, true),
  });

  // Filtered tools from query data - now using API-based filtering
  // const filteredTools =
  //   currentPage === 1 ? mcpData?.data || [] : allFetchedData;

  // Workflow store
  const {
    setOpenWorkflowStartingForm,
    setCurrentWorkflow,
    setCurrentWorkflowName,
    setWorkflowValues,
    setWorkflowSteps,
    activeCorrelationId,
  } = useWorkflowStore();

  const {
    fileInputRef,
    isUploading,
    uploadedFiles,
    triggerFileSelect,
    handleFileChange,
    removeFile,
    acceptValue,
    uploadFiles,
    validateFile,
  } = useMultiFileUpload({
    acceptedFormats: ".pdf,.doc,.docx,image/png,image/jpeg,image/jpg",
    maxCount: 5,
    maxSizeMB: 1,
    filePath: "chat-uploads",
    customSuccessMessage: "Files uploaded successfully!",
    customErrorMessage: "Failed to upload files.",
    onError: (error) => {
      toast.error(`File upload failed. Please try again.`);
    },
  });

  //chat input placeholder text
  const [placeholderText, setPlaceholderText] = useState("");
  const [slide, setSlide] = useState(false);

  // Speech recognition
  const [isDictating, setIsDictating] = useState(false);
  const {
    transcript,
    resetTranscript,
    browserSupportsSpeechRecognition,
    isMicrophoneAvailable,
  } = useSpeechRecognition();

  // Quick Tools state
  const [quickTools, setQuickTools] = useState<any[]>([]);
  const [selectedQuickTool, setSelectedQuickTool] = useState<any | null>(null);
  const [quickToolsSearch, setQuickToolsSearch] = useState("");
  const [filteredQuickTools, setFilteredQuickTools] = useState<any[]>([]);
  // Agent selection palette logic
  const [selectedAgentForMention, setSelectedAgentForMention] =
    useState<Employee | null>(null);
  const [isMentionOpen, setIsMentionOpen] = useState(false);
  const [mentionQuery, setMentionQuery] = useState("");
  const [mentionIndex, setMentionIndex] = useState(0);
  const [removeQuickToolLoader, setRemoveQuickToolLoader] = useState(false);
  const [removeQuickToolId, setRemoveQuickToolId] = useState("");
  const isGlobalChat = employee?.id === "global";
  const router = useRouter();
  const track = useMixpanelTrack();
  const [globalTaskQuickTools, setGlobalTaskQuickTools] = useState<any[]>([]);
  const [expandedTools, setExpandedTools] = useState<Record<string, boolean>>(
    {}
  );

  useEffect(() => {
    if (isGlobalChat && selectedMode === AIChatMode.ACT) {
      setGlobalTaskQuickTools(quickToolsData?.data || []);
    }
  }, [quickToolsData, selectedMode]);

  const { data: agentsResponse } = useQuery({
    queryKey: ["agents"],
    queryFn: () => agentApi.getAgents(1, 20, false, false),
    enabled: isGlobalChat,
  });

  // Filter agents by mentionQuery
  const agents = agentsResponse?.data || [];
  const filteredAgents: any[] = mentionQuery
    ? agents.filter((a: any) =>
        a.name.toLowerCase().includes(mentionQuery.toLowerCase())
      )
    : agents;

  // Filter quick tools by search query

  useEffect(() => {
    const filteredQuickToolsResult = quickToolsSearch
      ? quickTools
          .map((tool: any) => {
            // Check if the search query matches the tool name
            const toolNameMatches = sanitizeString(tool.name)
              .toLowerCase()
              .includes(quickToolsSearch.toLowerCase());

            // Filter subTools that match the search query
            const matchingSubTools =
              tool.subTools?.filter((subTool: any) =>
                sanitizeString(subTool.name)
                  .toLowerCase()
                  .includes(quickToolsSearch.toLowerCase())
              ) || [];

            // If tool name matches, return all subTools
            if (toolNameMatches) {
              return tool;
            }
            // If any subTool matches, return the tool with only matching subTools
            else if (matchingSubTools.length > 0) {
              return {
                name: tool?.name,
                integrations: tool?.integrations,
                is_connected: tool?.is_connected,
                id: tool?.id,
                subTools: matchingSubTools,
              };
            }
            // If neither matches, return null
            return null;
          })
          .filter(Boolean) // Remove null entries
      : quickTools;
    setFilteredQuickTools(filteredQuickToolsResult);
  }, [quickToolsSearch, quickTools]);

  // --- Palette item refs for scrolling ---
  const workflowItemRefs = useRef<(HTMLDivElement | null)[]>([]);
  const agentItemRefs = useRef<(HTMLDivElement | null)[]>([]);

  //chnaging input field placeholder text according to selected chat mode
  useEffect(() => {
    setIsWorkflowStarted(false);
    if (employee?.id === "global" && !chatStarted) {
      const intervalId = globalAgentPlaceholderText(
        setPlaceholderText,
        setSlide,
        selectedMode
      );
      return () => clearInterval(intervalId);
    } else {
      setPlaceholderText(
        `Start chatting with ${employee?.name || "RUH AI Agent"} here...`
      );
    }
  }, [employee, selectedMode]);

  // Workflow palette logic
  useEffect(() => {
    if (
      input.startsWith("/") &&
      input.length > 0 &&
      (input.length === 1 || input[1] !== " ")
    ) {
      const query = input.substring(1);

      const filtered = workflows.filter((wf) =>
        wf.name.toLowerCase().includes(query.toLowerCase())
      );
      if (filtered.length > 0) {
        setFilteredWorkflows(filtered);
        setIsPaletteOpen(true);
      } else {
        setIsPaletteOpen(false);
        setFilteredWorkflows([]);
      }
    } else {
      setIsPaletteOpen(false);
      setFilteredWorkflows([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [input, workflows]);

  // Auto-resize textarea on input change
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [input]);

  // Scroll workflow palette selected item into view
  useEffect(() => {
    if (isPaletteOpen && filteredWorkflows.length > 0) {
      const ref = workflowItemRefs.current[selectedWorkflowIndex];
      if (ref) {
        ref.scrollIntoView({ block: "nearest" });
      }
    }
  }, [selectedWorkflowIndex, isPaletteOpen, filteredWorkflows]);

  // Scroll agent mention palette selected item into view
  useEffect(() => {
    if (isMentionOpen && filteredAgents.length > 0) {
      const ref = agentItemRefs.current[mentionIndex];
      if (ref) {
        ref.scrollIntoView({ block: "nearest" });
      }
    }
  }, [mentionIndex, isMentionOpen, filteredAgents]);

  // Handle workflow palette selection
  const handlePaletteItemClick = (workflow: WorkflowInDB) => {
    if (!employee) return;
    setCurrentWorkflow(
      employee.id + "::" + (conversationId || ""),
      workflow.id
    );
    setCurrentWorkflowName(workflow.name);
    setWorkflowValues(workflow.start_nodes || []);
    setWorkflowSteps(workflow.available_nodes);
    setOpenWorkflowStartingForm(true);
    setInput("");
    setIsPaletteOpen(false);
  };

  useEffect(() => {
    if (
      !employee ||
      !Array.isArray(employee.mcp_server_ids) ||
      employee.mcp_server_ids.length === 0
    ) {
      setQuickTools([]);
      return;
    }
    communicationApi
      .fetchQuickTools(employee.mcp_server_ids)
      .then((mcpServers: any) => {
        const allTools = mcpServers.map((server: any) => {
          return {
            id: server.id,
            name: server?.name,
            integrations: server?.integrations,
            is_connected: server?.is_connected,
            subTools: server.mcp_tools_config?.tools?.map((tool: any) => {
              return {
                ...tool,
                id: `${server.id}-${tool.name}`,
                logo: server.logo,
                mcp_server_id: server.id,
              };
            }),
          };
        });
        setQuickTools(allTools);
      })
      .catch((err) => {
        setQuickTools([]);
      });
  }, [employee]);

  const handleQuickToolSelect = (tool: any) => {
    setSelectedQuickTool(tool);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (inputDisabled || isWorkflowStarted) return;
    setThinkingStatusText("Thinking");
    const filesToSend = uploadedFiles.filter((f) => f.status === "uploaded");
    let messageContent = input.trim();
    // Prepare attachments for API
    const apiAttachments = filesToSend.map((f) => ({
      file_name: f.name,
      file_type: f.type,
      file_size: f.size,
      file_url: f.url,
    }));
    // --- Enhanced mention handoff logic for global chat ---

    if (isGlobalChat && onMentionHandoff && selectedAgentForMention) {
      onMentionHandoff({
        message: messageContent,
        attachments: apiAttachments,
        mentionedEmployee: selectedAgentForMention,
      });
      setInput("");
      if (uploadedFiles.length > 0) {
        removeFile("ALL");
      }
      if (textareaRef.current) {
        textareaRef.current.style.height = "auto";
        textareaRef.current.style.height = "40px";
      }
      return;
    }

    // --- Custom onSend logic for / page ---
    if (isGlobalChat && onSend) {
      const tools =
        isGlobalChat && selectedMode === AIChatMode.ACT
          ? globalTaskQuickTools.map((tool) => tool.id)
          : [];
      onSend({
        message: messageContent,
        attachments: apiAttachments,
        tools: tools,
        selectedMode: selectedMode,
      });
      setInput("");
      if (uploadedFiles.length > 0) {
        removeFile("ALL");
      }
      if (textareaRef.current) {
        textareaRef.current.style.height = "auto";
        textareaRef.current.style.height = "40px";
      }
      return;
    }
    // Prepend tool name if selected (employee chat only)
    if (!isGlobalChat && selectedQuickTool) {
      messageContent = `Use this ${selectedQuickTool.name} tool : ${messageContent}`;
    }

    if (filesToSend.length > 0 || messageContent.length > 0) {
      clearDiscoveredEmployees();
      addMessage({
        content: messageContent || "",
        senderType: SenderType.USER,
        attachments: filesToSend.map((f) => ({
          file_url: f.url,
          file_name: f.name,
          file_type: f.type,
          file_size: f.size,
        })),
      });
    }
    setInput("");
    if (uploadedFiles.length > 0) {
      removeFile("ALL");
    }
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto";
      textareaRef.current.style.height = "40px";
    }
    if (sessionId && (messageContent || apiAttachments.length > 0)) {
      // Check credits before sending message
      const hasCredits = await communicationApi.checkCreditsBeforeSending();
      if (!hasCredits) {
        openOutOfCreditsModal();
        setTyping(false);
        return;
      }

      const mode = employee?.id === "global" ? selectedMode : undefined;
      // For global chat task mode, use all globalTaskQuickTools IDs
      const tools =
        isGlobalChat && selectedMode === AIChatMode.ACT
          ? globalTaskQuickTools.map((tool) => tool.id)
          : [];
      const resource =
        employee?.id === "global" && selectedMode === AIChatMode.ASK
          ? selectedSource
          : undefined;
      try {
        await communicationApi.sendMessage(
          messageContent,
          sessionId,
          apiAttachments,
          mode,
          tools,
          resource
        );
        if (employee?.id === "global") {
          queryClient.invalidateQueries({ queryKey: ["globalChatHistory"] });
        }
      } catch (err: any) {
        addMessage({
          content:
            err.message || "An error occurred while sending the message.",
          senderType: SenderType.USER,
          isError: true,
        });
        setTyping(false);
      }
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (
      (isPaletteOpen && filteredWorkflows.length > 0) ||
      (isMentionOpen && filteredAgents.length > 0)
    ) {
      if (event.key === "ArrowDown") {
        event.preventDefault();
        if (isPaletteOpen) {
          setSelectedWorkflowIndex(
            (prev) => (prev + 1) % filteredWorkflows.length
          );
        } else {
          setMentionIndex((prev) => (prev + 1) % filteredAgents.length);
        }
      } else if (event.key === "ArrowUp") {
        event.preventDefault();
        if (isPaletteOpen) {
          setSelectedWorkflowIndex(
            (prev) =>
              (prev - 1 + filteredWorkflows.length) % filteredWorkflows.length
          );
        } else {
          setMentionIndex(
            (prev) => (prev - 1 + filteredAgents.length) % filteredAgents.length
          );
        }
      } else if (event.key === "Enter") {
        event.preventDefault();
        if (isPaletteOpen) {
          const selectedWorkflow = filteredWorkflows[selectedWorkflowIndex];
          if (selectedWorkflow) {
            handlePaletteItemClick(selectedWorkflow);
            setIsPaletteOpen(false);
          }
        } else {
          handleMentionSelect(filteredAgents[mentionIndex] as Employee);
          setIsMentionOpen(false);
          return;
        }
      } else if (event.key === "Escape") {
        event.preventDefault();
        if (isPaletteOpen) {
          setIsPaletteOpen(false);
        } else {
          setIsMentionOpen(false);
        }
      }
    } else if (event.key === "Enter" && !event.shiftKey) {
      const date = new Date();
      track(AnalyticsEvents.GLOBAL_CHAT_USER_QUERY_ENTER, {
        location: window.location.href,
        timestamp: date.toString(),
        mode: selectedMode,
      });
      event.preventDefault();
      if (!isUploading) {
        handleSubmit(event);
      }
    }
  };

  const handleStartDictation = () => {
    if (!browserSupportsSpeechRecognition || !isMicrophoneAvailable) return;
    setIsDictating(true);
    resetTranscript();
    SpeechRecognition.startListening({ continuous: true, language: "en-US" });
  };

  const handleConfirmDictation = () => {
    SpeechRecognition.stopListening();
    setInput(transcript);
    setIsDictating(false);
    resetTranscript();
  };

  const handleCancelDictation = () => {
    SpeechRecognition.stopListening();
    setIsDictating(false);
    resetTranscript();
  };

  const handleSeachTools = (term: string) => {
    setToolsSearchQuery(term);
    // When search term changes, we need to refetch with the new term
    if (addToolsModal) {
      setCurrentPage(1);
      setAllFetchedData([]);
    }
  };

  // Agent selection palette logic
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    // Detect @ mention
    const cursor = e.target.selectionStart;
    const textBefore = value.slice(0, cursor);
    const match = /@([\w]*)$/.exec(textBefore);
    if (isGlobalChat && match) {
      setIsMentionOpen(true);
      setMentionQuery(match[1] || "");
      setMentionIndex(0);
    } else {
      setIsMentionOpen(false);
      setMentionQuery("");
    }
  };
  // Handle mention select
  const handleMentionSelect = (agent: Employee) => {
    if (!textareaRef.current) return;
    const cursor = textareaRef.current.selectionStart;
    const value = input;
    const textBefore = value.slice(0, cursor);
    const textAfter = value.slice(cursor);
    const match = /@([\w]*)$/.exec(textBefore);
    if (match) {
      const start = match.index;
      // const chatBeforeMentionAgent = textBefore.slice(0, start);
      const newText = textBefore.slice(0, start) + `@${agent.name}` + textAfter;
      setInput(newText);
      setIsMentionOpen(false);
      setMentionQuery("");
      setMentionIndex(0);
      setSelectedAgentForMention(agent);
    }
  };

  // Handle paste event for files/images
  const handlePaste = async (e: React.ClipboardEvent<HTMLTextAreaElement>) => {
    const items = e.clipboardData.items;
    const files: File[] = [];
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.kind === "file") {
        const file = item.getAsFile();
        if (file) files.push(file);
      }
    }
    if (files.length > 0) {
      e.preventDefault();
      // Validate all files, keep only valid ones
      const validFiles: File[] = [];
      files.forEach((file) => {
        const validation = validateFile(file);
        if (!validation.valid) {
          toast.error(validation.message || `Invalid file: ${file.name}`);
        } else {
          validFiles.push(file);
        }
      });
      if (validFiles.length === 0) return;
      await uploadFiles(validFiles);
    }
  };

  // Helper function to get source display name
  const getSourceDisplayName = (
    source: SearchModeResources
  ): { name: string; icon: React.JSX.Element } => {
    switch (source) {
      case SearchModeResources.ALL:
        return {
          name: "All Sources",
          icon: <BookOutlineIcon className="!h-4 !w-4" />,
        };
      case SearchModeResources.ORGANIZATION:
        return {
          name: "Company",
          icon: <Building2Icon className="!h-4 !w-4" />,
        };
      case SearchModeResources.RESEARCH:
        return {
          name: "Global",
          icon: <Globe className="!h-4 !w-4" />,
        };
      default:
        return {
          name: "All Sources",
          icon: <BookOutlineIcon width={18} height={18} />,
        };
    }
  };

  // Add global keyboard event listener for alphabet keys
  useEffect(() => {
    const handleGlobalKeyDown = (event: KeyboardEvent) => {
      // Check if the pressed key is an alphabet character
      const isAlphabetKey = /^[a-zA-Z]$/.test(event.key);

      // Only focus if:
      // 1. It's an alphabet key
      // 2. No modifier keys are pressed (Ctrl, Alt, Meta)
      // 3. The target is not an input, textarea, or contenteditable element
      // 4. The chat input is not disabled
      // 5. No modal/palette is open
      if (
        isAlphabetKey &&
        !event.ctrlKey &&
        !event.altKey &&
        !event.metaKey &&
        !inputDisabled &&
        !isDictating &&
        !isWorkflowStarted &&
        !isPaletteOpen &&
        !isMentionOpen &&
        !isMaximizeModalOpen &&
        !addToolsModal
      ) {
        const target = event.target as HTMLElement;
        const isInputElement =
          target.tagName === "INPUT" ||
          target.tagName === "TEXTAREA" ||
          target.isContentEditable ||
          target.closest('[contenteditable="true"]');

        if (!isInputElement && textareaRef.current) {
          event.preventDefault();
          textareaRef.current.focus();
          // Set the cursor to the end and add the typed character
          const currentValue = input;
          const newValue = currentValue + event.key;
          setInput(newValue);

          // Use setTimeout to ensure the value is set before focusing
          setTimeout(() => {
            if (textareaRef.current) {
              textareaRef.current.setSelectionRange(
                newValue.length,
                newValue.length
              );
            }
          }, 0);
        }
      }
    };

    // Add the event listener to the document
    document.addEventListener("keydown", handleGlobalKeyDown);

    // Cleanup on unmount
    return () => {
      document.removeEventListener("keydown", handleGlobalKeyDown);
    };
  }, [
    input,
    inputDisabled,
    isDictating,
    isWorkflowStarted,
    isPaletteOpen,
    isMentionOpen,
    isMaximizeModalOpen,
    addToolsModal,
  ]);

  const handleStopWorkflow = async () => {
    if (!activeCorrelationId) return;

    try {
      await workflowApi.stopWorkflow({
        correlationId: activeCorrelationId,
      });

      // Mark workflow as cancelled
      setWorkflowCancelled();

      // Remove workflow steps that don't have output
      removeWorkflowStepsWithoutOutput();
    } catch (error: any) {}
  };

  const handleStopStream = async () => {
    if (!sessionId) return;

    try {
      await communicationApi.stopStream(sessionId);
      // Stop typing indicator
      setTyping(false);
    } catch (error: any) {
      // You could add a toast notification here if needed
    }
  };

  const handleStopAction = () => {
    if (isWorkflowStarted && activeCorrelationId) {
      handleStopWorkflow();
    } else if (isStreaming || isTyping) {
      handleStopStream();
    }
  };

  // Show stop button when either workflow is running or AI is responding/streaming
  const shouldShowStopButton =
    (isWorkflowStarted && activeCorrelationId) || isStreaming || isTyping;

  const setSelectedSource = (source: SearchModeResources) => {
    setSelectedSourceState(source);
    if (typeof window !== "undefined") {
      localStorage.setItem(LOCAL_STORAGE_KEYS.SELECTED_SOURCE, source);
    }
  };

  const handleRemoveTool = async (data: any) => {
    setRemoveQuickToolLoader(true);
    setRemoveQuickToolId(data.id);
    try {
      await mcpApi.deleteQuickTool(data.id);
      setGlobalTaskQuickTools(
        globalTaskQuickTools.filter((tool) => tool.id !== data.id)
      );
      queryClient.invalidateQueries({ queryKey: ["mcps"] });
      toast.success(`${data.name} tool removed successfully`);
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setRemoveQuickToolLoader(false);
      setRemoveQuickToolId("");
    }
  };
  const handleAddTool = async (data: any) => {
    setRemoveQuickToolLoader(true);
    setRemoveQuickToolId(data.id);
    try {
      await mcpApi.addQuickTool(data.id);
      setGlobalTaskQuickTools([...globalTaskQuickTools, data]);
      queryClient.invalidateQueries({ queryKey: ["mcps"] });
      toast.success(`${data.name} tool added successfully`);
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setRemoveQuickToolLoader(false);
      setRemoveQuickToolId("");
    }
  };

  return (
    <div className={cn("w-full relative", className)}>
      {/* Overlay fade effect above prompt */}
      <div
        className="hidden md:block"
        style={{
          position: "absolute",
          top: -75,
          left: 0,
          right: 0,
          height: 75,
          pointerEvents: "none",
          zIndex: 10,
          background: !!chatStarted
            ? "linear-gradient(to top, var(--background), rgba(250,250,250,0))"
            : "",
        }}
      />
      {/* Attached Files */}
      {uploadedFiles.length > 0 && (
        <div className="mb-2 md:mb-3 flex flex-wrap gap-2 border-gray-200 rounded-lg p-2 bg-gray-50">
          {uploadedFiles.map((file) => (
            <Badge
              key={file.id}
              variant="outline"
              className="flex items-center gap-2 px-2 py-1 md:px-3 md:py-1 relative hover:shadow-md hover:border-primary transition-all"
            >
              {/* Preview for images */}
              {file.type.startsWith("image/") && file.status === "uploaded" && (
                <img
                  src={file.url}
                  alt={file.name}
                  className="h-6 w-6 md:h-8 md:w-8 rounded object-cover"
                />
              )}
              {/* Loader for uploading */}
              {file.status === "uploading" && (
                <span className="w-6 h-6 md:w-8 md:h-8 flex items-center justify-center">
                  <svg
                    className="animate-spin h-4 w-4 md:h-5 md:w-5 text-primary"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                      fill="none"
                    />
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8v8z"
                    />
                  </svg>
                </span>
              )}
              {/* Card for docs */}
              {file.type.startsWith("application/") &&
                file.status === "uploaded" && (
                  <span className="flex items-center gap-1">
                    <FileText className="h-4 w-4" />
                    <span className="text-xs max-w-[80px] md:max-w-xs truncate">
                      {file.name}
                    </span>
                  </span>
                )}
              {/* Error state */}
              {file.status === "error" && (
                <span className="text-xs text-red-500">Upload failed</span>
              )}
              {/* Remove/cancel button */}
              <Button
                variant="linkSecondary"
                onClick={() => removeFile(file.id)}
                className="ml-1 h-5 w-5 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}

      <div
        className={`p-1.5 md:p-2.5 ${
          selectedMode === AIChatMode.ASK
            ? "bg-brand-secondary/10"
            : "bg-brand-primary/10"
        } rounded-xl tour-chat-input`}
      >
        <div
          className={`relative rounded-md md:rounded-[8px] border ${
            selectedMode === AIChatMode.ASK
              ? "border-brand-secondary"
              : "border-brand-primary"
          } bg-background p-1 md:p-2`}
        >
          {/* Workflow Palette */}
          {isPaletteOpen && filteredWorkflows.length > 0 && (
            <div className="absolute left-0 right-0 z-50 bottom-full mb-2 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 md:max-h-60 overflow-y-auto max-h-[100px] md:max-h-full">
              {filteredWorkflows.map((wf, index) => (
                <div
                  key={wf.id}
                  ref={(el) => {
                    workflowItemRefs.current[index] = el;
                  }}
                  onClick={() => handlePaletteItemClick(wf)}
                  className={`flex items-center gap-3 px-3 py-2 md:px-4 md:py-2 cursor-pointer hover:bg-brand-primary/10 ${
                    index === selectedWorkflowIndex ? "bg-brand-primary/10" : ""
                  }`}
                >
                  {sanitizeString(wf.name)}
                </div>
              ))}
            </div>
          )}
          {/* Agent selection palette */}
          {isMentionOpen && filteredAgents.length > 0 && (
            <div className="absolute left-0 right-0 z-50 bottom-full mb-2 bg-white border border-gray-200 rounded-lg shadow-lg max-h-48 md:max-h-60 overflow-y-auto">
              {filteredAgents.map((agent, idx) => (
                <div
                  key={agent.id}
                  ref={(el) => {
                    agentItemRefs.current[idx] = el;
                  }}
                  className={`flex items-center gap-3 px-3 py-2 md:px-4 md:py-2 cursor-pointer hover:bg-brand-primary/10 ${
                    idx === mentionIndex ? "bg-brand-primary/10" : ""
                  }`}
                  onClick={() =>
                    handleMentionSelect(agent as unknown as Employee)
                  }
                >
                  <img
                    src={agent.avatar}
                    alt={agent.name}
                    className="h-8 w-8 rounded-full object-cover border border-gray-200"
                  />
                  <div className="flex flex-col">
                    <span className="font-medium text-sm">{agent.name}</span>
                  </div>
                </div>
              ))}
            </div>
          )}
          <form
            onSubmit={(e) => {
              const date = new Date();
              track(AnalyticsEvents.GLOBAL_CHAT_USER_QUERY_SENT, {
                location: window.location.href,
                timestamp: date.toString(),
                mode: selectedMode,
              });
              handleSubmit(e);
            }}
          >
            <div className="flex flex-col gap-2">
              {/* Row 1: Textarea or Dictation Visualizer */}
              <div className="w-full relative">
                {isDictating ? (
                  <>
                    {/* Blurred background overlay */}
                    <div className="absolute inset-0 bg-white/80 backdrop-blur-sm rounded-lg z-10"></div>

                    {/* Dictation UI - prominently displayed */}
                    <div className="absolute inset-0 flex items-center justify-between z-20 rounded-lg px-2 md:px-4">
                      {/* Voice visualizer bar spanning the entire width */}
                      <div className="flex-1 flex items-end gap-[1px] h-8 justify-center mr-2 md:mr-4">
                        {[...Array(40)].map((_, i) => (
                          <div
                            key={i}
                            className="flex-1 max-w-[3px] rounded-full bg-brand-primary/70 animate-pulse"
                            style={{
                              height: `${
                                8 +
                                Math.abs(Math.sin(Date.now() / 150 + i * 0.2)) *
                                  16
                              }px`,
                              animationDelay: `${i * 0.03}s`,
                              animationDuration: "0.8s",
                            }}
                          />
                        ))}
                      </div>

                      {/* Cancel and Confirm icons - subtle styling */}
                      <div className="flex items-center gap-1 md:gap-2">
                        <button
                          type="button"
                          onClick={handleCancelDictation}
                          aria-label="Cancel dictation"
                          className="p-1.5 rounded text-gray-400 hover:text-gray-600 focus:outline-none transition-colors"
                          tabIndex={0}
                        >
                          <X className="h-5 w-5" />
                        </button>
                        <button
                          type="button"
                          onClick={handleConfirmDictation}
                          aria-label="Confirm dictation"
                          className="p-1.5 rounded text-gray-400 hover:text-gray-600 focus:outline-none transition-colors"
                          tabIndex={0}
                        >
                          <Check className="h-5 w-5" />
                        </button>
                      </div>
                    </div>

                    {/* Hidden textarea (blurred in background) */}
                    <div className="blur-sm pointer-events-none">
                      <div className="flex items-start w-full gap-2 md:gap-3">
                        <EnhancePromptButton
                          inputText={input}
                          onTextUpdate={(enhancedText) => {
                            setInput(enhancedText);
                            if (textareaRef.current) {
                              textareaRef.current.style.height = "auto";
                              textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
                            }
                          }}
                          mode={selectedMode}
                          disabled={true}
                        />
                        <div className="flex-1 relative">
                          <textarea
                            ref={textareaRef}
                            value={input}
                            rows={1}
                            className="h-10 max-h-30 w-full p-2 pr-10 border-0 bg-transparent text-sm md:text-base focus-visible:ring-0 focus-visible:ring-offset-0 shadow-none outline-0 resize-none overflow-y-auto"
                            style={{ maxHeight: "120px" }}
                            disabled={true}
                            placeholder="Listening..."
                          />
                        </div>
                      </div>
                    </div>
                  </>
                ) : (
                  <div className="flex items-start w-full gap-2 md:gap-3">
                    <EnhancePromptButton
                      inputText={input}
                      onTextUpdate={(enhancedText) => {
                        setInput(enhancedText);
                        // Auto-resize textarea after setting new content
                        if (textareaRef.current) {
                          textareaRef.current.style.height = "auto";
                          textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
                        }
                      }}
                      mode={selectedMode}
                      disabled={
                        isUploading ||
                        isDictating ||
                        inputDisabled ||
                        isWorkflowStarted
                      }
                    />
                    <div className="flex-1 relative">
                      <textarea
                        ref={textareaRef}
                        value={input}
                        onChange={(e) => {
                          setInput(e.target.value);
                          e.target.style.height = "auto";
                          e.target.style.height = `${e.target.scrollHeight}px`;
                          handleInputChange(e); // adding to catch @ mention
                        }}
                        rows={1}
                        onKeyDown={handleKeyDown}
                        onPaste={handlePaste}
                        placeholder=""
                        className="h-10 max-h-30 w-full p-2 pr-10 border-0 bg-transparent text-sm md:text-base focus-visible:ring-0 focus-visible:ring-offset-0 shadow-none outline-0 resize-none overflow-y-auto"
                        style={{ maxHeight: "120px" }}
                        disabled={isDictating}
                      />
                      {/* adding this for showing placeholder text */}
                      {!input && (
                        <span
                          className={`absolute left-2 top-2 pointer-events-none transition-all duration-600 ${
                            slide
                              ? "-translate-y-4 opacity-0"
                              : "translate-y-0 opacity-60"
                          }`}
                          style={{ zIndex: 1 }}
                        >
                          {inputDisabled
                            ? isStreaming
                              ? "AI is responding..."
                              : "AI is thinking..."
                            : isWorkflowStarted
                              ? "Workflow is running..."
                              : placeholderText}
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
              {/* Row 2: Options */}
              <div
                className={`flex items-center justify-between gap-2 mt-0 w-full ${
                  isDictating ? "blur-sm pointer-events-none" : ""
                }`}
              >
                <div className="flex items-center justify-between w-full">
                  {showAIChatModeDropdown && (
                    <div className="flex flex-row items-center gap-2">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="secondary"
                            size="sm"
                            className={`gap-2 ${
                              selectedMode === AIChatMode.ASK
                                ? "bg-brand-secondary/5"
                                : "bg-brand-primary/5"
                            } border-0 text-accent-foreground shadow-none tour-task-search-dropdown`}
                          >
                            {selectedMode === AIChatMode.ACT ? (
                              <div className="flex flex-row items-center gap-3">
                                <SettingsOutlineIcon className="!h-4 !w-4" />
                                <span className="font-satoshi-regular text-text-primary text-sm">
                                  Task
                                </span>
                              </div>
                            ) : (
                              <div className="flex flex-row items-center gap-3">
                                <BookOutlineIcon className="!h-4 !w-4" />
                                <span className="font-satoshi-regular text-text-primary text-sm">
                                  Search
                                </span>
                              </div>
                            )}

                            <ChevronDown className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="start" className="p-0">
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedMode(AIChatMode.ACT);
                              localStorage.setItem(
                                LOCAL_STORAGE_KEYS.SELECTED_MODE,
                                AIChatMode.ACT
                              );
                            }}
                            className={`px-3 py-2 cursor-pointer hover:bg-brand-card-hover rounded-none ${
                              selectedMode === AIChatMode.ACT
                                ? "bg-brand-primary/10"
                                : ""
                            }`}
                          >
                            <div className="flex flex-col w-full">
                              <div className="flex flex-row items-start gap-3">
                                <SettingsOutlineIcon className="!h-4 !w-4 mt-1" />
                                <div className="flex flex-col max-w-[300px] md:max-w-full">
                                  <span className="font-satoshi-regular text-text-primary">
                                    Task
                                  </span>
                                  <span className="text-xs text-text-secondary font-satoshi-regular">
                                    Execute tasks using your AI employees
                                    (default mode).
                                  </span>
                                </div>
                              </div>
                            </div>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedMode(AIChatMode.ASK);
                              localStorage.setItem(
                                LOCAL_STORAGE_KEYS.SELECTED_MODE,
                                AIChatMode.ASK
                              );
                            }}
                            className={`px-3 py-2 cursor-pointer hover:bg-brand-card-hover rounded-none ${
                              selectedMode === AIChatMode.ASK
                                ? "bg-brand-primary/10"
                                : ""
                            }`}
                          >
                            <div className="flex flex-col w-full">
                              <div className="flex flex-row items-start gap-3">
                                <BookOutlineIcon className="!h-4 !w-4 mt-1" />
                                <div className="flex flex-col max-w-[300px] md:max-w-full">
                                  <span className="font-satoshi-regular text-text-primary">
                                    Search
                                  </span>
                                  <span className="text-xs text-text-secondary font-satoshi-regular">
                                    Search your knowledge base and connected
                                    sources for contextual answers.
                                  </span>
                                </div>
                              </div>
                            </div>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                      <CustomTooltip description="Mention AI Employee and Assign Tasks">
                        <Button
                          variant="linkSecondary"
                          className="!p-0 !h-auto !w-auto"
                          size="sm"
                          type="button"
                          onClick={() => {
                            let newValue = input;
                            if (!input) {
                              newValue = "@";
                            } else if (input.endsWith(" ")) {
                              newValue = input + "@";
                            } else {
                              newValue = input + " @";
                            }
                            setInput(newValue);
                            setTimeout(() => {
                              if (textareaRef.current) {
                                textareaRef.current.focus();
                                textareaRef.current.setSelectionRange(
                                  newValue.length,
                                  newValue.length
                                ); // Cursor after new '@'
                                // Create a synthetic event to trigger handleInputChange
                                const event = {
                                  target: {
                                    value: newValue,
                                    selectionStart: newValue.length,
                                  },
                                } as React.ChangeEvent<HTMLTextAreaElement>;
                                handleInputChange(event);
                              }
                            }, 0);
                          }}
                        >
                          <AtSignIcon width={20} height={20} />
                        </Button>
                      </CustomTooltip>
                    </div>
                  )}
                  <div className="flex items-center">
                    {showAIChatModeDropdown &&
                      selectedMode === AIChatMode.ASK && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="linkSecondary"
                              size="sm"
                              className="gap-2"
                            >
                              {getSourceDisplayName(selectedSource)?.icon}
                              <span className="hidden sm:inline font-satoshi-regular text-sm text-text-primary">
                                {getSourceDisplayName(selectedSource)?.name}
                              </span>
                              <ChevronDown className="hidden sm:inline h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent
                            align="end"
                            className="w-auto p-0"
                          >
                            <DropdownMenuItem
                              onClick={() =>
                                setSelectedSource(SearchModeResources.ALL)
                              }
                              className={`px-3 py-2 cursor-pointer hover:bg-brand-card-hover rounded-none ${
                                selectedSource === SearchModeResources.ALL
                                  ? "bg-brand-primary/10"
                                  : ""
                              }`}
                            >
                              <div className="flex flex-row items-start gap-3 w-full">
                                <BookOutlineIcon className="!h-4 !w-4 mt-1" />
                                <div className="flex flex-col max-w-[300px] md:max-w-full">
                                  <div className="flex flex-row items-center justify-between">
                                    <span className="text-sm font-satoshi-regular text-text-primary">
                                      All Sources
                                    </span>
                                  </div>
                                  <span className="text-xs text-text-secondary font-satoshi-regular">
                                    Includes both your internal knowledge base
                                    and relevant web results.
                                  </span>
                                </div>
                              </div>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                setSelectedSource(
                                  SearchModeResources.ORGANIZATION
                                )
                              }
                              className={`px-3 py-2 cursor-pointer hover:bg-brand-card-hover rounded-none ${
                                selectedSource ===
                                SearchModeResources.ORGANIZATION
                                  ? "bg-brand-primary/10"
                                  : ""
                              }`}
                            >
                              <div className="flex flex-row items-start gap-3 w-full">
                                <Building2Icon className="!h-4 !w-4 mt-1" />
                                <div className="flex flex-col max-w-[300px] md:max-w-full">
                                  <div className="flex flex-row items-center justify-between">
                                    <span className="text-sm font-satoshi-regular text-text-primary">
                                      Company
                                    </span>
                                  </div>
                                  <span className="text-xs text-text-secondary font-satoshi-regular">
                                    Uses only your internal knowledge base and
                                    organizational context
                                  </span>
                                </div>
                              </div>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() =>
                                setSelectedSource(SearchModeResources.RESEARCH)
                              }
                              className={`px-3 py-2 cursor-pointer hover:bg-brand-card-hover rounded-none ${
                                selectedSource === SearchModeResources.RESEARCH
                                  ? "bg-brand-primary/10"
                                  : ""
                              }`}
                            >
                              <div className="flex flex-row items-start gap-3 w-full">
                                <Globe className="!h-4 !w-4 mt-1" />
                                <div className="flex flex-col max-w-[300px] md:max-w-full">
                                  <div className="flex flex-row items-center justify-between">
                                    <span className="text-sm font-satoshi-regular text-text-primary">
                                      Global
                                    </span>
                                  </div>
                                  <span className="text-xs text-text-secondary font-satoshi-regular">
                                    Searches the web for results tailored to
                                    your query
                                  </span>
                                </div>
                              </div>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    <input
                      ref={fileInputRef}
                      type="file"
                      multiple
                      accept={acceptValue}
                      onChange={handleFileChange}
                      className="hidden"
                      disabled={isDictating}
                    />
                    <CustomTooltip description="Attach Files">
                      <Button
                        variant="linkSecondary"
                        onClick={triggerFileSelect}
                        disabled={isDictating}
                        type="button"
                      >
                        <Paperclip className="h-4 w-4" />
                      </Button>
                    </CustomTooltip>

                    {/*For employee chat -  Quick Tools Icon triggers dropdown, selected value is separate with cancel */}
                    {!showAIChatModeDropdown && quickTools.length > 0 && (
                      <div className="flex flex-row items-center gap-2">
                        <DropdownMenu>
                          <CustomTooltip
                            title="Quick Tools"
                            side="bottom"
                            description="Add third-party tools for faster task execution and instant results."
                          >
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="linkSecondary"
                                className="gap-2"
                                disabled={isDictating}
                              >
                                <QuickToolsIcon width={20} height={20} />
                                {!isMobile && selectedQuickTool === null && (
                                  <span className="text-sm">Quick Tools</span>
                                )}
                              </Button>
                            </DropdownMenuTrigger>
                          </CustomTooltip>
                          <DropdownMenuContent
                            align="start"
                            className="min-w-[250px] p-0"
                          >
                            <Command>
                              <span
                                onClick={() =>
                                  router.push("/user-settings/manage-tools")
                                }
                                className="text-sm text-secondary cursor-pointer flex justify-end w-full px-2 pt-1"
                              >
                                Manage
                              </span>
                              <CommandInput
                                placeholder="Search quick tools..."
                                value={quickToolsSearch}
                                onValueChange={setQuickToolsSearch}
                                className="!py-1"
                              />
                              {/* <CommandList>
                                <CommandGroup> */}
                              {!!filteredQuickTools?.length ? (
                                filteredQuickTools.map((tool: any) => {
                                  return (
                                    <div key={tool.id}>
                                      <div
                                        onClick={() =>
                                          setExpandedTools((prev) => ({
                                            ...prev,
                                            [tool.id]: !prev[tool.id],
                                          }))
                                        }
                                        className={`cursor-pointer bg-border-muted px-2 py-3 my-1 rounded-[4px]  min-h-[50px] max-h-[50px] mx-1`}
                                      >
                                        <div className="flex justify-between w-full items-center">
                                          <div className="flex flex-col">
                                            <span className="text-[14px] font-satoshi-bold max-w-[200px] truncate">
                                              {tool?.name}
                                            </span>
                                            {tool?.integrations?.length &&
                                              !tool?.is_connected && (
                                                <div className="flex gap-1 items-center">
                                                  <span className="text-[11px] text-error">
                                                    Not Connected
                                                  </span>

                                                  <Cable
                                                    onClick={(e) => {
                                                      e.stopPropagation();
                                                      router.push(
                                                        "/user-settings/manage-tools"
                                                      );
                                                    }}
                                                    className="w-3 h-3 text-error cursor-pointer"
                                                  />
                                                </div>
                                              )}
                                          </div>
                                          <ChevronDown
                                            className={`h-4 w-4 transition-transform ${expandedTools[tool.id] ? "rotate-180" : ""}`}
                                          />
                                        </div>
                                      </div>

                                      {/* Show subtools when expanded OR when there's a search query */}
                                      {expandedTools[tool.id] && (
                                        <>
                                          {!tool?.subTools ||
                                          tool.subTools.length === 0 ? (
                                            <CommandEmpty>
                                              No tools found.
                                            </CommandEmpty>
                                          ) : (
                                            tool.subTools.map(
                                              (subtool: any) => {
                                                return (
                                                  <div
                                                    key={subtool.id}
                                                    onClick={() => {
                                                      handleQuickToolSelect(
                                                        subtool
                                                      );
                                                    }}
                                                    className={`flex flex-row items-center gap-3 px-3 py-2 cursor-pointer  hover:!bg-gray-50
                                                  ${
                                                    selectedQuickTool &&
                                                    selectedQuickTool.id ===
                                                      subtool.id
                                                      ? "!bg-brand-primary/10 font-semibold"
                                                      : ""
                                                  }
                                                  `}
                                                  >
                                                    {subtool.logo ? (
                                                      <img
                                                        src={subtool.logo}
                                                        alt={subtool.name}
                                                        className="h-6 w-6 rounded-full object-cover"
                                                      />
                                                    ) : (
                                                      <QuickToolsIcon
                                                        width={24}
                                                        height={24}
                                                      />
                                                    )}
                                                    <span className="text-xs font-medium text-left">
                                                      {sanitizeString(
                                                        subtool.name
                                                      )}
                                                    </span>
                                                    {selectedQuickTool &&
                                                      selectedQuickTool.id ===
                                                        subtool.id && (
                                                        <Check className="h-4 w-4 text-brand-primary ml-auto" />
                                                      )}
                                                  </div>
                                                );
                                              }
                                            )
                                          )}
                                        </>
                                      )}
                                    </div>
                                  );
                                })
                              ) : (
                                <div className="min-h-[200px]">
                                  <CommandEmpty>No tools found.</CommandEmpty>
                                </div>
                              )}
                            </Command>
                          </DropdownMenuContent>
                        </DropdownMenu>

                        {!isMobile && selectedQuickTool && (
                          <div className="flex flex-row items-center gap-1 bg-brand-primary/10 rounded px-2 py-1 border border-brand-primary">
                            <span className="text-sm font-medium">
                              {sanitizeString(selectedQuickTool.name)}
                            </span>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => setSelectedQuickTool(null)}
                              className="h-6 w-6 p-0 ml-1"
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>
                    )}

                    {showAIChatModeDropdown &&
                      selectedMode === AIChatMode.ACT && (
                        <DropdownMenu>
                          <CustomTooltip
                            title="Quick Tools"
                            side="bottom"
                            description="Add third-party tools for faster task execution and instant results."
                          >
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="linkSecondary"
                                className="gap-2"
                                disabled={isDictating}
                              >
                                <QuickToolsIcon width={20} height={20} />
                                <span className="hidden sm:inline text-sm">
                                  Quick Tools
                                </span>
                              </Button>
                            </DropdownMenuTrigger>
                          </CustomTooltip>
                          <DropdownMenuContent
                            align="end"
                            className="p-3 flex flex-col gap-1 justify-center items-center w-[246px]"
                          >
                            <div className="flex justify-between w-full">
                              <span className="text-[16px] font-satoshi-bold">
                                Tools
                              </span>
                              <span
                                onClick={() =>
                                  router.push("/user-settings/manage-tools")
                                }
                                className="text-sm text-secondary cursor-pointer"
                              >
                                Manage
                              </span>
                            </div>
                            <CustomSeparator className="m-0 w-full h-[1px]" />
                            {isQuickToolsLoading ? (
                              <div className="flex gap-2">
                                <LoadingSpinner className="!min-h-[100px]" />
                              </div>
                            ) : (
                              <div className="flex flex-col gap-1 flex-start w-full max-h-[250px] overflow-y-auto scrollbar-hide">
                                {globalTaskQuickTools.length > 0 ? (
                                  globalTaskQuickTools.map((tool) => (
                                    <div
                                      className={`flex flex-col py-2 px-3 w-full min-h-[62px] justify-center hover:bg-brand-card-hover rounded ${tool?.integrations?.length && !tool?.is_connected && "border-red-300 bg-red-100"}`}
                                    >
                                      <div
                                        key={tool.id}
                                        className={`flex flex-row items-center gap-1 cursor-default group`}
                                        style={{ minHeight: 40 }}
                                      >
                                        {tool.logo ? (
                                          <img
                                            key={tool.id}
                                            src={tool.logo}
                                            alt={tool.name}
                                            className="h-4 w-4 rounded-full object-cover mr-2"
                                          />
                                        ) : (
                                          <PackageIcon className="h-4 w-4 mr-2 text-brand-primary" />
                                        )}
                                        <span className="text-sm font-medium flex-1 truncate">
                                          {sanitizeString(tool.name)}
                                        </span>
                                        <Button
                                          variant="ghost"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleRemoveTool(tool);
                                          }}
                                          disabled={
                                            !addToolsModal &&
                                            removeQuickToolLoader
                                          }
                                        >
                                          {removeQuickToolLoader &&
                                          !addToolsModal &&
                                          tool.id == removeQuickToolId ? (
                                            <Loader2 className="h-4 w-4 animate-spin text-primary" />
                                          ) : (
                                            <Trash2Icon className="h-4 w-4" />
                                          )}
                                        </Button>
                                      </div>
                                      {tool?.integrations?.length &&
                                        !tool?.is_connected && (
                                          <div className="flex gap-1 items-center">
                                            <span className="text-[11px] text-error">
                                              Not Connected
                                            </span>

                                            <Cable
                                              onClick={() =>
                                                router.push(
                                                  "/user-settings/manage-tools"
                                                )
                                              }
                                              className="w-3 h-3 text-error cursor-pointer"
                                            />
                                          </div>
                                        )}
                                    </div>
                                  ))
                                ) : (
                                  <div className="flex flex-row items-center gap-2">
                                    <InfoIcon className="h-4 w-4 text-text-secondary" />
                                    <span className="text-sm text-text-secondary">
                                      No tool connected currently{" "}
                                    </span>
                                  </div>
                                )}
                              </div>
                            )}
                            <CustomSeparator className="m-0 w-full h-[1px]" />
                            <Button
                              variant={"gradient"}
                              className="w-full flex gap-2"
                              onClick={() => {
                                // Reset pagination and search when opening modal
                                // setCurrentPage(1);
                                // setAllFetchedData([]);
                                setToolsSearchQuery("");
                                setDebouncedToolsSearchQuery("");
                                // Opening the modal will trigger the query automatically
                                setAddToolsModal(true);
                              }}
                            >
                              <PlusIcon className="h-4 w-4" />
                              Add Tools
                            </Button>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                  </div>
                </div>
                <div className="flex items-center gap-2 ">
                  <CustomTooltip description="Speak Your Query">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={handleStartDictation}
                      aria-label="Start dictation"
                      disabled={isDictating}
                      className="text-black"
                    >
                      <Mic className="h-4 w-4 text-black" />
                    </Button>
                  </CustomTooltip>
                  {/* Show stop button when workflow is running or AI is responding, send button otherwise */}
                  {shouldShowStopButton ? (
                    <CustomTooltip description="Stop process">
                      <Button
                        type="button"
                        variant="ghost"
                        onClick={handleStopAction}
                        className="relative h-9 w-9 rounded-full bg-brand-primary hover:bg-brand-primary/90 border-0 transition-all duration-200 group"
                        size="sm"
                        aria-label="Stop workflow"
                      >
                        <Square className="h-4 w-4 fill-white text-white group-hover:scale-110 transition-transform duration-200" />
                      </Button>
                    </CustomTooltip>
                  ) : (
                    <CustomTooltip description="Send message">
                      <span className="inline-flex">
                        <Button
                          type="submit"
                          variant={"ghost"}
                          disabled={
                            isUploading ||
                            (!input.trim() &&
                              uploadedFiles.filter(
                                (f) => f.status === "uploaded"
                              ).length === 0) ||
                            isDictating
                          }
                          className="h-8 w-8 md:h-9 md:w-9"
                        >
                          <SendHorizonalIcon className="h-4 w-4 text-black" />
                        </Button>
                      </span>
                    </CustomTooltip>
                  )}
                </div>
              </div>
              {isMobile && selectedQuickTool && (
                <div className="flex w-fit flex-row flex-start gap-1 bg-brand-primary/10 rounded px-2 py-1 border border-brand-primary">
                  <span className="text-sm font-medium">
                    {sanitizeString(selectedQuickTool.name)}
                  </span>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setSelectedQuickTool(null)}
                    className="h-6 w-6 p-0 ml-1"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          </form>
        </div>
      </div>
      {/* Maximize Textarea Modal */}
      <MaximizeTextareaModal
        open={isMaximizeModalOpen}
        onClose={() => setIsMaximizeModalOpen(false)}
        value={input}
        onChange={setInput}
        placeholder={
          inputDisabled
            ? isStreaming
              ? "AI is responding..."
              : "AI is thinking..."
            : isWorkflowStarted
              ? "Workflow is running..."
              : `Start chatting with ${employee?.name || "RUH AI Agent"} here...`
        }
        mode={selectedMode}
        disabled={inputDisabled || isDictating || isWorkflowStarted}
      />
      {/* All Tools Modal */}
      <ItemSelectionDialog
        addtoolsFor={"chat"}
        toolLoader={removeQuickToolLoader}
        selectedToolId={removeQuickToolId}
        isOpen={addToolsModal}
        onOpenChange={setAddToolsModal}
        title="All Tools"
        subtitle="Your tools and tool templates from the community"
        searchPlaceholder="Search tools here"
        items={allFetchedData}
        isLoading={isLoadingTools}
        searchTerm={toolsSearchQuery}
        onSearchChange={handleSeachTools}
        addedItemIds={
          isGlobalChat && selectedMode === AIChatMode.ACT
            ? globalTaskQuickTools
            : []
        }
        marketplaceUrl={marketplaceMcpsUrl}
        showRefresh={true}
        onRefresh={async () => {
          setCurrentPage(1);
          setAllFetchedData([]);
          await refetchMcpTools();
          toast.success("Tool list refreshed!");
        }}
        // Load more functionality
        onLoadMore={() => {
          if (hasMoreItems && !isLoadingMore) {
            setIsLoadingMore(true);
            setCurrentPage((prev) => prev + 1);
          }
        }}
        hasMoreItems={hasMoreItems}
        isLoadingMore={isLoadingMore}
        onItemToggle={async (data: any) => {
          if (isGlobalChat && selectedMode === AIChatMode.ACT) {
            if (globalTaskQuickTools.find((tool) => tool.id === data.id)) {
              handleRemoveTool(data);
            } else {
              if (globalTaskQuickTools.length >= 4) {
                toast.error("You can select a maximum of 4 quick tools.");
                return;
              }
              handleAddTool(data);
            }
          }
        }}
        emptyStateIcon={<Package2 className="h-12 w-12 mb-2" />}
        emptyStateText="No tools found"
        renderItemIcon={(tool) =>
          tool.logo ? (
            <Image src={tool.logo} alt={tool.name} width={24} height={24} />
          ) : (
            <PackageIcon className="w-5 h-5 text-blue-600" />
          )
        }
        renderItemSubtext={
          (tool) => ``
          // `by ${sanitizeString(tool.department || "")}`
        }
      />
    </div>
  );
}
