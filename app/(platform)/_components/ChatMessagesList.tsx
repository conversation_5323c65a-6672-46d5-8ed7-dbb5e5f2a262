"use client";

import React, { useEffect, useRef, useCallback, useState } from "react";
import { capitalizeFirstLetter, cn } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { createEmployeeOnDeveloperPortalUrl } from "@/shared/constants";
import {
  EmployeeChatAttachment,
  useEmployeeManagementStore,
  EmployeeChatMessage,
  WorkflowChatMessage,
  DelegationChatMessage,
  CreateEmployeeChatMessage,
  isDelegationChatMessage,
  isCreateEmployeeChatMessage,
} from "@/hooks/useEmployeeManagementStore";
import { SenderType } from "@/shared/enums";
import { Employee } from "@/shared/interfaces";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { AvatarImage } from "@radix-ui/react-avatar";
import PreviewFile from "@/components/shared/PreviewFile";
import { CopyIcon, FileText, Loader2 } from "lucide-react";
import MessageContent from "@/components/shared/MessageContent";
import { Skeleton } from "@/components/ui/skeleton";
import WorkflowStepsCard from "./WorkflowStepsCard"; // Placeholder, to be implemented
import { useWorkflowStore } from "@/hooks/use-workflow";
import { Button } from "@/components/ui/button";
import { communicationApi } from "@/app/api/communication";
import { toast } from "sonner";
import { noTextLogoPath } from "@/shared/constants";
import Image from "next/image";
import SourcesBar from "@/components/shared/SourcesBar";
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import DelegationCard from "./DelegationCard";
import { MCPExecutionIndicator } from "@/components/shared/MCPExecutionIndicator";
import { KnowledgeFetchIndicator } from "@/components/shared/KnowledgeFetchIndicator";
import { createEmployeeRoute } from "@/shared/routes";
import ThinkingBox from "@/components/shared/ThinkingBox";

interface EmployeeEnhancedMessageListProps {
  employee: Employee;
  className?: string;
  isLoading?: boolean;
  onLoadMore?: () => void;
  isLoadingMoreMessages?: boolean;
  hasMoreMessages?: boolean;
}

const isWorkflowMessage = (
  msg: EmployeeChatMessage | WorkflowChatMessage
): msg is WorkflowChatMessage => {
  return (
    (msg as WorkflowChatMessage).type === "workflow" ||
    (msg as WorkflowChatMessage).isWorkflowEvent === true
  );
};

const ChatMessagesList = React.memo(
  ({
    employee,
    className,
    isLoading,
    onLoadMore,
    isLoadingMoreMessages = false,
    hasMoreMessages = false,
  }: EmployeeEnhancedMessageListProps) => {
    const {
      employee: { selectedId },
      chat: { sessions, currentStreamingMessage },
      isWorkflowStarted,
      workflowMessageThinking,
      getDelegationThinkingContent,
      thinkingStatusText,
      updateDelegationApprovalStatus,
      setDelegationLoading,
      mcpExecution,
      knowledgeFetch,
    } = useEmployeeManagementStore();
    const { workflowPayload, getCurrentWorkflow } = useWorkflowStore();
    const session = selectedId ? sessions[selectedId] : null;
    const currentMessages = session?.messages || [];
    const isTyping = session?.isTyping || false;
    const streamState = session?.streamState || {
      isStreaming: false,
      currentMessage: null,
      error: null,
    };
    const delegationThinkingContent = selectedId
      ? getDelegationThinkingContent(selectedId)
      : null;

    const messagesEndRef = useRef<HTMLDivElement>(null);
    const isInitialLoadRef = useRef(true);
    const isUserScrolledUpRef = useRef(false);
    const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const prevMessagesLengthRef = useRef(currentMessages.length);
    const [previewFile, setPreviewFile] =
      useState<null | EmployeeChatAttachment>(null);
    // Delegation thinking states - removed as it's now handled by ThinkingBox component
    const [copiedMessageId, setCopiedMessageId] = useState<string | null>(null);
    const [createEmployeeLoading, setCreateEmployeeLoading] = useState<
      string | null
    >(null);
    const router = useRouter();

    const handleDelegationApprove = useCallback(
      async (employee: Employee, messageId: string) => {
        setDelegationLoading(messageId, true);
        const conversationId = session?.conversationId || "";
        const globalSessionId = session?.sessionId || "";
        try {
          const response = await communicationApi.delegateTask({
            globalChatConversationId: conversationId,
            agentId: employee.id,
            title: employee.assigned_task || "",
            globalSessionId: globalSessionId,
          });
          if (response.success) {
            updateDelegationApprovalStatus(messageId, true);
            toast.success(
              "Task delegated successfully, agent will start executing shortly"
            );
          }
        } catch (error) {
          console.error(error);
          toast.error("Failed to delegate task");
        } finally {
          setDelegationLoading(messageId, false);
        }
      },
      [
        session?.conversationId,
        session?.sessionId,
        setDelegationLoading,
        updateDelegationApprovalStatus,
      ]
    );

    const scrollToBottom = useCallback(
      (behavior: ScrollBehavior = "smooth") => {
        messagesEndRef.current?.scrollIntoView({ behavior });
      },
      []
    );

    const handleScroll = useCallback(() => {
      // Get the parent scroll container (now in EmployeeChatInterface)
      const container = messagesEndRef.current?.closest(".overflow-y-auto");
      if (!container) return;
      const { scrollTop, scrollHeight, clientHeight } = container;
      const threshold = 100; // Pixels from bottom
      isUserScrolledUpRef.current =
        scrollHeight - scrollTop - clientHeight > threshold;
    }, []);

    useEffect(() => {
      // Get the parent scroll container (now in EmployeeChatInterface)
      const container = messagesEndRef.current?.closest(".overflow-y-auto");
      container?.addEventListener("scroll", handleScroll);
      return () => container?.removeEventListener("scroll", handleScroll);
    }, [handleScroll]);

    // This effect detects when a new message from the user is added and unlocks
    // the auto-scroll, ensuring the view scrolls down for their new message.
    useEffect(() => {
      if (currentMessages.length > prevMessagesLengthRef.current) {
        const lastMessage = currentMessages[currentMessages.length - 1];
        if (lastMessage?.senderType === SenderType.USER) {
          isUserScrolledUpRef.current = false;
        }
      }
      prevMessagesLengthRef.current = currentMessages.length;
    }, [currentMessages]);

    // This is the main effect that handles all scrolling logic.
    useEffect(() => {
      // Get the parent scroll container (now in EmployeeChatInterface)
      const container = messagesEndRef.current?.closest(".overflow-y-auto");
      if (!container) return;

      // On initial load, scroll to the bottom instantly.
      if (isInitialLoadRef.current && currentMessages.length > 0) {
        scrollToBottom("instant");
        isInitialLoadRef.current = false;
      }

      // Use a MutationObserver to watch for new messages or streaming content.
      // This is more reliable and performant than using state changes in useEffect.
      const observer = new MutationObserver(() => {
        if (!isUserScrolledUpRef.current) {
          scrollToBottom("smooth");
        }
      });

      // Start observing the messages container for any changes in its children or subtree.
      if (messagesEndRef.current?.parentElement) {
        observer.observe(messagesEndRef.current.parentElement, {
          childList: true, // Catches new messages being added/removed
          subtree: true, // Catches text changes within a streaming message
        });
      }

      // Clean up the observer when the component unmounts.
      return () => observer.disconnect();
    }, [scrollToBottom, currentMessages.length]);

    // Reset flags when the selected employee (and thus the chat) changes.
    useEffect(() => {
      isInitialLoadRef.current = true;
      isUserScrolledUpRef.current = false;
    }, [selectedId]);

    // No-op useEffect to satisfy linter, as we don't need to clean up timeouts anymore
    useEffect(() => {
      return () => {
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      };
    }, []);

    // Handle delegation thinking content streaming - now handled by ThinkingBox component

    const handleCopy = (content: string, messageId: string) => {
      navigator.clipboard.writeText(content);
      setCopiedMessageId(messageId);
      setTimeout(() => setCopiedMessageId(null), 1500);
    };

    const renderMessage = (message: any, index: number) => {
      const isUser = message.senderType === SenderType.USER;
      const content =
        typeof message.content === "string"
          ? message.content
          : message.content?.content || "Message failed ";

      return (
        <div className="flex flex-col gap-2 md:gap-3" key={message.id || index}>
          {!isUser && !message.isSearchMessage && (
            <div className="flex gap-2 md:gap-[9px] items-center">
              <Avatar className="w-8 h-8 md:w-10 md:h-10">
                <AvatarImage src={employee.avatar} alt={employee.name} />
                <AvatarFallback className="border-none bg-transparent">
                  <Image
                    src={noTextLogoPath}
                    alt="Logo"
                    width={32}
                    height={32}
                  />
                </AvatarFallback>
              </Avatar>
              <div className="text-sm text-primary">
                {capitalizeFirstLetter(employee.name)}
              </div>
            </div>
          )}
          <div
            key={message.id}
            className={`flex flex-col gap-2 md:gap-3 ${
              isUser ? "justify-end " : "ml-2 lg:ml-[49px]"
            }`}
          >
            {/* if below code executed than do not execute below code */}
            {message.employeeSearchType &&
              message.searchSources &&
              message.searchSources.length > 0 && (
                <SourcesBar
                  key={"sources-" + (message.id || index)}
                  employeeSearchType={message.employeeSearchType}
                  searchSources={message.searchSources}
                />
              )}
            {(message.content || message.attachments.length > 0) && (
              <div className="relative group">
                <div
                  className={`${
                    isUser
                      ? "max-w-[90%] md:max-w-[70%] bg-user-bubble ml-auto w-fit"
                      : "max-w-full bg-agent-bubble border border-border-default"
                  } ${
                    message.isError
                      ? "border-error bg-red-50/50 text-error"
                      : ""
                  } rounded-xl p-3 md:p-5 md:pr-10`}
                >
                  <div className="flex flex-col gap-3">
                    {/* Attachments row */}
                    {message.attachments && message.attachments.length > 0 && (
                      <div className="flex gap-2 mt-3">
                        {message.attachments.map((file: any, idx: number) => (
                          <div
                            key={`${message.id || index}-attachment-${idx}-${
                              file?.file_name || file?.file_url || idx
                            }`}
                            className="cursor-pointer flex flex-col items-center justify-center border rounded bg-white shadow-sm p-1 w-16 h-16 overflow-hidden"
                            onClick={() => setPreviewFile(file)}
                          >
                            {file?.file_type?.startsWith("image/") ? (
                              <img
                                src={file?.file_url}
                                alt={file?.file_name}
                                className="object-cover w-full h-full rounded"
                              />
                            ) : (
                              <div className="flex flex-col items-center justify-center w-full h-full">
                                <FileText className="h-6 w-6 text-primary mb-1" />
                                <span className="text-[10px] text-center truncate w-full">
                                  {file?.file_name}
                                </span>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                    <MessageContent isUser={isUser} content={content} />
                  </div>
                </div>
                <div className="absolute bottom-0 right-0 flex gap-2 justify-end opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <TooltipProvider>
                    <Tooltip
                      open={copiedMessageId === message.id ? true : undefined}
                    >
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          title="Copy"
                          onClick={() => handleCopy(content, message.id)}
                        >
                          <CopyIcon width={32} height={32} />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent side="top">
                        {copiedMessageId === message.id
                          ? "Copied!"
                          : "Click to copy"}
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            )}
          </div>
        </div>
      );
    };

    const renderStreamingMessage = () => {
      if (!isTyping && !mcpExecution.isExecuting && !knowledgeFetch.isActive) {
        return null;
      }
      return (
        <div key="streaming-message" className="flex gap-2 md:gap-3 flex-col">
          <div className="flex gap-2 md:gap-[9px] items-center">
            <Avatar className="w-8 h-8 md:w-10 md:h-10">
              <AvatarImage src={employee.avatar} alt={employee.name} />
              <AvatarFallback className="border-none bg-transparent">
                <Image src={noTextLogoPath} alt="Logo" width={32} height={32} />
              </AvatarFallback>
            </Avatar>
            <div className="text-sm text-primary">
              {capitalizeFirstLetter(employee.name)}
            </div>
            {renderThinkingBubble()}
          </div>
          <div className="ml-2 lg:ml-[49px]">
            {/* MCP Execution Indicator */}
            {mcpExecution.isExecuting && mcpExecution.toolName && (
              <div className="mb-4">
                <MCPExecutionIndicator
                  toolName={mcpExecution.toolName}
                  logo={mcpExecution.logo || undefined}
                  description={mcpExecution.description || undefined}
                  className="max-w-[80%]"
                />
              </div>
            )}

            {/* Knowledge Fetch Indicator */}
            {knowledgeFetch.isActive && (
              <div className="mb-4">
                <KnowledgeFetchIndicator className="max-w-[80%]" />
              </div>
            )}

            {renderDelegationThinking()}

            {currentStreamingMessage && (
              <>
                {currentStreamingMessage?.employeeSearchType &&
                  currentStreamingMessage?.searchSources &&
                  currentStreamingMessage.searchSources.length > 0 && (
                    <SourcesBar
                      employeeSearchType={
                        currentStreamingMessage.employeeSearchType
                      }
                      searchSources={currentStreamingMessage.searchSources}
                    />
                  )}
                <div className="max-w-full bg-agent-bubble border-border-muted rounded-xl p-3 md:p-5">
                  <MessageContent content={currentStreamingMessage?.content} />
                </div>
              </>
            )}
          </div>
        </div>
      );
    };

    const renderThinkingBubble = () => {
      if (isTyping && !streamState.isStreaming) {
        return (
          <div className="flex items-center text-muted-foreground text-sm ">
            <span className="mr-2">
              {thinkingStatusText || "Preparing response"}
            </span>
            <span className="text-xl animate-bounce [animation-delay:0s] font-satoshi-bold">
              .
            </span>
            <span className="text-xl animate-bounce [animation-delay:0.3s] font-satoshi-bold">
              .
            </span>
            <span className="text-xl animate-bounce [animation-delay:0.6s] font-satoshi-bold">
              .
            </span>
          </div>
        );
      } else if (streamState.isStreaming) {
        return (
          <div className="flex items-center text-muted-foreground text-sm ">
            <span className="mr-2">
              {thinkingStatusText || "Preparing response"}
            </span>
            <span className="text-xl animate-bounce [animation-delay:0s] font-satoshi-bold">
              .
            </span>
            <span className="text-xl animate-bounce [animation-delay:0.3s] font-satoshi-bold">
              .
            </span>
            <span className="text-xl animate-bounce [animation-delay:0.6s] font-satoshi-bold">
              .
            </span>
          </div>
        );
      }
    };

    const renderStreamError = () => {
      if (!streamState.error) return null;

      return (
        <div key="stream-error" className="flex gap-3 ml-2 lg:ml-[49px]">
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-red-500 to-red-600 flex items-center justify-center text-white text-sm font-medium">
            ⚠️
          </div>
          <div className="max-w-[70%] bg-red-50 border border-red-200 rounded-2xl px-4 py-3">
            <div className="text-sm font-medium text-red-800">
              Streaming Error
            </div>
            <div className="text-xs text-red-600 mt-1">{streamState.error}</div>
          </div>
        </div>
      );
    };

    const renderThinkingBubbleForWorkflow = () => {
      return (
        <div key="streaming-message" className="flex gap-3 flex-col  ">
          <div className="flex gap-[9px] items-center">
            <Avatar className="w-10 h-10">
              <AvatarImage src={employee.avatar} alt={employee.name} />
              <AvatarFallback className="bg-gradient-to-br from-purple-500 to-purple-600 text-white text-sm font-medium">
                {employee.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="text-sm text-primary">
              {capitalizeFirstLetter(employee.name)}
            </div>
            <div className="flex items-center text-muted-foreground text-sm ">
              <span className="mr-2">
                {workflowMessageThinking ?? "Workflow executing"}
              </span>
              <span className="text-xl animate-bounce [animation-delay:0s] font-satoshi-bold">
                .
              </span>
              <span className="text-xl animate-bounce [animation-delay:0.3s] font-satoshi-bold">
                .
              </span>
              <span className="text-xl animate-bounce [animation-delay:0.6s] font-satoshi-bold">
                .
              </span>
            </div>
          </div>
        </div>
      );
    };

    const renderWorkflowMessage = (
      message: WorkflowChatMessage,
      isLastMessage: boolean
    ) => {
      return (
        <WorkflowStepsCard
          key={message.id}
          workflowMessage={message}
          selectedWorkflowId={message.workflowData?.selectedWorkflowId}
          workflowPayload={workflowPayload}
          isLastMessage={isLastMessage}
        />
      );
    };

    const renderDelegationMessage = useCallback(
      (message: DelegationChatMessage) => {
        return (
          <div className="flex flex-col gap-3" key={message.id}>
            {/* Agent Avatar and Name */}
            <div className="flex gap-[9px] items-center">
              <Avatar className="w-10 h-10">
                <AvatarImage src={employee.avatar} alt={employee.name} />
                <AvatarFallback className="border-none bg-transparent">
                  <Image
                    src={noTextLogoPath}
                    alt="Logo"
                    width={32}
                    height={32}
                  />
                </AvatarFallback>
              </Avatar>
              <div className="text-sm text-primary">
                {capitalizeFirstLetter(employee.name)}
              </div>
            </div>

            {/* Delegation Card with proper alignment */}
            <div className="ml-2 lg:ml-[49px]">
              <DelegationCard
                employee={message.delegationData.employee}
                onApprove={(employee) =>
                  handleDelegationApprove(employee, message.id)
                }
                isApproveLoading={message.delegationData.isApproveLoading}
                isApproved={message.delegationData.isApproved}
                isTaskStarted={message.delegationData.isTaskStarted}
                taskData={message.delegationData.taskData}
              />
            </div>
          </div>
        );
      },
      [handleDelegationApprove, employee.avatar, employee.name]
    );

    const renderCreateEmployeeMessage = useCallback(
      (message: CreateEmployeeChatMessage) => {
        const handleButtonClick = async (buttonType: string) => {
          setCreateEmployeeLoading(buttonType);

          try {
            if (buttonType === "developer") {
              // Open developer portal in new tab
              window.open(createEmployeeOnDeveloperPortalUrl, "_blank");
            } else {
              // Navigate to the create employee page
              router.push(createEmployeeRoute);
            }
          } catch (error) {
            console.error("Navigation error:", error);
          } finally {
            // Keep loading state for a bit longer to ensure user sees the feedback
            setTimeout(() => {
              setCreateEmployeeLoading(null);
            }, 1500);
          }
        };

        return (
          <div className="flex flex-col gap-2 md:gap-3" key={message.id}>
            {/* Agent Avatar and Name */}
            <div className="flex gap-2 md:gap-[9px] items-center">
              <Avatar className="w-8 h-8 md:w-10 md:h-10">
                <AvatarImage src={employee.avatar} alt={employee.name} />
                <AvatarFallback className="border-none bg-transparent">
                  <Image
                    src={noTextLogoPath}
                    alt="Logo"
                    width={32}
                    height={32}
                  />
                </AvatarFallback>
              </Avatar>
              <div className="text-sm text-primary">
                {capitalizeFirstLetter(employee.name)}
              </div>
            </div>

            {/* Employee creator buttons */}
            <div className="ml-2 lg:ml-[49px]">
              <div className="flex flex-col sm:flex-row gap-3 md:gap-4  items-center">
                {/* Employee Creator Button */}
                <Button
                  variant="outline"
                  className={`min-w-[214px] min-h-[40px] sm:w-auto px-6 py-3 bg-white border border-border-default text-black font-satoshi-medium rounded-sm transition-colors shadow-none ${
                    createEmployeeLoading !== null
                      ? "opacity-50 cursor-not-allowed"
                      : "hover:bg-gray-50"
                  }`}
                  onClick={() => handleButtonClick("employee")}
                  disabled={createEmployeeLoading !== null}
                >
                  {createEmployeeLoading === "employee" ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    "Employee Creator"
                  )}
                </Button>

                {/* Developer Portal Button */}
                <Button
                  variant="outline"
                  className={`min-w-[214px] min-h-[40px] sm:w-auto px-6 py-3 bg-white border border-border-default text-black font-satoshi-medium rounded-sm transition-colors shadow-none ${
                    createEmployeeLoading !== null
                      ? "opacity-50 cursor-not-allowed"
                      : "hover:bg-gray-50"
                  }`}
                  onClick={() => handleButtonClick("developer")}
                  disabled={createEmployeeLoading !== null}
                >
                  {createEmployeeLoading === "developer" ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Loading...
                    </>
                  ) : (
                    "Developer Portal"
                  )}
                </Button>
              </div>
            </div>
          </div>
        );
      },
      [employee.avatar, employee.name, router, setCreateEmployeeLoading]
    );

    // Render the delegation thinking content if present
    const renderDelegationThinking = () => {
      if (!delegationThinkingContent) return null;

      return (
        <ThinkingBox
          content={delegationThinkingContent}
          title="Analyzing and thinking..."
          maxHeight="max-h-64"
          className="w-full"
        />
      );
    };

    // Render Load More button
    const renderLoadMoreButton = () => {
      return (
        <div className="flex justify-center items-center">
          <Button
            onClick={onLoadMore}
            disabled={
              isLoadingMoreMessages || isTyping || streamState.isStreaming
            }
            variant="outline"
            size="sm"
          >
            {isLoadingMoreMessages ? (
              <>
                <Loader2 className="w-4 h-4 mr-2" />
                Loading...
              </>
            ) : (
              "Load more messages"
            )}
          </Button>
        </div>
      );
    };

    return (
      <div
        className={cn("flex flex-col py-10 space-y-7 relative", className)}
        // style={{ paddingBottom: 80 }}
      >
        {isLoading ? (
          <div className="flex flex-col space-y-7 w-full">
            {/* Agent message skeleton */}
            <div className="flex flex-col gap-2 md:gap-3">
              <div className="flex gap-2 md:gap-[9px] items-center">
                <Skeleton className="w-8 h-8 md:w-10 md:h-10 rounded-full bg-gray-200" />
                <Skeleton className="h-5 w-35 bg-gray-200" />
              </div>
              <div className="ml-2 lg:ml-[49px]">
                <Skeleton className="h-15 w-full md:w-[80%] rounded-lg bg-gray-200" />
              </div>
            </div>

            {/* User message skeleton */}
            <div className="flex flex-col gap-2 md:gap-3">
              <div className="flex justify-end">
                <Skeleton className="h-15 w-full md:w-[70%] ml-auto rounded-lg bg-gray-200" />
              </div>
            </div>

            {/* Agent message skeleton */}
            <div className="flex flex-col gap-2 md:gap-3">
              <div className="flex gap-2 md:gap-[9px] items-center">
                <Skeleton className="w-8 h-8 md:w-10 md:h-10 rounded-full bg-gray-200" />
                <Skeleton className="h-5 w-35 bg-gray-200" />
              </div>
              <div className="ml-2 lg:ml-[49px]">
                <Skeleton className="h-15 w-full md:w-[70%] rounded-lg bg-gray-200" />
              </div>
            </div>

            {/* User message skeleton */}
            <div className="flex flex-col gap-2 md:gap-3">
              <div className="flex justify-end">
                <Skeleton className="h-15 w-full md:w-[50%] ml-auto rounded-lg bg-gray-200" />
              </div>
            </div>
          </div>
        ) : (
          <>
            {hasMoreMessages && renderLoadMoreButton()}
            {currentMessages.map((message, index) => {
              if (isWorkflowMessage(message)) {
                // Only show the thinking bubble for the current workflow being executed
                const currentWorkflowId = getCurrentWorkflow(selectedId || "");
                const isCurrentWorkflow =
                  message.workflowData?.selectedWorkflowId +
                    message.workflowData?.correlationId ===
                  currentWorkflowId;
                return (
                  <div className="flex flex-col gap-3" key={message.id}>
                    {isWorkflowStarted &&
                      isCurrentWorkflow &&
                      renderThinkingBubbleForWorkflow()}
                    <div className="ml-2 lg:ml-[49px]">
                      {renderWorkflowMessage(
                        message,
                        currentMessages.length - 1 === index
                      )}
                    </div>
                  </div>
                );
              }
              if (isDelegationChatMessage(message)) {
                return renderDelegationMessage(message);
              }
              if (isCreateEmployeeChatMessage(message)) {
                return renderCreateEmployeeMessage(message);
              }
              return renderMessage(message, index);
            })}
            {renderStreamingMessage()}
            {renderStreamError()}
          </>
        )}
        <div ref={messagesEndRef} />
        <PreviewFile
          previewFile={previewFile}
          onClose={() => setPreviewFile(null)}
        />
      </div>
    );
  }
);

ChatMessagesList.displayName = "ChatMessagesList";

export default ChatMessagesList;
