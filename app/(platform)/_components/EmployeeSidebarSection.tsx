"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  ArchiveX,
  IdCardLanyard,
  Code,
  ArrowUpFromLine,
} from "lucide-react";
import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { createEmployeeRoute } from "@/shared/routes";
import {
  createEmployeeOnDeveloperPortalUrl,
  marketplaceAgentsUrl,
} from "@/shared/constants";

export const EmployeeSidebarSection = () => {
  const pathname = usePathname();

  return (
    <div className="flex flex-col gap-2 w-full">
      <div className="tour-employee-sidebar-view-all-employees">
        <Link
          href="/employees"
          className={`flex items-center gap-3 p-1 font-satoshi-bold rounded-md transition-colors ${
            pathname === "/employees"
              ? "text-brand-primary "
              : "hover:bg-brand-clicked hover:text-brand-primary text-brand-primary-font"
          }`}
        >
          <div
            className={`flex items-center bg-background-muted justify-center w-8 h-8 rounded-[4px] ${
              pathname === "/employees"
                ? "text-brand-primary bg-color-light"
                : "text-text-primary"
            }`}
          >
            <IdCardLanyard className="w-6 h-6" strokeWidth={1.5} />
          </div>
          <span className="text-sm font-medium">View All Employees</span>
        </Link>
        {/* <button
        onClick={handleCreateEmployee}
        className={`flex items-center gap-3 p-1 rounded-md transition-colors hover:bg-brand-clicked hover:text-brand-primary text-brand-primary-font`}
      > */}
        <Link
          href="/employees/bench-employee"
          className={`flex items-center gap-3 p-1 font-satoshi-bold rounded-md transition-colors  ${
            pathname === "/employees/bench-employee"
              ? "text-brand-primary "
              : "hover:bg-brand-clicked hover:text-brand-primary text-brand-primary-font"
          }`}
        >
          <div
            className={`flex items-center bg-background-muted justify-center w-8 h-8 rounded-[4px] ${
              pathname === "/employees/bench-employee"
                ? "text-brand-primary bg-color-light"
                : "text-text-primary"
            }`}
          >
            <ArchiveX className="w-5 h-5" strokeWidth={1.5} />
          </div>
          <span className="text-sm font-medium">Employee Bench</span>
        </Link>
      </div>

      <hr className="mt-3" />

      <div className="tour-employee-sidebar-add-ai-employee flex flex-col gap-3 mt-4">
        <button
          onClick={() => {
            window.open(marketplaceAgentsUrl, "_blank");
          }}
          className="flex items-center gap-2 p-3 bg-background-muted hover:bg-color-light rounded-lg transition-colors group"
        >
          <div className="flex items-center justify-center w-8 h-8 bg-white rounded-[4px]">
            <ArrowUpFromLine
              className="w-6 h-6 text-text-primary"
              strokeWidth={1.5}
            />
          </div>
          <div className="flex flex-col text-left">
            <span className="text-sm font-medium text-text-primary">
              Add AI Employee
            </span>
            <span className="text-xs text-text-secondary">
              From Ruh Marketplace
            </span>
          </div>
        </button>

        <button
          onClick={() => {
            window.open(createEmployeeOnDeveloperPortalUrl, "_blank");
          }}
          className="flex items-center gap-2 p-3 bg-background-muted hover:bg-color-light rounded-lg transition-colors group"
        >
          <div className="flex items-center justify-center w-8 h-8 bg-white rounded-[4px]">
            <Code className="w-6 h-6 text-text-primary" strokeWidth={1.5} />
          </div>
          <div className="flex flex-col text-left">
            <span className="text-sm font-medium text-text-primary">
              Code an AI Employee
            </span>
            <span className="text-xs text-text-secondary">
              from the Developer Portal
            </span>
          </div>
        </button>
      </div>
      {/* </button> */}
    </div>
  );
};
