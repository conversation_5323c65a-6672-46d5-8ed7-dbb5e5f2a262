"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { userSettingsButtons } from "@/shared/constants";
import { useTourContext } from "@/lib/providers/TourProvider";
import { LOCAL_STORAGE_KEYS } from "@/shared/constants";
import { isTourCompleted, markTourCompleted } from "@/lib/utils/tourUtils";
import { useCallback, useEffect } from "react";
import { userSettingsTourConfig } from "../../../lib/utils/tourConfig";

export const UserSettingsSidebarButtons = () => {
  const pathname = usePathname();
  const { startTour } = useTourContext();

  useEffect(() => {
    if (!isTourCompleted(LOCAL_STORAGE_KEYS.RUH_USER_SETTINGS_TOUR_COMPLETED)) {
      handleStartTour();
    }
  }, []);

  const handleStartTour = useCallback(() => {
    startTour(userSettingsTourConfig, {
      onComplete: () => {
        markTourCompleted(LOCAL_STORAGE_KEYS.RUH_USER_SETTINGS_TOUR_COMPLETED);
      },
      onSkip: () => {
        markTourCompleted(LOCAL_STORAGE_KEYS.RUH_USER_SETTINGS_TOUR_COMPLETED);
      },
    });
  }, [startTour]);
  return (
    <div className="flex flex-col gap-[12px] w-full">
      {userSettingsButtons.map((button, index) => (
        <Link
          key={index}
          href={button.path}
          className={`${
            button.tourClass
          } flex items-center gap-2 p-1 rounded-md transition-colors ${
            pathname === button.path
              ? "text-brand-primary bg-background-accent"
              : "hover:bg-brand-clicked  text-brand-primary-font"
          }`}
        >
          <div
            className={`flex items-center justify-center w-8 h-8 rounded-[4px] p-1 ${
              pathname === button.path
                ? "text-brand-primary"
                : "text-brand-primary-font"
            }`}
          >
            <button.icon className="w-5 h-5" strokeWidth={1.5} />
          </div>
          <span className="text-sm font-satoshi-bold">{button.text}</span>
        </Link>
      ))}
    </div>
  );
};
