"use client";

import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { useMixpanelTrack } from "@/hooks/useMixPanelTrack";
import {
  Wrench,
  Database,
  Settings,
  Workflow,
  FileSpreadsheet,
} from "lucide-react";

const steps = [
  {
    step: 1,
    title: "Profile",
    description: "Add guidelines for your agent",
    icon: FileSpreadsheet,
  },
  {
    step: 2,
    title: "Knowledge",
    description: "Upload your data relationships",
    icon: Database,
  },
  {
    step: 3,
    title: "Tools",
    description: "Add tools to complete tasks",
    icon: Wrench,
  },
  {
    step: 4,
    title: "Workflows",
    description: "Add or customize AI workflows",
    icon: Workflow,
  },
  {
    step: 5,
    title: "Advanced Settings",
    description: "Fine-tune your AI employee",
    icon: Settings,
  },
];

interface CreateEmployeeSidebarStepsProps {
  isEdit?: boolean;
  onStepChange?: (step: number) => void;
}

export const CreateEmployeeSidebarSteps = ({
}: CreateEmployeeSidebarStepsProps) => {
  const { formStep, setFormStep, setPendingSaveStep } = useEmployeeCreateStore();


  const handleStepClick = (step: number) => {
    if (formStep !== step) {
      setPendingSaveStep(formStep);
      const interval = setInterval(() => {
        if (useEmployeeCreateStore.getState().pendingSaveStep === null) {
          setFormStep(step);
          clearInterval(interval);
        }
      }, 10);
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-4 tour-create-employee-profile">
        {steps.map((step) => {
          const isCurrent = formStep === step.step;
          const IconComponent = step.icon;

          return (
            <div
              key={step.step}
              className={`flex items-start gap-[10px] p-[5px] rounded-[6px] cursor-pointer transition-all duration-200 ${
                isCurrent ? "bg-background-accent text-primary" : ""
              }`}
              onClick={() => handleStepClick(step.step)}
            >
              <div
                className={`py-3 px-1 rounded-lg ${
                  isCurrent ? "text-primary" : "text-[#9CA3AF]"
                }`}
              >
                <IconComponent className="w-[20px] h-[20px]" />
              </div>

              <div className="flex-1">
                <h3 className={`text-[14px] leading-[22px]`}>{step.title}</h3>
                <p
                  className={`text-[12px] text-text-secondary ${
                    isCurrent ? "!text-primary" : ""
                  }`}
                >
                  {step.description}
                </p>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
