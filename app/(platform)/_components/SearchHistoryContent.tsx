"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Search,
  ChevronDown,
  ChevronRight,
  Bot,
  Loader2,
  Clock,
  MoreVertical,
  ChevronDownIcon,
  Trash2,
  ArrowLeft,
} from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import Image from "next/image";
import { GLOBAL_AGENT, noTextLogoPath } from "@/shared/constants";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import { useEmployeeManagementStore } from "@/hooks/useEmployeeManagementStore";
import { useSearchHistoryStore } from "@/hooks/useSearchHistoryStore";
import { ChatType } from "@/shared/interfaces";
import { TaskStatus } from "@/shared/enums";
import { capitalizeFirstLetter, cn, getTimeAgo } from "@/lib/utils";
import {
  useInfiniteQuery,
  useMutation,
  useQueryClient,
} from "@tanstack/react-query";
import { communicationApi } from "@/app/api/communication";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { toast } from "sonner";
import { employeeChatRoute, globalChatRoute } from "@/shared/routes";

const FILTERS = [
  { label: "All", value: "all" },
  { label: "Global", value: "global" },
  { label: "Employee", value: "agent" },
];

const PAGE_SIZE = 15;

type ChatSummary = {
  id: string;
  title: string;
  tasks?: {
    id: string;
    title: string;
    status?: TaskStatus;
    updatedAt: string;
    agentConversationId: string;
    agentId: string;
    sessionId: string;
    agentDetails?: {
      avatar?: string;
    };
  }[];
  agentDetails?: {
    avatar?: string;
  };
  updatedAt: string;
  chatType: ChatType;
  agentId: string;
};

// Function to get status dot color based on task status (same as GlobalChatHistory)
const getStatusDotColor = (status?: TaskStatus) => {
  switch (status) {
    case TaskStatus.TASK_STATUS_COMPLETED:
      return "bg-green-500";
    case TaskStatus.TASK_STATUS_RUNNING:
      return "bg-orange-500";
    case TaskStatus.TASK_STATUS_FAILED:
      return "bg-red-500";
    case TaskStatus.TASK_STATUS_PAUSED:
      return "bg-yellow-500";
    case TaskStatus.TASK_STATUS_CANCELLED:
      return "bg-gray-500";
    case TaskStatus.TASK_STATUS_UNSPECIFIED:
    default:
      return "bg-gray-400";
  }
};

async function fetchSearchHistory(
  filter: "all" | "global" | "agent",
  search: string,
  pageParam: number
) {
  let chatType = undefined;

  if (filter === "global") {
    chatType = ChatType.GLOBAL;
  } else if (filter === "agent") {
    chatType = ChatType.AGENT;
  }

  const data = await communicationApi.getConversations({
    agentId: "",
    page: pageParam,
    limit: PAGE_SIZE,
    chatType,
    search: search.trim() || undefined,
  });
  return data;
}

// Loading skeleton component
const ChatItemSkeleton = () => (
  <div className="flex items-center justify-between p-4 border-b border-gray-100 animate-pulse gap-[5px]">
    <div className="flex items-center gap-3">
      <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
      <div className="flex flex-col gap-1">
        <div className="h-4 bg-gray-200 rounded w-48"></div>
      </div>
    </div>
    <div className="flex items-center gap-3">
      <div className="h-3 bg-gray-200 rounded w-16"></div>
      <div className="w-6 h-6 bg-gray-200 rounded"></div>
    </div>
  </div>
);

const LoadingState = () => (
  <div className="flex flex-col">
    {Array.from({ length: 8 }).map((_, index) => (
      <ChatItemSkeleton key={index} />
    ))}
  </div>
);

// Infinite scroll loader component
const InfiniteScrollLoader = () => (
  <div className="flex items-center justify-center py-6">
    <Loader2 className="w-6 h-6 animate-spin text-purple-600" />
  </div>
);

export default function SearchHistoryContent() {
  const [filter, setFilter] = React.useState<"all" | "global" | "agent">("all");
  const [search, setSearch] = React.useState("");
  const [debouncedSearch, setDebouncedSearch] = React.useState("");
  const [expandedChats, setExpandedChats] = React.useState<Set<string>>(
    new Set()
  );
  const [deletingChatId, setDeletingChatId] = React.useState<string | null>(
    null
  );
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { closeSearchHistory } = useSearchHistoryStore();
  const scrollContainerRef = React.useRef<HTMLDivElement>(null);
  const previousPathnameRef = React.useRef<string>(pathname);
  const previousSearchParamsRef = React.useRef<string>(searchParams.toString());
  const queryClient = useQueryClient();

  const {
    initializeChatSession,
    setSelectedEmployeeId,
    setChatStarted,
    setSessionData,
  } = useEmployeeManagementStore();

  // Delete conversation mutation
  const deleteConversationMutation = useMutation({
    mutationFn: (conversationId: string) =>
      communicationApi.deleteConversation(conversationId),
    onMutate: (conversationId) => {
      setDeletingChatId(conversationId);
    },
    onSuccess: () => {
      toast.success("Conversation deleted successfully");
      // Invalidate and refetch search history
      queryClient.invalidateQueries({ queryKey: ["searchHistory"] });
      // Also invalidate global chat history if it exists
      queryClient.invalidateQueries({ queryKey: ["globalChatHistory"] });
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to delete conversation");
    },
    onSettled: () => {
      setDeletingChatId(null);
    },
  });

  // Close search history when pathname or search params change (but not on initial mount)
  React.useEffect(() => {
    const currentSearchParams = searchParams.toString();

    if (
      previousPathnameRef.current !== pathname ||
      previousSearchParamsRef.current !== currentSearchParams
    ) {
      closeSearchHistory();
    }

    previousPathnameRef.current = pathname;
    previousSearchParamsRef.current = currentSearchParams;
  }, [pathname, searchParams, closeSearchHistory]);

  // Debounce search input
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  // Infinite query for search history
  const {
    data,
    isLoading,
    isError,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = useInfiniteQuery({
    queryKey: ["searchHistory", filter, debouncedSearch],
    queryFn: ({ pageParam = 1 }) =>
      fetchSearchHistory(filter, debouncedSearch, pageParam),
    getNextPageParam: (lastPage, allPages) => {
      const currentPage = allPages.length;
      const totalPages = Math.ceil((lastPage.metadata?.total || 0) / PAGE_SIZE);
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    initialPageParam: 1,
  });

  // Flatten all pages data
  const chatsHistory = React.useMemo(() => {
    return data?.pages.flatMap((page) => page.data) || [];
  }, [data]);

  // Handle scroll for infinite loading
  const handleScroll = React.useCallback(() => {
    if (!scrollContainerRef.current || !hasNextPage || isFetchingNextPage) {
      return;
    }

    const { scrollTop, scrollHeight, clientHeight } =
      scrollContainerRef.current;
    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

    // Trigger next page when scrolled 80% down
    if (scrollPercentage > 0.8) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Attach scroll listener
  React.useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  };

  const handleSearchKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      if (!search.trim()) {
        setDebouncedSearch("");
        refetch();
      }
    }
  };

  const toggleChatExpansion = (chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setExpandedChats((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(chatId)) {
        newSet.delete(chatId);
      } else {
        newSet.add(chatId);
      }
      return newSet;
    });
  };

  const handleChatClick = (chat: ChatSummary) => {
    // Close search history before navigating
    closeSearchHistory();

    if (chat.chatType === ChatType.GLOBAL) {
      router.push(`${globalChatRoute}/${chat.id}`);
      setSelectedEmployeeId(GLOBAL_AGENT.id + chat.id);
      initializeChatSession(GLOBAL_AGENT.id + chat.id);
      setChatStarted(GLOBAL_AGENT.id + chat.id, true);
    } else if (chat.chatType === ChatType.AGENT) {
      router.push(
        `${employeeChatRoute}/${chat.agentId}?conversationId=${chat.id}`
      );
      setSelectedEmployeeId(chat.agentId);
      initializeChatSession(chat.agentId);
      setChatStarted(chat.agentId, true);
    }
  };

  const handleTaskClick = (
    task: NonNullable<ChatSummary["tasks"]>[0],
    e: React.MouseEvent
  ) => {
    e.stopPropagation();

    // Close search history before navigating
    closeSearchHistory();

    if (task.status === TaskStatus.TASK_STATUS_RUNNING) {
      setSessionData(task.agentId, task.agentConversationId, task.sessionId);
    }

    router.push(
      `${employeeChatRoute}/${task.agentId}?conversationId=${task.agentConversationId}`
    );
    setSelectedEmployeeId(task.agentId);
    initializeChatSession(task.agentId);
    setChatStarted(task.agentId, true);
  };

  const handleDeleteChat = (chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    deleteConversationMutation.mutate(chatId);
  };

  const getFilterLabel = () => {
    const currentFilter = FILTERS.find((f) => f.value === filter);
    return currentFilter ? currentFilter.label : "All";
  };

  const renderChatItem = (chat: ChatSummary) => {
    const isExpanded = expandedChats.has(chat.id);
    const hasTask =
      chat.chatType === ChatType.GLOBAL && chat.tasks && chat.tasks.length > 0;
    const isAgent = chat.chatType === ChatType.AGENT;

    if (!chat.title) {
      return null;
    }

    return (
      <div key={chat.id} className="flex flex-col">
        {/* Main Chat Item */}
        <div
          className="bg-background-muted rounded-sm p-3 flex flex-col cursor-pointer transition-colors hover:bg-gray-100"
          onClick={() => handleChatClick(chat)}
        >
          {/* Main conversation header */}
          <div className="flex items-center gap-3 mb-2">
            <Avatar className="bg-background-accent flex items-center justify-center">
              {isAgent ? (
                chat.agentDetails?.avatar ? (
                  <AvatarImage src={chat.agentDetails.avatar} />
                ) : (
                  <AvatarFallback>
                    <Bot className="w-4 h-4 text-gray-600" />
                  </AvatarFallback>
                )
              ) : (
                <Image src={noTextLogoPath} alt="Logo" width={18} height={18} />
              )}
            </Avatar>
            <div className="flex flex-col gap-1 flex-1">
              <div className="text-sm font-satoshi-medium text-text-primary line-clamp-1">
                {capitalizeFirstLetter(chat.title)}
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1 text-xs text-text-secondary">
                  <Clock className="w-3 h-3" />
                  <span>{getTimeAgo(chat.updatedAt)}</span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {hasTask && (
                <button
                  onClick={(e) => toggleChatExpansion(chat.id, e)}
                  className="p-1 hover:bg-gray-200 rounded transition-colors"
                >
                  {isExpanded ? (
                    <ChevronDown className="w-4 h-4 text-gray-500" />
                  ) : (
                    <ChevronRight className="w-4 h-4 text-gray-500" />
                  )}
                </button>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button
                    onClick={(e) => e.stopPropagation()}
                    className="p-1 hover:bg-gray-200 rounded transition-colors"
                    disabled={deletingChatId === chat.id}
                  >
                    {deletingChatId === chat.id ? (
                      <Loader2 className="w-4 h-4 text-gray-500 animate-spin" />
                    ) : (
                      <MoreVertical className="w-4 h-4 text-gray-500" />
                    )}
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem
                    onClick={(e) => handleDeleteChat(chat.id, e)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 cursor-pointer"
                    disabled={deletingChatId === chat.id}
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    <span>Delete Chat</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Tasks List (shown when expanded) - same as GlobalChatHistory */}
          {hasTask && isExpanded && (
            <>
              {/* Separator */}
              <div className="h-px bg-gray-200 mb-2"></div>

              {/* Tasks */}
              <div className="space-y-1">
                {chat.tasks!.map((task) => (
                  <div
                    key={task.id}
                    className="p-2 flex items-center gap-2 bg-white rounded-sm hover:bg-gray-50 transition-colors cursor-pointer"
                    onClick={(e) => handleTaskClick(task, e)}
                  >
                    {/* Bot Avatar with Status Dot */}
                    <div className="relative">
                      <Avatar className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                        {task.agentDetails?.avatar ? (
                          <AvatarImage src={task.agentDetails.avatar} />
                        ) : (
                          <AvatarFallback>
                            <Bot className="w-3 h-3 text-purple-600" />
                          </AvatarFallback>
                        )}
                      </Avatar>
                      {/* Status Dot */}
                      <div
                        className={cn(
                          "absolute -bottom-0.5 -right-0.5 w-2 h-2 rounded-full border border-white",
                          getStatusDotColor(task.status)
                        )}
                      />
                    </div>

                    {/* Task Content */}
                    <div className="flex-1 min-w-0">
                      <div className="text-[10px] font-satoshi-medium text-text-primary line-clamp-1">
                        {capitalizeFirstLetter(task.title)}
                      </div>
                    </div>

                    {/* Timestamp */}
                    <div className="text-[8px] text-text-secondary font-satoshi-regular">
                      {getTimeAgo(task.updatedAt)}
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="h-screen w-full bg-white flex flex-col overflow-hidden">
      {/* Header - Centered with larger text and back button */}
      <div className="relative flex flex-col items-center justify-center  pt-15 md:pt-20 pb-8 md:pb-12 gap-4 mt-[50px] md:mt-0">
        {/* Back Button - positioned absolutely to top left */}
        <Button
          variant="ghost"
          size="sm"
          onClick={closeSearchHistory}
          className="absolute top-6 left-6 flex items-center gap-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back</span>
        </Button>

        <h1 className="!text-3xl md:!text-4xl text-gradient-brand font-satoshi-bold mt-4">
          Search Chat History
        </h1>
        <p className="text-black text-md text-center">
          Search through your chat history to find the conversation you need.
        </p>
      </div>

      {/* Search Bar - Centered with constrained width */}
      <div className="flex justify-center px-6 mb-8 md:mb-12">
        <div className="relative w-full max-w-xl">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <Input
            value={search}
            onChange={handleSearchChange}
            onKeyPress={handleSearchKeyPress}
            placeholder="Search your chat"
            className="pl-12 h-12 text-base border-gray-300 rounded-lg focus:border-purple-600 focus:ring-purple-600"
          />
        </div>
      </div>

      {/* Sticky Filter Section */}
      <div className="sticky top-0 z-10 bg-white  pb-4">
        <div className="w-full max-w-4xl mx-auto px-6">
          <div className="flex items-center justify-between">
            <span className="text-base text-gray-600">Recent</span>
            <div className="flex items-center gap-3">
              <span className="text-base text-gray-600">Filter By</span>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="flex items-center gap-2 bg-white border-gray-300 hover:bg-gray-50"
                  >
                    <span>{getFilterLabel()}</span>
                    <ChevronDownIcon className="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-40">
                  {FILTERS.map((filterOption) => (
                    <DropdownMenuItem
                      key={filterOption.value}
                      onClick={() => setFilter(filterOption.value as any)}
                      className={cn(
                        "cursor-pointer",
                        filter === filterOption.value &&
                          "bg-purple-50 text-purple-600"
                      )}
                    >
                      {filterOption.label}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>

      {/* Full width scrollable container with centered content */}
      <div ref={scrollContainerRef} className="flex-1 w-full overflow-y-auto">
        <div className="w-full max-w-4xl mx-auto px-2 md:px-6 flex flex-col pt-6">
          {/* Content Area */}
          {isLoading ? (
            <LoadingState />
          ) : isError ? (
            <div className="flex flex-col items-center justify-center py-12 text-red-500">
              <div className="mb-4 text-lg font-medium">
                {error?.message || "Something went wrong"}
              </div>
              <Button
                onClick={() => refetch()}
                variant="outline"
                className="text-sm"
              >
                Try Again
              </Button>
            </div>
          ) : chatsHistory.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Search className="w-16 h-16 text-gray-300 mb-4" />
              <div className="text-center text-gray-500 text-lg">
                {search.trim() ? (
                  <>
                    No chats found for &quot;{search}&quot;
                    <br />
                    <span className="text-sm">
                      Try adjusting your search terms
                    </span>
                  </>
                ) : (
                  <>
                    No chat history found
                    <br />
                    <span className="text-sm">
                      Start a conversation to see it here
                    </span>
                  </>
                )}
              </div>
            </div>
          ) : (
            <div className="space-y-3 pb-6">
              {chatsHistory.map((chat) => renderChatItem(chat as ChatSummary))}
              {/* Infinite scroll loader */}
              {isFetchingNextPage && hasNextPage && <InfiniteScrollLoader />}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
