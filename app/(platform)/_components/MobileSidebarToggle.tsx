"use client";

import { NotificationButton } from "@/components/shared/NotificationButton";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebarStore } from "@/hooks/use-sidebar";
import { dashboardRoute } from "@/shared/routes";
import { PanelRightCloseIcon } from "lucide-react";
import Link from "next/link";

export function MobileSidebarToggle() {
  const { isMobileSidebarOpen, toggleMobileSidebar } = useSidebarStore();

  return (
    <div className="block md:hidden">
      <div className="bg-brand-background fixed top-0 right-0 left-0 z-50 flex h-14 items-center px-4">
        {!isMobileSidebarOpen && (
          <div
            role="button"
            className="bg-brand-background text-brand-primary-font flex cursor-pointer gap-4 p-2 w-full"
          >
            <PanelRightCloseIcon
              strokeWidth={1.2}
              onClick={() => {
                toggleMobileSidebar();
              }}
            />
            <Link href={dashboardRoute}>
              <RuhIcon />
            </Link>
          </div>
        )}
        <NotificationButton />
      </div>
    </div>
  );
}

const RuhIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="30"
    height="20"
    viewBox="0 0 30 20"
    fill="none"
  >
    <path
      d="M19.065 8.51244L11.4827 8.88132C10.638 8.92246 10.1941 9.95956 10.7255 10.6547L13.8743 14.7776C14.1864 15.1862 14.1783 15.7721 13.8541 16.1708L11.09 19.5687C10.8949 19.8085 10.6111 19.9475 10.3112 19.9489L1.04085 20C0.157127 20.0042 -0.324413 18.9132 0.248593 18.2024L3.58575 14.0697L5.12991 12.1544L14.6195 0.384479C14.8159 0.140455 15.1037 0 15.4064 0H24.2032C25.0883 0 25.5631 1.09669 24.9847 1.8018L19.7994 8.13506C19.6138 8.36206 19.3488 8.49826 19.065 8.51103V8.51244Z"
      fill="url(#paint0_linear_4715_36553)"
    />
    <path
      d="M14.1588 10.0053H22.6691C22.9691 10.0053 23.2529 10.1415 23.4493 10.3799L28.917 17.0182L29.7766 18.1659C30.3106 18.8795 29.8304 19.9294 28.9682 19.9323C28.7287 19.9323 28.4933 19.9323 28.2754 19.9323L20.0946 19.8883C19.7853 19.8869 19.4934 19.7393 19.2983 19.4853L13.3584 11.7816C12.8097 11.0694 13.2898 10.0039 14.1601 10.0039L14.1588 10.0053Z"
      fill="url(#paint1_linear_4715_36553)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_4715_36553"
        x1="26.9109"
        y1="26.9121"
        x2="12.5155"
        y2="11.6005"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#7B5AFF" />
        <stop offset="1" stopColor="#AE00D0" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_4715_36553"
        x1="28.6561"
        y1="23.5529"
        x2="15.8345"
        y2="9.91575"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#7B2AFF" />
        <stop offset="1" stopColor="#AE00D0" />
      </linearGradient>
    </defs>
  </svg>
);
