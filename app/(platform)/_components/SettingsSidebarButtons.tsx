"use client";

import { useState } from "react";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";

import { useOrgStore } from "@/hooks/use-organization";
import { settingsButtons } from "@/shared/constants";

export const SettingsSidebarButtons = () => {
  const pathname = usePathname();
  const { currentOrganization } = useOrgStore();


  // useEffect(() => {
  //   // Check if user is admin using the same logic as LeftBar
  //   if (currentOrganization) {
  //     if (!currentOrganization.isAdmin) {
  //       // User is not admin, redirect to dashboard
  //       router.push(dashboardRoute);
  //     } else {
  //       // User is admin, allow rendering
  //       setCheckingAdmin(false);
  //     }
  //   } else if (currentOrganization !== null) {
  //     // Data has loaded but user doesn't have proper access
  //     router.push(dashboardRoute);
  //   }
  //   // If organization or currentOrganization is still null, keep checking (loading state)
  // }, [currentOrganization, router]);

  // // Show loader while checking admin status
  // if (checkingAdmin) {
  //   return (
  //     <div className="flex justify-center py-4">
  //       <LoadingSpinner />
  //     </div>
  //   );
  // }
  let finalSettingButton = settingsButtons;
  if (!currentOrganization?.isAdmin) {
    const tokenUsageIndex = settingsButtons?.findIndex(
      (item) => item?.text == "Token Usage"
    );
    finalSettingButton = [
      ...settingsButtons.slice(0, tokenUsageIndex),
      ...settingsButtons.slice(tokenUsageIndex + 1),
    ];
  }
  return (
    <div className="flex flex-col gap-2 w-full">
      {finalSettingButton.map((button, index) => (
        <Link
          key={index}
          href={button.path}
          className={`${
            button.tourClass
          } flex items-center gap-3 p-1 rounded-md transition-colors ${
            pathname === button.path
              ? "bg-brand-clicked text-brand-primary font-semibold"
              : "hover:bg-brand-clicked hover:text-brand-primary text-brand-primary-font"
          }`}
        >
          <div
            className={`flex items-center justify-center w-6 h-6 ${
              pathname === button.path
                ? "text-brand-primary"
                : "text-brand-primary-font"
            }`}
          >
            <button.icon className="w-5 h-5" strokeWidth={1.5} />
          </div>
          <span className="text-sm font-medium">{button.text}</span>
        </Link>
      ))}
    </div>
  );
};
