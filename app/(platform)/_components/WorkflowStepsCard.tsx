import { workflowApi } from "@/app/api/workflow";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { useWorkflowStore } from "@/hooks/use-workflow";
import {
  useEmployeeManagementStore,
  WorkflowChatMessage,
} from "@/hooks/useEmployeeManagementStore";
import { sanitizeString } from "@/services/helper";
import { WorkflowStatus, WorkflowStepStatus } from "@/shared/enums";
import { AnimatePresence, motion } from "framer-motion";
import {
  Check,
  CheckCircle,
  Loader,
  Loader2,
  Maximize2,
  Pause,
  X,
} from "lucide-react";
import React, { useEffect, useState } from "react";

interface WorkflowStep {
  display_name: string;
  transition_id: string;
  label?: string;
}

interface WorkflowStepsCardProps {
  workflowMessage: WorkflowChatMessage;
  selectedWorkflowId: string;
  workflowPayload: any;
  isLastMessage: boolean;
}

// Helper to get step status from workflowStatusMap
const getStepStatus = (
  step: WorkflowStep,
  workflowStatusMap: Record<string, string>
): string => {
  return workflowStatusMap?.[step.transition_id] || "pending";
};

const WorkflowStepsCard: React.FC<WorkflowStepsCardProps> = ({
  workflowMessage,
  workflowPayload,
  isLastMessage,
  selectedWorkflowId,
}) => {
  const [approveLoading, setApproveLoading] = useState(false);
  const [approveStepLoading, setApproveStepLoading] = useState(false);
  const [reExecuteStepLoading, setReExecuteStepLoading] = useState(false);
  const { setWorkflowPayload, setActiveCorrelationId, activeCorrelationId } =
    useWorkflowStore();
  const {
    available_nodes = [],
    stepStatus = {},
    approvalRequired,
    cancelled,
    workflowStatus,
    stepApprovalRequired,
  } = workflowMessage.workflowData || {};
  const {
    setIsWorkflowStarted,
    setIsSplitView,
    setSelectedStep,
    selectedStep,
  } = useEmployeeManagementStore();

  // Track which step is expanded (for completed workflows)
  const [expandedStepId, setExpandedStepId] = useState<string | null>(null);

  // Find the first active (not completed/time_logged) step
  const firstActiveStepIdx = available_nodes.findIndex((step: WorkflowStep) => {
    const status = getStepStatus(step, stepStatus);
    return (
      status !== WorkflowStepStatus.COMPLETED &&
      status !== WorkflowStepStatus.TIME_LOGGED
    );
  });
  const allStepsCompleted = firstActiveStepIdx === -1;

  useEffect(() => {
    // Auto-expand the current active step
    if (firstActiveStepIdx !== -1) {
      setExpandedStepId(available_nodes[firstActiveStepIdx].transition_id);
    }
  }, [firstActiveStepIdx, available_nodes]);

  // Approve handler
  const handleApprove = async () => {
    setApproveLoading(true);
    try {
      const { auto_approve, ...rest } = workflowPayload;
      const { correlationId } = await workflowApi.executeWorkflow(rest);
      setActiveCorrelationId(correlationId);
      setIsWorkflowStarted(true);
      if (workflowMessage && workflowMessage.workflowData) {
        workflowMessage.workflowData.approvalRequired = false;
      }
    } catch (error) {
      // Handle error (show toast, etc.)
    } finally {
      setApproveLoading(false);
    }
  };

  // Auto-approve handler
  const handleAutoApproveChange = (checked: boolean) => {
    if (selectedWorkflowId === workflowPayload.workflow_id) {
      setWorkflowPayload({
        ...workflowPayload,
        auto_approve: !checked,
        approval: !checked,
      });
    }
  };

  const handleApproveStep = async () => {
    setApproveStepLoading(true);
    try {
      await workflowApi.executeWorkflowApprove({
        correlationId: activeCorrelationId,
        decision: "approve",
      });
    } catch (error) {
      // Handle error (show toast, etc.)
    } finally {
      setApproveStepLoading(false);
    }
  };

  const handleReExecuteStep = async (pausedStep: WorkflowStep) => {
    setReExecuteStepLoading(true);
    try {
      await workflowApi.executeWorkflowReExecute({
        action: "regenerate",
        correlationId: activeCorrelationId,
        node_id: pausedStep.transition_id,
      });
    } catch (error) {
      // Handle error (show toast, etc.)
    } finally {
      setReExecuteStepLoading(false);
    }
  };

  // Auto-open RightSidePanel for the most recently TIME_LOGGED step
  // useEffect(() => {
  //   // Find the last step with TIME_LOGGED status
  //   const timeLoggedSteps = available_nodes.filter(
  //     (step: WorkflowStep) =>
  //       getStepStatus(step, stepStatus) === WorkflowStepStatus.TIME_LOGGED
  //   );
  //   const lastTimeLoggedStep =
  //     timeLoggedSteps.length > 0
  //       ? timeLoggedSteps[timeLoggedSteps.length - 1]
  //       : null;
  //   if (
  //     lastTimeLoggedStep &&
  //     (!selectedStep ||
  //       selectedStep.transition_id !== lastTimeLoggedStep.transition_id)
  //   ) {
  //     setSelectedStep(lastTimeLoggedStep);
  //     setIsSplitView(true);
  //   }
  // }, [available_nodes, stepStatus]);

  return (
    <div className="flex flex-col gap-8">
      <div className="flex flex-col gap-8">
        {available_nodes.map((step: WorkflowStep, idx: number) => {
          const status = getStepStatus(step, stepStatus);
          const stepMsgObj =
            workflowMessage.workflowData?.stepMessages?.[step.transition_id];
          const stepMessagesAndStatus = {
            messages: stepMsgObj?.messages || [],
            status: stepMsgObj?.status || "",
          };
          const isExpanded = expandedStepId === step.transition_id;

          return (
            <div className="flex flex-col gap-2" key={step.transition_id + idx}>
              <AnimatePresence mode="wait">
                <motion.div
                  key={
                    isExpanded
                      ? `step-${step.transition_id}-expanded`
                      : `step-${step.transition_id}`
                  }
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: 10 }}
                  transition={{ duration: 0.2 }}
                  className={`bg-white rounded-xl p-5 w-[100%] xl:w-[70%] flex items-center gap-3 border ${
                    isExpanded ? "border-primary" : "border-border-muted"
                  }`}
                >
                  {/* Step header clickable area for expansion */}
                  <div
                    className="flex-1 flex items-center gap-3 cursor-pointer"
                    onClick={() => {
                      setExpandedStepId((prev) =>
                        prev === step.transition_id ? null : step.transition_id
                      );
                    }}
                  >
                    <div className="w-8 h-8 flex items-center justify-center">
                      {status === WorkflowStepStatus.COMPLETED ||
                      status === WorkflowStepStatus.TIME_LOGGED ? (
                        <CheckCircle className="text-primary w-6 h-6" />
                      ) : status === WorkflowStepStatus.STARTED ||
                        status === WorkflowStepStatus.CONNECTING ? (
                        <Loader2 className="animate-spin text-primary w-6 h-6" />
                      ) : status === WorkflowStepStatus.PAUSED ? (
                        <Pause className="text-primary w-6 h-6" />
                      ) : status === WorkflowStepStatus.FAILED ? (
                        <X className="text-red-500 w-6 h-6" />
                      ) : (
                        <div className="w-6 h-6 rounded-full border border-border-muted bg-muted" />
                      )}
                    </div>
                    <div>
                      <span className="font-satoshi-bold text-sm text-text-secondary">
                        Step {idx + 1} :{" "}
                        {step.label
                          ? sanitizeString(step.label)
                          : sanitizeString(step.display_name?.split("-")[0])}
                        {!step.label && step.display_name?.split("-")[1]
                          ? "(" +
                            sanitizeString(step.display_name?.split("-")[1]) +
                            ")"
                          : ""}
                      </span>
                    </div>
                  </div>
                  {/* Show open button for completed, time_logged, or paused steps */}
                  {(status === WorkflowStepStatus.COMPLETED ||
                    status === WorkflowStepStatus.TIME_LOGGED ||
                    status === WorkflowStepStatus.PAUSED) && (
                    <div className="flex items-center gap-1 ml-auto">
                      <div
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedStep(step);
                          setIsSplitView(true);
                        }}
                        className="bg-tab-color h-auto w-auto p-1 rounded-md cursor-pointer"
                      >
                        <Maximize2 className="w-4 h-4" />
                      </div>
                      <span className="text-xs text-text-primary">Open</span>
                    </div>
                  )}
                </motion.div>
              </AnimatePresence>

              {workflowStatus === WorkflowStatus.WAITING_FOR_APPROVAL &&
                stepApprovalRequired === step.transition_id && (
                  <div className="flex items-center gap-2">
                    <Button
                      onClick={handleApproveStep}
                      disabled={approveStepLoading}
                      variant="primary"
                      className="w-fit mb-4"
                    >
                      {approveLoading ? (
                        <Loader2 className="animate-spin w-4 h-4" />
                      ) : (
                        <Check className="w-4 h-4" />
                      )}
                      Approve
                    </Button>
                    <Button
                      onClick={() => handleReExecuteStep(step)}
                      disabled={reExecuteStepLoading}
                      variant="primary"
                      className="w-fit mb-4"
                    >
                      Regenerate
                    </Button>
                  </div>
                )}

              {/* Message card below the step */}
              <AnimatePresence mode="wait">
                {isExpanded && stepMessagesAndStatus.messages.length > 0 && (
                  <motion.div
                    key={`msg-card-${step.transition_id}`}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 10 }}
                    transition={{ duration: 0.2 }}
                    className="bg-white p-5 gap-3 flex flex-col rounded-xl w-full xl:w-[70%] shadow-xs border border-border-muted"
                  >
                    {stepMessagesAndStatus.messages.map(
                      (
                        msgObj: { text: string; status: string },
                        index: number
                      ) => (
                        <div
                          key={index}
                          className="flex items-center gap-2 mb-1"
                        >
                          {stepMessagesAndStatus.status ===
                          WorkflowStepStatus.FAILED ? (
                            msgObj.status === WorkflowStepStatus.FAILED ? (
                              <X className="w-4 h-4 text-red-500" />
                            ) : (
                              <CheckCircle className="w-4 h-4 text-primary" />
                            )
                          ) : stepMessagesAndStatus.status ===
                              WorkflowStepStatus.COMPLETED ||
                            stepMessagesAndStatus.status ===
                              WorkflowStepStatus.TIME_LOGGED ||
                            index <
                              stepMessagesAndStatus.messages.length - 1 ? (
                            <CheckCircle className="w-4 h-4 text-primary" />
                          ) : (
                            <Loader className="animate-spin w-4 h-4 text-primary" />
                          )}
                          <span className="text-sm">{msgObj.text}</span>
                        </div>
                      )
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          );
        })}
      </div>
      {approvalRequired && isLastMessage && !stepApprovalRequired && (
        <div className="flex items-center justify-between w-full xl:w-[70%]">
          <Button
            onClick={handleApprove}
            disabled={approveLoading}
            variant="primary"
          >
            {approveLoading ? (
              <Loader2 className="animate-spin w-4 h-4" />
            ) : (
              <Check className="w-4 h-4" />
            )}
            Approve Plan
          </Button>
          <div className="flex items-center gap-2">
            <Switch
              checked={!workflowPayload.auto_approve}
              onCheckedChange={handleAutoApproveChange}
            />
            <span className="text-xs font-satoshi-bold text-text-primary">
              Auto-Approve
            </span>
          </div>
        </div>
      )}

      {!approvalRequired && isLastMessage && !stepApprovalRequired && (
        <div className="flex items-center gap-2  justify-center w-full xl:w-[70%]">
          <Badge className="flex items-center gap-2   bg-primary text-white rounded-full w-fit">
            <Check className="w-4 w-4" />
            <span className="text-xs font-satoshi-regular text-white">
              Plan Approved
            </span>
          </Badge>
        </div>
      )}

      {cancelled && isLastMessage && (
        <div className="flex items-center gap-2 justify-center w-full xl:w-[70%]">
          <Badge className="flex items-center gap-2 bg-red-500 text-white rounded-full w-fit">
            <X className="w-4 h-4" />
            <span className="text-xs font-satoshi-regular text-white">
              Workflow Cancelled
            </span>
          </Badge>
        </div>
      )}
    </div>
  );
};

export default WorkflowStepsCard;
