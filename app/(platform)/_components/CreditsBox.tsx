"use client";

import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { RocketIcon, Loader2, AlertCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { usePricingModalStore } from "@/hooks/use-pricing";
import { useSubscription } from "@/hooks/use-subscription";
import { useOrgStore } from "@/hooks/use-organization";

export const CreditsBox = () => {
  const { openModal } = usePricingModalStore();
  const { currentOrganization } = useOrgStore();
  const {
    subscription,
    isLoading: loading,
    error,
  } = useSubscription();




  const creditsDisplay = loading
    ? null
    : error || !subscription
      ? "0"
      : `${subscription.current_credits.toFixed(2)}`;

  return (
    <div className="bg-brand-card w-full flex flex-col gap-2 pt-4">
      <div className="flex justify-between text-brand-secondary-font text-sm">
        <p>Total RCU's Remaining</p>
        <div className="flex items-center gap-1">
          {creditsDisplay === null ? (
            <div className="flex items-center gap-1">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-xs text-brand-secondary-font">Loading...</span>
            </div>
          ) : (
            <p className="text-brand-secondary-font">{creditsDisplay}</p>
          )}
          <Tooltip>
            <TooltipTrigger>
              <AlertCircle className="h-4 w-4 " />
            </TooltipTrigger>
            <TooltipContent className="max-w-[250px]">
              This bar represents the total RCUs you have remaining in the plan
              tier you're using.
            </TooltipContent>
          </Tooltip>
        </div>
      </div>
      {/* <Progress value={getProgressValue()} /> */}
      {
        currentOrganization?.isAdmin ? (
          <PrimaryButton className="brand-gradient-indicator" onClick={openModal}>
            <RocketIcon />
            Upgrade
          </PrimaryButton>
        ) : null
      }

    </div>
  );
};