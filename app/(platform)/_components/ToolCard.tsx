"use client";

import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { motion, AnimatePresence } from "framer-motion";
import {
  ChevronDown,
  PackageIcon,
  Loader2,
  MoreVertical,
  Trash2,
  ChevronUp,
  Plus,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { sanitizeString } from "@/services/helper";
import Image from "next/image";

interface ToolCardProps {
  tool: any;
  oauthStatus?: {
    isAuthenticated: boolean;
    isLoading: boolean;
    toolName?: string;
    error?: string;
  };
  envKeyValues: { [key: string]: string };
  isGettingEnvs: boolean;
  isAddingEnvKeys: boolean;
  isRemovingOAuth: boolean;
  apiKeyConfig?: { [integrationId: string]: any[] };
  toolConnectionStatus?: Record<string, boolean>;
  onEnvKeyChange: (key: string, value: string) => void;
  onAddEnvKeys: () => void;
  onOAuthLogin: () => void;
  onOAuthDisconnect: () => void;
  onRemoveTool?: () => void;
  onDisableTool?: () => void;
}

const ToolCard: React.FC<ToolCardProps> = ({
  tool,
  oauthStatus,
  envKeyValues,
  isGettingEnvs,
  isAddingEnvKeys,
  isRemovingOAuth,
  apiKeyConfig,
  toolConnectionStatus,
  onEnvKeyChange,
  onAddEnvKeys,
  onOAuthLogin,
  onOAuthDisconnect,
  onRemoveTool,
  onDisableTool,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  // Helper function to check if Add Keys button should be disabled
  const isToolKeysButtonDisabled = () => {
    if (Object.keys(envKeyValues).length === 0) return true;

    // Check if ALL environment keys have values
    return !Object.entries(envKeyValues).every(([key, value]) => {
      return value.trim().length > 0;
    });
  };

  const hasEnvKeys =
    tool.integrations &&
    tool.integrations.length > 0 &&
    tool.integration_type === "api_key";
  const hasOAuth =
    tool.integrations &&
    tool.integrations.length > 0 &&
    tool.integration_type === "oauth";
  const needsConfiguration =
    (hasEnvKeys && !tool.is_connected) || (hasOAuth && !tool.is_connected);

  return (
    <div className="w-full">
      <Card
        className={`px-4 py-3 cursor-pointer border hover:border-border-primary transition-all ${
          isExpanded ? "border-border-primary rounded-b-none border-b-0" : ""
        }  ${needsConfiguration ? "border-[#FFC107]" : "border-border-muted"}`}
        onClick={(hasEnvKeys || hasOAuth) && toggleExpand}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center border flex-shrink-0">
              {tool.logo ? (
                <Image src={tool.logo} alt={tool.name} width={24} height={24} />
              ) : (
                <PackageIcon className="w-6 h-6 text-gray-500" />
              )}
            </div>
            <div className="w-full">
              <h3 className="text-base font-satoshi-bold">
                {sanitizeString(tool.name)}
              </h3>
              <p className="text-sm text-text-secondary line-clamp-2">
                {tool.description}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {(hasEnvKeys || hasOAuth) && (
              <div className="rounded-full border-1 border-black">
                {isExpanded ? (
                  <ChevronUp className="w-5 h-5 text-text-secondary" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-text-secondary" />
                )}
              </div>
            )}
            <DropdownMenu>
              <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
                  <MoreVertical className="h-5 w-5 text-text-secondary" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="min-w-[250px]">
                <DropdownMenuItem
                  onClick={(e) => {
                    e.stopPropagation();
                    onRemoveTool && onRemoveTool();
                  }}
                  className="cursor-pointer"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Remove tool
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </Card>

      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <Card
              className={`w-full border border-t-0 border-border-primary rounded-t-none p-0 ${needsConfiguration ? "border-[#FFC107]" : "border-border-muted"}`}
            >
              <div className="p-4">
                <div className="flex flex-col gap-6">
                  {/* Environment Keys Section */}
                  {hasEnvKeys && (
                    <div className="flex flex-col gap-[6px]">
                      {/* Show credentials if connected */}
                      {tool.is_connected ? (
                        <div className="mb-4">
                          {Object.entries(envKeyValues || {}).map(
                            ([key, value], idx) => (
                              <div
                                key={idx}
                                className="mb-2 flex flex-col gap-2"
                              >
                                <label className="flex gap-2 text-sm font-satoshi-bold mb-1 text-gray-900">
                                  {key}
                                </label>
                                <Input
                                  disabled={true}
                                  type="password"
                                  className="w-full border text-sm border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-300"
                                  value={value || ""}
                                />
                              </div>
                            )
                          )}
                          {isRemovingOAuth ? (
                            <Button className="min-w-[120px]" disabled>
                              <Loader2 className="w-4 h-4 animate-spin mr-2" />
                              Disconnecting...
                            </Button>
                          ) : (
                            <Button
                              variant="destructive"
                              className="min-w-[120px]"
                              onClick={onOAuthDisconnect}
                            >
                              Disconnect
                            </Button>
                          )}
                        </div>
                      ) : (
                        /* Show input fields if not connected */
                        <>
                          {isGettingEnvs ? (
                            <div className="flex items-center gap-2 mb-4">
                              <Loader2 className="w-4 h-4 animate-spin" />
                              <span className="text-sm">
                                Loading API key configuration...
                              </span>
                            </div>
                          ) : (
                            <>
                              {tool.integrations &&
                              tool.integrations.length > 0 &&
                              apiKeyConfig &&
                              apiKeyConfig[tool.integrations[0]] ? (
                                <>
                                  {/* Display API key config fields */}
                                  {apiKeyConfig[tool.integrations[0]].map(
                                    (config: any, idx: number) => (
                                      <div
                                        key={idx}
                                        className="mb-4 flex flex-col gap-2"
                                      >
                                        <label className="flex gap-2 text-sm font-satoshi-bold mb-1 text-gray-900">
                                          {config.name}
                                          {config.required && (
                                            <span className="text-red-500">
                                              *
                                            </span>
                                          )}
                                        </label>
                                        <Input
                                          disabled={isGettingEnvs}
                                          placeholder={
                                            config.description || config.name
                                          }
                                          type="password"
                                          className="w-full border text-sm border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-300"
                                          value={
                                            envKeyValues[config.name] || ""
                                          }
                                          onChange={(e) =>
                                            onEnvKeyChange(
                                              config.name,
                                              e.target.value
                                            )
                                          }
                                        />
                                      </div>
                                    )
                                  )}

                                  <Button
                                    className="w-[120px] mt-2"
                                    variant={"primary"}
                                    disabled={isAddingEnvKeys}
                                    onClick={() => onAddEnvKeys()}
                                  >
                                    {isAddingEnvKeys ? (
                                      <>
                                        <Loader2 className="w-4 h-4 animate-spin mr-2" />
                                        Adding...
                                      </>
                                    ) : (
                                      "Connect"
                                    )}
                                  </Button>
                                </>
                              ) : (
                                <>
                                  {/* Fallback to old method if apiKeyConfig is not available */}
                                  {Object.keys(envKeyValues).length > 0 && (
                                    <>
                                      {/* Display API key fields */}
                                      {Object.entries(envKeyValues).map(
                                        ([key, value], idx) => (
                                          <div
                                            key={idx}
                                            className="mb-4 flex flex-col gap-2"
                                          >
                                            <label className="flex gap-2 text-sm font-satoshi-bold mb-1 text-gray-900">
                                              {key}
                                            </label>
                                            <Input
                                              disabled={isGettingEnvs}
                                              placeholder={key}
                                              type="password"
                                              className="w-full border text-sm border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-300"
                                              value={value || ""}
                                              onChange={(e) =>
                                                onEnvKeyChange(
                                                  key,
                                                  e.target.value
                                                )
                                              }
                                            />
                                          </div>
                                        )
                                      )}

                                      <Button
                                        className="w-[120px] mt-2"
                                        variant={"primary"}
                                        disabled={
                                          isToolKeysButtonDisabled() ||
                                          isAddingEnvKeys
                                        }
                                        onClick={() => onAddEnvKeys()}
                                      >
                                        {isAddingEnvKeys ? (
                                          <>
                                            <Loader2 className="w-4 h-4 animate-spin mr-2" />
                                            Adding...
                                          </>
                                        ) : (
                                          "Connect"
                                        )}
                                      </Button>
                                    </>
                                  )}
                                </>
                              )}
                            </>
                          )}
                        </>
                      )}
                    </div>
                  )}

                  {/* OAuth Section */}
                  {hasOAuth && (
                    <div className="flex flex-col gap-[6px]">
                      {oauthStatus?.isLoading ? (
                        <Button disabled className="w-[120px]">
                          <Loader2 className="w-4 h-4 animate-spin mr-2" />
                          Loading...
                        </Button>
                      ) : tool?.is_connected ? (
                        <>
                          {isRemovingOAuth ? (
                            <Button disabled className="min-w-[120px] w-fit">
                              <Loader2 className="w-4 h-4 animate-spin mr-2" />
                              Disconnecting...
                            </Button>
                          ) : (
                            <Button
                              variant="destructive"
                              className="min-w-[120px] w-fit"
                              onClick={onOAuthDisconnect}
                            >
                              Disconnect
                            </Button>
                          )}
                        </>
                      ) : (
                        <Button
                          variant={"tertiary"}
                          className="min-w-[120px] w-fit"
                          onClick={onOAuthLogin}
                        >
                          <Plus />
                          Connect Your Account
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ToolCard;
