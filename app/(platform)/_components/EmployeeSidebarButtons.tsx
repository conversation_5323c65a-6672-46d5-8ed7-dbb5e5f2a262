"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { ClipboardList, UserPenIcon } from "lucide-react";
import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { createEmployeeRoute } from "@/shared/routes";

export const EmployeeSidebarButtons = () => {
  const pathname = usePathname();
  const router = useRouter();
  const { reset } = useEmployeeCreateStore();

  const handleCreateEmployee = () => {
    reset(); // Reset the form state before navigating
    router.push(createEmployeeRoute);
  };
  return (
    <div className="flex flex-col gap-2 w-full">
      <Link
        href="/dashboard/employees"
        className={`flex items-center gap-3 p-1 rounded-md transition-colors ${
          pathname === "/dashboard/employees"
            ? "bg-brand-clicked text-brand-primary font-semibold"
            : "hover:bg-brand-clicked hover:text-brand-primary text-brand-primary-font"
        }`}
      >
        <div
          className={`flex items-center justify-center w-6 h-6 ${
            pathname === "/dashboard/employees"
              ? "text-brand-primary"
              : "text-brand-primary-font"
          }`}
        >
          <ClipboardList className="w-5 h-5" strokeWidth={1.5} />
        </div>
        <span className="text-sm font-medium">View all employee</span>
      </Link>

      <button
        onClick={handleCreateEmployee}
        className={`flex items-center gap-3 p-1 rounded-md transition-colors hover:bg-brand-clicked hover:text-brand-primary text-brand-primary-font`}
      >
        <div
          className={`flex items-center justify-center w-6 h-6 text-brand-primary-font`}
        >
          <UserPenIcon className="w-5 h-5" strokeWidth={1.5} />
        </div>
        <span className="text-sm font-medium">Create employee</span>
      </button>
    </div>
  );
};
