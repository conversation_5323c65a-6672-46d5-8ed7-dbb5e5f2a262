"use client";

import { UploadCloud, Loader2 } from "lucide-react";
import React, { useCallback, useRef, useState } from "react";
import { gcsApi, uploadToGCS } from "@/app/api/gcs";

interface FileUploadProps {
  onUploadSuccess: (urls: any[]) => void;
  gcsPathPrefix: string;
  onUploadingStateChange?: (isUploading: boolean) => void;
  acceptedFileTypes?: string; // e.g., "image/*,.pdf"
  className?: string;
}

export const FileUpload: React.FC<FileUploadProps> = ({
  onUploadSuccess,
  gcsPathPrefix,
  onUploadingStateChange,
  acceptedFileTypes,
  className = "",
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [isUploadingInternally, setIsUploadingInternally] = useState(false);

  const handleFilesSelected = useCallback(
    async (selectedFiles: FileList | null) => {
      if (isUploadingInternally || !selectedFiles || selectedFiles.length === 0)
        return;

      onUploadingStateChange?.(true);
      setIsUploadingInternally(true);

      const validFiles: File[] = [];
      const unsupportedFiles: { name: string; type: string }[] = [];

      Array.from(selectedFiles).forEach((file) => {
        if (acceptedFileTypes) {
          const types = acceptedFileTypes.split(",").map((t) => t.trim());
          const fileType = file.type;
          const fileExtension = "." + file.name.split(".").pop()?.toLowerCase();
          if (
            types.some(
              (type) =>
                type === fileType ||
                type === fileExtension ||
                (type.endsWith("/*") &&
                  fileType.startsWith(type.replace("/*", "")))
            )
          ) {
            validFiles.push(file);
          } else {
            unsupportedFiles.push({
              name: file.name,
              type: fileType || fileExtension,
            });
          }
        } else {
          validFiles.push(file);
        }
      });

      if (unsupportedFiles.length > 0) {
        console.warn(
          "Unsupported file types:",
          unsupportedFiles.map((f) => `${f.name} (${f.type})`).join(", ")
        );
        alert(
          `Some file types were not supported. Please upload files of types: ${acceptedFileTypes}. Unsupported files: ${unsupportedFiles
            .map((f) => f.name)
            .join(", ")}`
        );
      }

      if (validFiles.length > 0) {
        try {
          const uploadPromises = validFiles.map(async (file) => {
            const presignedUrlResponse = await gcsApi.getPresignedUrl(
              file.name,
              file.type,
              `${gcsPathPrefix}`
            );
            const publicUrl = await uploadToGCS(presignedUrlResponse.url, file);
            const fileData = {
              file: publicUrl,
              name: file.name,
              size: file.size,
              created_at: new Date().toISOString(),
            };
            return fileData;
          });
          const newUrls = await Promise.all(uploadPromises);
          onUploadSuccess(newUrls);
        } catch (error) {
          console.error("Error during GCS upload:", error);
          alert(
            "An error occurred during file upload. Please check the console and try again."
          );
        }
      }

      setIsUploadingInternally(false);
      onUploadingStateChange?.(false);

      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    },
    [
      acceptedFileTypes,
      gcsPathPrefix,
      onUploadSuccess,
      onUploadingStateChange,
      isUploadingInternally,
    ]
  );

  const handleFileChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      handleFilesSelected(event.target.files);
    },
    [handleFilesSelected]
  );

  const handleClick = useCallback(() => {
    if (isUploadingInternally) return;
    fileInputRef.current?.click();
  }, [isUploadingInternally]);

  const handleDragEnter = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      if (isUploadingInternally) return;
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(true);
    },
    [isUploadingInternally]
  );

  const handleDragLeave = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      if (isUploadingInternally) return;
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(false);
    },
    [isUploadingInternally]
  );

  const handleDragOver = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      if (isUploadingInternally) return;
      event.preventDefault();
      event.stopPropagation();
    },
    [isUploadingInternally]
  );

  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      if (isUploadingInternally) return;
      event.preventDefault();
      event.stopPropagation();
      setIsDragging(false);

      handleFilesSelected(event.dataTransfer.files);
    },
    [handleFilesSelected, isUploadingInternally]
  );

  const baseClasses =
    "flex flex-col items-center justify-center p-8 border-2  border-dashed bg-brand-background rounded-lg text-center cursor-pointer transition-colors";
  const idleClasses =
    "border-brand-stroke hover:border-brand-stroke/80 bg-brand-background";
  const draggingClasses = "border-primary bg-primary/10";
  const loadingClasses =
    "border-gray-300 bg-gray-100 opacity-75 cursor-not-allowed";

  let currentClasses = idleClasses;
  if (isUploadingInternally) {
    currentClasses = loadingClasses;
  } else if (isDragging) {
    currentClasses = draggingClasses;
  }

  return (
    <div
      className={`${baseClasses} ${currentClasses} ${className}`}
      onClick={handleClick}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      role="button"
      tabIndex={isUploadingInternally ? -1 : 0}
      aria-disabled={isUploadingInternally}
      onKeyDown={(e) => {
        if (isUploadingInternally) return;
        if (e.key === "Enter" || e.key === " ") {
          handleClick();
        }
      }}
    >
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        className="hidden"
        accept={acceptedFileTypes}
        disabled={isUploadingInternally}
        multiple
      />
      {isUploadingInternally ? (
        <div className="flex flex-col items-center">
          <Loader2 className="mb-4 h-12 w-12 animate-spin text-primary" />
          <p className="text-lg font-semibold text-gray-700">Uploading...</p>
        </div>
      ) : (
        <>
          <UploadCloud
            className={`mb-4 h-12 w-12 ${
              isDragging ? "text-primary" : "text-gray-400"
            }`}
          />
          <p className="mb-2 text-lg font-semibold text-gray-700">
            Format supported
          </p>
          <div className="space-y-1 text-sm text-gray-500">
            <p>Text - PDF, DOCX, TXT</p>
            <p>Image - JPEG, PNG, SVG, WEBP</p>
            <p>Audio - MP3, M4A, WAV</p>
            <p>Video - MP4, WMV</p>
          </div>
        </>
      )}
    </div>
  );
};
