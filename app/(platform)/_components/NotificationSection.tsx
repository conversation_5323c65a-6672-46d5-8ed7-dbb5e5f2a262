"use client";

import { useInfiniteQuery, useQueryClient } from "@tanstack/react-query";
import { notificationApi } from "../../api/notification";
import { Notification } from "@/shared/interfaces";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { CheckCheckIcon } from "lucide-react";
import { EnvelopeIcon } from "@/components/customIcons/EnvelopeIcon";
import { cn, getTimeAgo } from "@/lib/utils";
import { useMutation } from "@tanstack/react-query";

const PAGE_SIZE = 10;

type NotificationPage = {
  notifications: Notification[];
  hasNextPage: boolean;
  nextPage: number | undefined;
  is_all_seen: boolean;
};

export function NotificationSection({ className }: { className?: string }) {
  const queryClient = useQueryClient();
  const {
    data,
    isLoading,
    isError,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery<NotificationPage, Error>({
    queryKey: ["notifications"],
    queryFn: async ({ pageParam }) => {
      const page = typeof pageParam === "number" ? pageParam : 1;
      const res = await notificationApi.getNotifications(page, PAGE_SIZE);
      return {
        notifications: res.data || [],
        nextPage: res.metadata?.hasNextPage ? page + 1 : undefined,
        hasNextPage: !!res.metadata?.hasNextPage,
        is_all_seen: res.is_all_seen,
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) =>
      lastPage.hasNextPage ? lastPage.nextPage : undefined,
    staleTime: 1000 * 60 * 5,
    refetchOnWindowFocus: false,
    refetchOnMount: true,
  });

  const markAsSeenMutation = useMutation({
    mutationFn: (id: string) => notificationApi.markAsSeen(id),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ["notifications"] }),
  });

  const markAllAsSeenMutation = useMutation({
    mutationFn: () => notificationApi.markAllAsSeen(),
    onSuccess: () =>
      queryClient.invalidateQueries({ queryKey: ["notifications"] }),
  });

  const notifications = data?.pages.flatMap((page) => page.notifications) || [];
  const hasUnread = data?.pages[0]?.is_all_seen;
  const showEmpty = !isLoading && notifications.length === 0;

  const handleNotificationClick = async (n: Notification) => {
    window.open(n.link, "_blank");
    if (!n.seen) {
      markAsSeenMutation.mutate(n.id);
    }
  };

  return (
    <div
      className={cn(
        "w-full flex flex-col gap-4 h-full overflow-hidden",
        className
      )}
    >
      <div className="flex justify-between items-center">
        <div className="text-xs font-satoshi-bold text-text-primary">
          Notifications
        </div>
        {notifications.length > 0 && (
          <Button
            variant="link"
            onClick={() => markAllAsSeenMutation.mutate()}
            disabled={
              markAllAsSeenMutation.isPending || (hasUnread && !isLoading)
            }
            className="text-xs flex items-center gap-[6px] text-primary !p-0 h-auto"
          >
            <CheckCheckIcon className="w-4 h-4" />
            Mark all as read
          </Button>
        )}
      </div>
      <div className="h-full overflow-y-auto scrollbar-hide">
        {isLoading ? (
          <div className="flex flex-1 items-center justify-center h-full text-xs text-muted-foreground">
            Loading notifications...
          </div>
        ) : isError ? (
          <div className="flex flex-1 items-center justify-center h-full text-xs text-destructive">
            {error instanceof Error
              ? error.message
              : "Failed to fetch notifications"}
          </div>
        ) : showEmpty ? (
          <div className="flex flex-1 flex-col items-center justify-center h-full">
            <EnvelopeIcon />
            <h3 className="text-xs text-text-primary mb-[6px] mt-[18px] text-center">
              You&apos;re all caught up on Ruh
            </h3>
            <p className="text-[10px] text-text-secondary font-satoshi-regular text-center">
              No new notifications, your dashboard is up to date and ready for
              your next task.
            </p>
          </div>
        ) : (
          <div className="flex flex-col gap-[6px]">
            {notifications.map((n) => (
              <Card
                key={n.id}
                onClick={() => handleNotificationClick(n)}
                className={cn(
                  "p-[6px] pt-2 rounded-[6px] border-1 border-border-muted bg-background-muted cursor-pointer hover:bg-background-accent",
                  !n.seen && "bg-background-accent hover:bg-primary-hover/20"
                )}
              >
                <CardContent className="p-0">
                  <div className="flex gap-2">
                    <div className="flex-shrink-0 w-10 h-10 rounded-lg flex items-center justify-center">
                      <img
                        src={n.logo}
                        alt={n.title}
                        className="w-10 h-10 rounded-lg"
                      />
                    </div>
                    <div className="flex flex-col gap-1">
                      <h4 className="text-text-primary text-xs">{n.title}</h4>
                      {n.description && (
                        <p className="text-[10px] text-text-secondary font-satoshi-regular">
                          {n.description}
                        </p>
                      )}
                      <Badge
                        variant="secondary"
                        className="text-[7px] text-text-secondary font-satoshi-bold bg-white h-[16px] px-[5px]"
                      >
                        {getTimeAgo(n.created_at)}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
            {hasNextPage && (
              <Button
                onClick={() => fetchNextPage()}
                disabled={isFetchingNextPage}
                className="mt-2 mx-auto text-xs px-4 py-1 text-primary"
                variant="link"
              >
                {isFetchingNextPage ? "Loading..." : "Load More"}
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
