"use client";

import { leftBarItems, noTextLogoPath } from "@/shared/constants";
import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { CustomSeparator } from "@/components/shared/CustomSeparator";
import { NotificationButton } from "@/components/shared/NotificationButton";
import { UserAvatar } from "@/components/shared/UserAvatar";
import { Button } from "@/components/ui/button";
import { useNotificationStore } from "@/hooks/use-notification";
import { useOrgStore } from "@/hooks/use-organization";
import { useSidebarStore } from "@/hooks/use-sidebar";
import { dashboardRoute, settingsRoute } from "@/shared/routes";
import { Edit, PanelRightCloseIcon, SettingsIcon } from "lucide-react";
import Link from "next/link";
import { useIsMobile } from "@/hooks/use-mobile";
import { useMixpanelTrack } from "@/hooks/useMixPanelTrack";
import { AnalyticsEvents } from "@/shared/enums";

export const LeftBar = () => {
  const router = useRouter();
  const pathname = usePathname();
  const { currentOrganization } = useOrgStore();
  const { isSecondaryBarOpen, toggleSecondarySidebar } = useSidebarStore();
  const { closeNotification } = useNotificationStore();
  const pathSegments = pathname.split("/").filter((segment) => segment !== "");
  // const lastSegment = pathSegments[pathSegments.length - 1];
  const lastSegment = pathSegments[0];
  const track = useMixpanelTrack();

  return (
    <div className="bg-brand-card fixed bottom-0 z-50 flex h-[80px] w-full items-center justify-around border-t sm:justify-center md:sticky md:top-0 md:h-screen md:w-[76px] md:flex-col md:justify-between md:border-r md:pt-6">
      {!isSecondaryBarOpen && (
        <div className="hidden md:absolute md:top-4 md:-right-6 md:block">
          <div
            onClick={toggleSecondarySidebar}
            role="button"
            className="bg-brand-background border-brand-stroke text-brand-primary-font cursor-pointer rounded-full border p-2 shadow-xl"
          >
            <PanelRightCloseIcon strokeWidth={1.2} />
          </div>
        </div>
      )}

      <div className="flex w-full flex-col md:items-center md:gap-6">
        <div className="hidden md:block">
          <Link href={dashboardRoute}>
            <Image src={noTextLogoPath} alt="Logo" width={32} height={32} />
          </Link>
        </div>
        {/* <div className="hidden md:block"> */}
        <CustomSeparator />
        {/* </div> */}
        {!isSecondaryBarOpen && (
          <div className="hidden md:block">
            <Button
              onClick={() => router.push(dashboardRoute)}
              variant="gradient"
              className="flex h-max w-max items-center gap-2"
            >
              <Edit className="h-4 w-4" />
            </Button>
          </div>
        )}

        <nav className="navbar-items flex items-center justify-around gap-2 py-2 md:flex-col">
          {leftBarItems.map((item) => {
            const href = item.href
              ? item.name === "Home"
                ? dashboardRoute
                : item.href
              : "#";
            const isActive =
              (item.name === "Home" &&
                (lastSegment === "chat" ||
                  pathname === dashboardRoute ||
                  !lastSegment)) ||
              (item.name !== "Home" &&
                lastSegment === item.href?.split("/").pop());

            return (
              <Link
                href={href}
                key={item.name}
                className={`flex h-16 w-16 cursor-pointer flex-col items-center justify-center space-y-1 rounded-sm transition-all duration-100 ease-in ${
                  isActive
                    ? "bg-brand-clicked text-brand-primary"
                    : "hover:bg-brand-card-hover hover:text-brand-primary"
                }`}
                onClick={() => {
                  const date = new Date();
                  track(item.analytics_event_name, {
                    location: window.location.href,
                    timestamp: date.toString(),
                  });
                  closeNotification();
                }}
              >
                <item.icon size={24} strokeWidth={1.2} />
                <span className="text-center text-xs">{item.name}</span>
              </Link>
            );
          })}

          <>
            <Link
              onClick={() => {
                const date = new Date();
                track(AnalyticsEvents.ADMIN_TAB_CLICKED, {
                  location: window.location.href,
                  timestamp: date.toString(),
                });
              }}
              href={settingsRoute}
              key="admin-settings"
              className={`flex h-16 w-16 cursor-pointer flex-col items-center justify-center space-y-1 rounded-sm transition-all duration-100 ease-in ${
                pathname.includes("admin-settings")
                  ? "bg-brand-clicked text-brand-primary"
                  : "hover:bg-brand-card-hover hover:text-brand-primary"
              }`}
            >
              <SettingsIcon size={24} strokeWidth={1.2} />
              <span className="text-center text-xs">
                {currentOrganization?.isAdmin ? "Admin" : "Manage"}
              </span>
            </Link>
          </>
          <div className="flex h-16 w-16 cursor-pointer flex-col items-center justify-center space-y-1 rounded-sm transition-all duration-100 ease-in md:hidden">
            <UserAvatar className="h-[24px] w-[24px] rounded-full" />
            <span className="text-center text-xs">Profile</span>
          </div>
        </nav>
      </div>

      <div className="hidden md:flex sm:gap-8 sm:pb-4 md:flex-col">
        <NotificationButton />
        {/* <ModeToggle /> */}
        <UserAvatar className="profile-dropdown h-[35px] w-[35px]" />
      </div>
    </div>
  );
};
