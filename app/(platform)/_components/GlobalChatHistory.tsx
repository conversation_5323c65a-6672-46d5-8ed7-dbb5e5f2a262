"use client";
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import ClipboardIcon from "@/public/assets/Icons-components/ClipboardIcon";
import { capitalizeFirstLetter, cn, getTimeAgo } from "@/lib/utils";
import { useInfiniteQuery } from "@tanstack/react-query";
import { communicationApi } from "@/app/api/communication";
import {
  Edit,
  Search,
  ChevronDown,
  ChevronRight,
  Bot,
  Loader2,
  History,
} from "lucide-react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import Image from "next/image";
import {
  GLOBAL_AGENT,
  LOCAL_STORAGE_KEYS,
  noTextLogoPath,
} from "@/shared/constants";
import { useRouter, useParams } from "next/navigation";
import { useEmployeeManagementStore } from "@/hooks/useEmployeeManagementStore";
import { useSearchHistoryStore } from "@/hooks/useSearchHistoryStore";
import { ChatType } from "@/shared/interfaces";
import { AnalyticsEvents, TaskStatus } from "@/shared/enums";
import {
  dashboardRoute,
  employeeChatRoute,
  globalChatRoute,
} from "@/shared/routes";
import { useMixpanelTrack } from "@/hooks/useMixPanelTrack";

const FILTERS = [
  { label: "All", value: "all" },
  { label: "Global", value: "global" },
  { label: "Employee", value: "agent" },
];

const PAGE_SIZE = 10;

type ChatSummary = {
  id: string;
  title: string;
  tasks?: {
    id: string;
    title: string;
    taskStatus?: TaskStatus;
    updatedAt: string;
    agentConversationId: string;
    agentId: string;
    sessionId: string;
    agentDetails?: {
      avatar?: string;
    };
  }[];
  agentDetails?: {
    avatar?: string;
  };
  updatedAt: string;
  chatType: ChatType;
  agentId: string;
};

// Function to get status dot color based on task status
const getStatusDotColor = (status?: TaskStatus) => {
  switch (status) {
    case TaskStatus.TASK_STATUS_COMPLETED:
      return "bg-green-500";
    case TaskStatus.TASK_STATUS_RUNNING:
      return "bg-orange-500";
    case TaskStatus.TASK_STATUS_FAILED:
      return "bg-red-500";
    case TaskStatus.TASK_STATUS_PAUSED:
      return "bg-yellow-500";
    case TaskStatus.TASK_STATUS_CANCELLED:
      return "bg-gray-500";
    case TaskStatus.TASK_STATUS_UNSPECIFIED:
    default:
      return "bg-gray-400";
  }
};

async function fetchGlobalChatHistory(
  filter: "all" | "global" | "agent",
  search: string,
  pageParam: number
) {
  let chatType = undefined;

  if (filter === "global") {
    chatType = ChatType.GLOBAL;
  } else if (filter === "agent") {
    chatType = ChatType.AGENT;
  }
  // For "all", chatType remains undefined to fetch all conversations

  const data = await communicationApi.getConversations({
    agentId: "",
    page: pageParam,
    limit: PAGE_SIZE,
    chatType,
    search: search.trim() || undefined,
  });
  return data;
}

// Loading skeleton component
const ChatItemSkeleton = () => (
  <div className="bg-background-muted rounded-sm p-2 flex items-center gap-2 animate-pulse">
    <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
    <div className="flex flex-col gap-1 flex-1">
      <div className="h-3 bg-gray-300 rounded w-3/4"></div>
      <div className="h-2 bg-gray-200 rounded w-1/3"></div>
    </div>
  </div>
);

const LoadingState = () => (
  <div className="flex flex-col gap-2">
    {Array.from({ length: 6 }).map((_, index) => (
      <ChatItemSkeleton key={index} />
    ))}
  </div>
);

// Infinite scroll loader component
const InfiniteScrollLoader = () => (
  <div className="flex items-center justify-center py-4">
    <Loader2 className="w-6 h-6 animate-spin text-purple-600" />
  </div>
);

export default function GlobalChatHistory({
  onItemClick,
}: {
  onItemClick?: () => void;
}) {
  const [filter, setFilter] = React.useState<"all" | "global" | "agent">("all");
  // On mount, update filter from localStorage if available
  React.useEffect(() => {
    const savedFilter = localStorage.getItem(
      LOCAL_STORAGE_KEYS.GLOBAL_CHAT_HISTORY_FILTER
    );
    if (
      savedFilter === "all" ||
      savedFilter === "global" ||
      savedFilter === "agent"
    ) {
      setFilter(savedFilter);
    }
  }, []);
  const [search, setSearch] = React.useState("");
  const [debouncedSearch, setDebouncedSearch] = React.useState("");
  const [expandedChats, setExpandedChats] = React.useState<Set<string>>(
    new Set()
  );
  const router = useRouter();
  const params = useParams();
  const selectedConversationId = React.useMemo(() => {
    try {
      const url = new URL(window.location.href);
      const fromQuery = url.searchParams.get("conversationId");
      if (fromQuery) return fromQuery;
    } catch {}
    return (params.conversationId as string) || "";
  }, [params]);
  const scrollContainerRef = React.useRef<HTMLDivElement>(null);

  const {
    initializeChatSession,
    setSelectedEmployeeId,
    setChatStarted,
    setSessionData,
  } = useEmployeeManagementStore();

  const { openSearchHistory } = useSearchHistoryStore();

  // Debounce search input
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  // Infinite query for chat history
  const {
    data,
    isLoading,
    isError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = useInfiniteQuery({
    queryKey: ["globalChatHistory", filter, debouncedSearch],
    queryFn: ({ pageParam = 1 }) =>
      fetchGlobalChatHistory(filter, debouncedSearch, pageParam),
    getNextPageParam: (lastPage, allPages) => {
      const currentPage = allPages.length;
      const totalPages = Math.ceil((lastPage.metadata?.total || 0) / PAGE_SIZE);
      return currentPage < totalPages ? currentPage + 1 : undefined;
    },
    initialPageParam: 1,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
  const track = useMixpanelTrack();

  // Flatten all pages data
  const chatsHistory = React.useMemo(() => {
    return data?.pages.flatMap((page) => page.data) || [];
  }, [data]);

  // Handle scroll for infinite loading
  const handleScroll = React.useCallback(() => {
    if (!scrollContainerRef.current || !hasNextPage || isFetchingNextPage) {
      return;
    }

    const { scrollTop, scrollHeight, clientHeight } =
      scrollContainerRef.current;
    const scrollPercentage = (scrollTop + clientHeight) / scrollHeight;

    // Trigger next page when scrolled 80% down
    if (scrollPercentage > 0.8) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  // Attach scroll listener
  React.useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    container.addEventListener("scroll", handleScroll);
    return () => container.removeEventListener("scroll", handleScroll);
  }, [handleScroll]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  };

  // Handle Enter key press in search
  const handleSearchKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      // If search is empty, trigger refetch to get all results
      if (!search.trim()) {
        setDebouncedSearch("");
        refetch();
      }
    }
  };

  const handleHistoryClick = () => {
    openSearchHistory();
  };

  const toggleChatExpansion = (chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setExpandedChats((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(chatId)) {
        newSet.delete(chatId);
      } else {
        newSet.add(chatId);
      }
      return newSet;
    });
  };

  const handleChatClick = (chat: ChatSummary) => {
    // Call the onItemClick callback if provided
    if (onItemClick) {
      onItemClick();
    }
    if (chat.chatType === ChatType.GLOBAL) {
      // Global chat routes to global-agent
      router.push(`${globalChatRoute}/${chat.id}`);
      setSelectedEmployeeId(GLOBAL_AGENT.id + chat.id);
      initializeChatSession(GLOBAL_AGENT.id + chat.id);
      setChatStarted(GLOBAL_AGENT.id + chat.id, true);
    } else if (chat.chatType === ChatType.AGENT) {
      localStorage.removeItem(LOCAL_STORAGE_KEYS.IS_NEW_CHAT);
      router.push(
        `${employeeChatRoute}/${chat.agentId}?conversationId=${chat.id}`
      );
      const employeeKey = `${chat.agentId}::${chat.id}`;
      setSelectedEmployeeId(employeeKey);
      initializeChatSession(employeeKey);
      setChatStarted(employeeKey, true);
    }
  };

  const handleTaskClick = (
    task: NonNullable<ChatSummary["tasks"]>[0],
    e: React.MouseEvent
  ) => {
    e.stopPropagation(); // Prevent triggering parent chat click

    // Call the onItemClick callback if provided
    if (onItemClick) {
      onItemClick();
    }

    // Save current filter before navigation
    localStorage.setItem(LOCAL_STORAGE_KEYS.GLOBAL_CHAT_HISTORY_FILTER, filter);
    // Task routes to employees/chat/{agentId} with conversation ID as query param
    // Only set the session data with existing sessionId if task status is RUNNING
    if (task.taskStatus === TaskStatus.TASK_STATUS_RUNNING) {
      setSessionData(task.agentId, task.agentConversationId, task.sessionId);
    }
    const navigationUrl = `${employeeChatRoute}/${task.agentId}?conversationId=${task.agentConversationId}`;
    const employeeKey = `${task.agentId}::${task.agentConversationId}`;
    setSelectedEmployeeId(employeeKey);
    initializeChatSession(employeeKey);
    setChatStarted(employeeKey, true);
    router.push(navigationUrl);
  };

  const renderChatItem = (chat: ChatSummary) => {
    const isSelected =
      chat.chatType === ChatType.AGENT
        ? chat.id === selectedConversationId
        : chat.id === selectedConversationId;
    const isExpanded = expandedChats.has(chat.id);
    const hasTask =
      chat.chatType === ChatType.GLOBAL && chat.tasks && chat.tasks.length > 0;
    const isAgent = chat.chatType === ChatType.AGENT;

    if (!chat.title) {
      return null;
    }

    return (
      <div key={chat.id} className="flex flex-col">
        {/* Main Chat Item */}
        <div
          className={cn(
            "bg-border-muted rounded-sm p-2 flex flex-col cursor-pointer transition-colors group",
            "hover:bg-color-light",
            isSelected && "bg-color-light border border-brand-primary shadow-sm"
          )}
          aria-current={isSelected ? "true" : undefined}
          onClick={() => handleChatClick(chat)}
        >
          {/* Main conversation header */}
          <div className="flex items-center gap-2 mb-2">
            <Avatar className="bg-white group-hover:bg-color-light flex items-center justify-center">
              {isAgent ? (
                chat.agentDetails?.avatar ? (
                  <AvatarImage src={chat.agentDetails.avatar} />
                ) : (
                  <AvatarFallback>
                    <Bot className="w-4 h-4 text-gray-600" />
                  </AvatarFallback>
                )
              ) : (
                <Image src={noTextLogoPath} alt="Logo" width={18} height={18} />
              )}
            </Avatar>
            <div className="flex flex-col gap-1 flex-1">
              <div className="text-[12px] font-satoshi-medium text-text-primary line-clamp-1">
                {capitalizeFirstLetter(chat.title)}
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center px-[5px] py-[2px] rounded-md bg-white w-fit text-text-secondary font-satoshi-bold text-[7px]">
                  {getTimeAgo(chat.updatedAt)}
                </div>
              </div>
            </div>
            {hasTask && (
              <button
                onClick={(e) => toggleChatExpansion(chat.id, e)}
                className="p-1 hover:bg-gray-200 rounded transition-colors"
              >
                {isExpanded ? (
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronRight className="w-4 h-4 text-gray-500" />
                )}
              </button>
            )}
          </div>

          {/* Tasks List (shown when expanded) */}
          {hasTask && isExpanded && (
            <>
              {/* Separator */}
              <div className="h-px bg-gray-200 mb-2"></div>

              {/* Tasks */}
              <div className="space-y-1">
                {chat.tasks!.map((task) => (
                  <div
                    key={task.id}
                    className="p-2 flex items-center gap-2 bg-white rounded-sm hover:bg-gray-50 transition-colors cursor-pointer"
                    onClick={(e) => handleTaskClick(task, e)}
                  >
                    {/* Bot Avatar with Status Dot */}
                    <div className="relative">
                      <Avatar className="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center">
                        {task.agentDetails?.avatar ? (
                          <AvatarImage src={task.agentDetails.avatar} />
                        ) : (
                          <AvatarFallback>
                            <Bot className="w-3 h-3 text-purple-600" />
                          </AvatarFallback>
                        )}
                      </Avatar>
                      {/* Status Dot */}
                      <div
                        className={cn(
                          "absolute -bottom-0.5 -right-0.5 w-2 h-2 rounded-full border border-white",
                          getStatusDotColor(task.taskStatus)
                        )}
                      />
                    </div>

                    {/* Task Content */}
                    <div className="flex-1 min-w-0">
                      <div className="text-[10px] font-satoshi-medium text-text-primary line-clamp-1">
                        {capitalizeFirstLetter(task.title)}
                      </div>
                    </div>

                    {/* Timestamp */}
                    <div className="text-[8px] text-text-secondary font-satoshi-regular">
                      {getTimeAgo(task.updatedAt)}
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>
      </div>
    );
  };

  return (
    <aside className="w-full h-full bg-white flex flex-col">
      {/* Fixed Header Section */}
      <div className="flex-shrink-0">
        {/* Header */}
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-xs font-satoshi-bold text-text-primary">
            Recent Chats
          </h2>
          <Button
            onClick={() => {
              const date = new Date();
              track(AnalyticsEvents.NEW_CHAT_CLICKED, {
                location: window.location.href,
                timestamp: date.toString(),
              });
              // Save current filter before navigation
              localStorage.setItem(
                LOCAL_STORAGE_KEYS.GLOBAL_CHAT_HISTORY_FILTER,
                filter
              );
              router.push(dashboardRoute);
              if (onItemClick) {
                onItemClick();
              }
            }}
            variant="gradient"
            className="flex items-center gap-2 h-[26px]"
          >
            <Edit className="w-4 h-4" />
            New Chat
          </Button>
        </div>

        {/* Search Bar */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            value={search}
            onChange={handleSearchChange}
            onKeyPress={handleSearchKeyPress}
            placeholder="Search Chat"
            className="pl-10 pr-10 h-8 text-sm bg-background-muted border-none rounded-md focus-visible:ring-0"
          />
          {/* History Icon */}
          <button
            onClick={() => {
              // Save current filter before navigation
              localStorage.setItem(
                LOCAL_STORAGE_KEYS.GLOBAL_CHAT_HISTORY_FILTER,
                filter
              );
              handleHistoryClick();
              if (onItemClick) {
                onItemClick();
              }
            }}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded transition-colors"
            title="Search History"
          >
            <History className="h-4 w-4 text-gray-400 hover:text-primary" />
          </button>
        </div>

        {/* Filter Tabs */}
        <div className="flex items-center gap-2 mb-3">
          <span className="text-sm font-satoshi-regular">Filter:</span>
          <Tabs
            value={filter}
            onValueChange={(value) => {
              const typedValue = value as "all" | "global" | "agent";
              setFilter(typedValue);
              // Save filter selection to localStorage
              localStorage.setItem(
                LOCAL_STORAGE_KEYS.GLOBAL_CHAT_HISTORY_FILTER,
                typedValue
              );
            }}
          >
            <TabsList className="bg-white border-none gap-2">
              {FILTERS.map((f) => (
                <TabsTrigger
                  key={f.value}
                  value={f.value}
                  className={cn(
                    "px-2 py-1 rounded-xs text-xs font-satoshi-regular text-text-primary h-6",
                    filter === f.value && "shadow"
                  )}
                >
                  {f.label}
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>
        </div>
      </div>

      {/* Scrollable Chat List Section */}
      <div
        ref={scrollContainerRef}
        className="flex-1 overflow-y-auto scrollbar-hide"
      >
        {isLoading ? (
          <LoadingState />
        ) : isError ? (
          <div className="flex-1 flex flex-col items-center justify-center">
            <InfiniteScrollLoader />
          </div>
        ) : chatsHistory.length === 0 ||
          (chatsHistory?.length == 1 && chatsHistory[0]?.title == null) ? (
          <div className="flex-1 flex flex-col gap-2 items-center justify-center mt-[86px]">
            <div className="rounded-full w-12 h-12 flex items-center justify-center bg-color-light-secondary">
              <ClipboardIcon width={28} height={28} />
            </div>
            <div className="text-center text-disabled text-xs font-satoshi-regular">
              {search.trim() ? (
                <>
                  No chats found for
                  <br />"{search}"
                </>
              ) : (
                <>
                  Start fresh — no chat
                  <br />
                  history found
                </>
              )}
            </div>
          </div>
        ) : (
          <div className="flex flex-col gap-2">
            {chatsHistory.map((chat) => renderChatItem(chat as ChatSummary))}
            {/* Infinite scroll loader - only show if there's a next page and we're fetching */}
            {isFetchingNextPage && hasNextPage && <InfiniteScrollLoader />}
          </div>
        )}
      </div>
    </aside>
  );
}
