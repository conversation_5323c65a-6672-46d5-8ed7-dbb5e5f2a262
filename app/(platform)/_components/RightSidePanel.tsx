import AudioPlayerCard from "@/components/shared/AudioPlayerCard";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON>Content,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useEmployeeManagementStore } from "@/hooks/useEmployeeManagementStore";
import { sanitizeString } from "@/services/helper";
import { AnimatePresence, motion } from "framer-motion";
import { CopyIcon, X } from "lucide-react";
import React, { useState } from "react";
import ReactMarkdown from "react-markdown";
import CustomVideoPlayer from "../../../components/shared/CustomVideoPlayer";

interface RightSidePanelProps {
  isOpen: boolean;
}

const isSemanticTypeArray = (semantic_type: string) =>
  ["array_of_string", "array", "array_of_object", "object"].includes(
    semantic_type
  );

const renderContent = (data: any, idx?: number | string) => {
  if (!data) return null;
  //add image content
  if (data.semantic_type === "image") {
    return (
      <div key={`image-${data.property_name}-${idx}`}>
        <div className="font-satoshi-bold text-base capitalize mb-1">
          {sanitizeString(data.property_name)}
        </div>
        <img
          src={data.data}
          alt={data.property_name}
          className="w-full h-auto object-cover bg-no-repeat bg-center"
        />
      </div>
    );
  }

  // Add URL iframe rendering
  if (data.semantic_type === "url" && typeof data.data === "string") {
    return (
      <div key={`url-${data.property_name}-${idx}`}>
        <div className="font-satoshi-bold text-base capitalize mb-3 text-brand-primary-font">
          {sanitizeString(data.property_name)}
        </div>
        <div className="rounded-xl overflow-hidden shadow-lg border-2 border-brand-stroke h-[50vh] bg-brand-card">
          <iframe
            src={data.data}
            className="w-full h-full"
            style={{ border: "none" }}
            allowFullScreen
            sandbox="allow-same-origin allow-scripts allow-popups allow-forms"
            title={data.property_name || "Website"}
          />
        </div>
        <div className="mt-3 flex justify-end">
          <Button
            onClick={() => window.open(data.data, "_blank")}
            className="flex items-center gap-2 bg-brand-primary hover:bg-brand-primary-hover text-brand-white-text border-0 font-satoshi-medium px-4 py-2 rounded-lg transition-colors duration-200"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
              />
            </svg>
            Open in New Tab
          </Button>
        </div>
      </div>
    );
  }

  if (
    data.semantic_type === "ppt" &&
    typeof data.data === "string" &&
    data.data.includes("https://")
  ) {
    // Encode the PPT URL for Google Document Viewer without toolbar
    const encodedPptUrl = encodeURIComponent(data.data);
    const googleViewerUrl = `https://docs.google.com/gview?url=${encodedPptUrl}&embedded=true&widget=false&chrome=false`;

    const handleDownload = () => {
      const link = document.createElement("a");
      link.href = data.data;
      link.download = `${data.property_name || "presentation"}.pptx`;
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    };

    return (
      <div key={`ppt-${data.property_name}-${idx}`}>
        <div className="font-satoshi-bold text-base capitalize mb-3 text-brand-primary-font">
          {sanitizeString(data.property_name)}
        </div>
        <div className="rounded-xl overflow-hidden shadow-lg border-2 border-brand-stroke h-[80vh] bg-brand-card">
          <iframe
            src={googleViewerUrl}
            className="w-full h-full"
            style={{ border: "none" }}
            allowFullScreen
            title={data.property_name || "PowerPoint Presentation"}
          />
        </div>
        <div className="mt-3 flex justify-end">
          <Button
            onClick={handleDownload}
            className="flex items-center gap-2 bg-brand-primary hover:bg-brand-primary-hover text-brand-white-text border-0 font-satoshi-medium px-4 py-2 rounded-lg transition-colors duration-200"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            Download PPT
          </Button>
        </div>
      </div>
    );
  }

  if (data.semantic_type === "string") {
    return (
      <div key={`string-${data.property_name}-${idx}`}>
        <div className="font-satoshi-bold text-base capitalize mb-1">
          {sanitizeString(data.property_name)}
        </div>
        <div className="font-satoshi-regular text-sm">
          <ReactMarkdown>{data.data}</ReactMarkdown>
        </div>
      </div>
    );
  } else if (
    (data.semantic_type === "audio" || data.semantic_type === "video") &&
    typeof data.data === "string" &&
    data.data.includes("https://")
  ) {
    return (
      <div key={`media-${data.property_name}-${idx}`}>
        <div className="font-satoshi-bold text-base capitalize mb-1">
          {sanitizeString(data.property_name)}
        </div>
        {data.semantic_type === "audio" && (
          <AudioPlayerCard
            key={`media-${data.property_name}-${idx}`}
            src={data.data}
          />
        )}

        {data.semantic_type === "video" && (
          <CustomVideoPlayer
            key={`media-${data.property_name}-${idx}`}
            url={data.data}
          />
        )}
      </div>
    );
  } else {
    return (
      <div key={`fallback-${data.property_name}-${idx}`}>
        <div className="font-satoshi-bold text-base capitalize mb-1">
          {sanitizeString(data.property_name)}
        </div>
        <div className="font-satoshi-regular text-sm">
          <ReactMarkdown>{String(data.data)}</ReactMarkdown>
        </div>
      </div>
    );
  }
};

function renderResultItem(item: any, key: string | number) {
  if (!item) return null;

  // Handle plain string results that don't have semantic_type structure
  if (typeof item === "string") {
    return (
      <div key={`string-result-${key}`} className="mb-6">
        <div className="font-satoshi-regular text-lg">
          <ReactMarkdown>{item}</ReactMarkdown>
        </div>
      </div>
    );
  }

  // Handle non-object types (numbers, booleans, etc.)
  if (typeof item !== "object") {
    return (
      <div key={`primitive-result-${key}`} className="mb-6">
        <div className="font-satoshi-regular text-sm">{String(item)}</div>
      </div>
    );
  }

  if (isSemanticTypeArray(item.semantic_type) && Array.isArray(item.data)) {
    return (
      <div className="mb-6 flex flex-col gap-6" key={key}>
        {item.data.map((child: any, idx: number) =>
          renderResultItem(child, `${key}-${idx}`)
        )}
      </div>
    );
  }
  return renderContent(item, key);
}

export const RightSidePanel: React.FC<RightSidePanelProps> = ({ isOpen }) => {
  const setIsSplitView = useEmployeeManagementStore((s) => s.setIsSplitView);
  const selectedStep = useEmployeeManagementStore((s) => s.selectedStep);
  const workflowStepResults = useEmployeeManagementStore(
    (s) => s.workflowStepResults
  );

  let result = null;
  if (workflowStepResults.hasOwnProperty(selectedStep?.transition_id)) {
    result = workflowStepResults[selectedStep.transition_id];
  } else {
    result = selectedStep?.result;
  }

  const [copied, setCopied] = useState(false);

  const handleCopy = () => {
    if (!result) return;
    let text = "";

    // Handle plain string results
    if (typeof result === "string") {
      text = result;
    } else {
      function flatten(item: any): string {
        if (!item) return "";
        if (Array.isArray(item)) {
          return item.map(flatten).join("\n\n");
        } else if (typeof item === "object" && item.semantic_type) {
          if (
            isSemanticTypeArray(item.semantic_type) &&
            Array.isArray(item.data)
          ) {
            return flatten(item.data);
          } else {
            return `${item.property_name || ""}\n${item.data || ""}`;
          }
        } else if (typeof item === "object") {
          return Object.values(item).map(flatten).join("\n");
        } else {
          return String(item);
        }
      }
      text = flatten(result);
    }

    navigator.clipboard.writeText(text);
    setCopied(true);
    setTimeout(() => setCopied(false), 1500);
  };

  const handleClose = () => {
    setIsSplitView(false);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ x: 100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          exit={{ x: 100, opacity: 0 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          className="w-full bg-white shadow-2xl mt-2 rounded-lg flex flex-col border-l border-gray-200 h-full z-30 p-8 gap-6"
        >
          {/* Header */}
          <div className="flex items-center justify-between border-b">
            <div className="flex items-center gap-2">
              <Button
                variant="linkSecondary"
                size="icon"
                onClick={handleClose}
                title="Close"
                className="!p-0 w-auto h-auto"
              >
                <X className="!w-6 !h-6" />
              </Button>
              <div className="font-bold text-lg text-brand-primary-font line-clamp-1">
                {sanitizeString(selectedStep?.display_name?.split("-")[0]) ||
                  "Workflow Step"}
              </div>
            </div>

            <div className="flex gap-2">
              <TooltipProvider>
                <Tooltip open={copied ? true : undefined}>
                  <TooltipTrigger asChild>
                    <Button
                      variant="linkSecondary"
                      size="icon"
                      title="Copy"
                      onClick={handleCopy}
                    >
                      <CopyIcon width={32} height={32} />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">
                    {copied ? "Copied!" : "Click to copy"}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          {/* Body */}
          <div className="flex-1 overflow-y-auto gap-6">
            {result ? (
              Array.isArray(result) ? (
                result.map((item, idx) =>
                  renderResultItem(
                    item,
                    item.property_name ? `${item.property_name}-${idx}` : idx
                  )
                )
              ) : (
                renderResultItem(result, 0)
              )
            ) : (
              <div className="text-gray-400 text-sm">No result to display.</div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default RightSidePanel;
