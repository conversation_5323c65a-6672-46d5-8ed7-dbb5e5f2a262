"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Employee } from "@/shared/interfaces";
import { capitalizeFirstLetter } from "@/lib/utils";
import { Eye } from "lucide-react";
import { EmployeeAvatar } from "@/components/shared/EmployeeAvatar";
import { useRouter } from "next/navigation";
import { useEmployeeManagementStore } from "@/hooks/useEmployeeManagementStore";
import { employeeChatRoute } from "@/shared/routes";

interface DelegationCardProps {
  employee: Employee;
  onApprove: (employee: Employee) => Promise<void>;
  isApproveLoading: boolean;
  isApproved?: boolean;
  isTaskStarted?: boolean;
  taskData?: {
    task_id: string;
    agent_id: string;
    conversation_id: string;
    agent_session_id: string;
    title: string;
  };
}

const DelegationCard = ({
  employee,
  onApprove,
  isApproveLoading,
  isApproved = false,
  isTaskStarted = false,
  taskData,
}: DelegationCardProps) => {
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);

  const { setSelectedEmployeeId, initializeChatSession, setChatStarted } =
    useEmployeeManagementStore();
  const handleApprove = async () => {
    try {
      await onApprove(employee);
    } catch (error) {
      console.error("Error approving delegation:", error);
    }
  };

  const handleViewProcess = async () => {
    if (taskData) {
      setIsNavigating(true);
      const navigationUrl = `${employeeChatRoute}/${taskData.agent_id}?conversationId=${taskData.conversation_id}`;
      const employeeKey = `${taskData.agent_id}::${taskData.conversation_id}`;
      setSelectedEmployeeId(employeeKey);
      initializeChatSession(employeeKey);
      setChatStarted(employeeKey, true);
      router.push(navigationUrl);
      // Note: setIsNavigating(false) is not needed as the component will unmount during navigation
    } else {
      console.error("No task data available for navigation");
    }
  };

  const getActionButton = () => {
    if (isTaskStarted && taskData) {
      return (
        <Button
          onClick={handleViewProcess}
          disabled={isNavigating}
          className="bg-brand-primary hover:bg-brand-primary/80 text-white px-8 py-2 rounded-lg font-medium flex items-center gap-2 min-w-[180px]"
        >
          {isNavigating ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              Redirecting...
            </>
          ) : (
            <>
              <Eye className="w-4 h-4" />
              View full process
            </>
          )}
        </Button>
      );
    } else if (isApproved) {
      return (
        <Button
          disabled
          className="bg-green-600 text-white px-8 py-2 rounded-lg font-medium min-w-[120px] opacity-75"
        >
          Approved ✓
        </Button>
      );
    } else {
      return (
        <Button
          onClick={handleApprove}
          disabled={isApproveLoading}
          className="bg-brand-primary hover:bg-brand-primary/80 text-white px-8 py-2 rounded-lg font-medium min-w-[120px]"
        >
          {isApproveLoading ? "Approving..." : "Approve"}
        </Button>
      );
    }
  };

  // Helper function to truncate description
  const truncateDescription = (text: string, maxLength: number = 80) => {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength).trim() + "...";
  };

  // Workflow steps - static like in the image
  const workflowSteps = [
    { id: 1, name: "Data Fetching and Execution", completed: true },
    { id: 2, name: "Refine output and finalize results", completed: false },
  ];

  return (
    <div className="w-full max-w-[600px] bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
      {/* Header */}
      <div className="p-6 pb-4">
        <h3 className="text-md font-semibold text-gray-900 mb-6">
          Found an correct employee for you, Meet 👇
        </h3>

        {/* Employee Info */}
        <div className="flex items-start gap-4 mb-6 border border-gray-200 rounded-lg p-4">
          <div className="hover:bg-brand-clicked hover:text-brand-primary p-2 rounded-lg flex flex-col items-center justify-start gap-2 bg-brand-clicked h-[max-content] text-brand-primary">
            <EmployeeAvatar
              src={employee.avatar}
              name={employee.name}
              className="w-16 h-16 flex-shrink-0 rounded-sm"
            />
          </div>

          <div className="flex-1 min-w-0">
            <h4 className="text-lg font-semibold text-gray-900 mb-1">
              {capitalizeFirstLetter(employee.name)}
            </h4>
            <p className="text-text-secondary text-sm line-clamp-2">
              {truncateDescription(
                employee.description ||
                  "Hi there! I am Julia. I am a marketing expert"
              )}
            </p>
          </div>
        </div>

        {/* Workflow Steps */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-900 mb-4">
            Here are the steps I will be following to execute your task:
          </h4>
          <div className="space-y-3">
            {workflowSteps.map((step) => (
              <div key={step.id}>
                <span className="text-base font-bold text-gray-900">
                  Step {step.id} : {step.name}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Action Button */}
        <div className="flex justify-end">{getActionButton()}</div>
      </div>
    </div>
  );
};

export default DelegationCard;
