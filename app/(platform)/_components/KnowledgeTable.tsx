import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  EyeIcon,
  DownloadIcon,
  Trash2Icon,
  SearchIcon,
  ChevronLeftIcon,
} from "lucide-react";
import { useState } from "react";

interface KnowledgeTableProps {
  files: string[];
  deleteFile: (file: File | string) => void;
  previewFile: (file: File | string) => void;
  downloadFile?: (file: File | string) => void;
  closeTable: () => void;
}

export const KnowledgeTable = ({
  files,
  deleteFile,
  previewFile,
  downloadFile,
  closeTable,
}: KnowledgeTableProps) => {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredFiles = files.filter((file) =>
    file.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-6 ">
      <div className="flex items-center mb-4">
        <Button
          variant="ghost"
          size="icon"
          className="mr-2"
          onClick={closeTable}
        >
          <ChevronLeftIcon className="w-6 h-6" />
        </Button>
        <h1 className="text-lg font-semibold">All files</h1>
      </div>
      <div className="relative mb-4">
        <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
        <Input
          placeholder="Search..."
          className="pl-10 w-full border-gray-300 focus:border-brand-primary rounded-md"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
        />
      </div>
      <Table>
        <TableHeader>
          <TableRow className="border-b-brand-input-color">
            <TableHead className="text-brand-primary-font font-semibold">
              File
            </TableHead>
            <TableHead className="text-brand-primary-font font-semibold text-right">
              More
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody className="text-lg">
          {filteredFiles.map((file) => (
            <TableRow
              key={file}
              className="border-b-brand-input-color hover:bg-brand-card-hover"
            >
              <TableCell className="font-medium py-4">
                <div className="flex items-center">{file}</div>
              </TableCell>
              <TableCell className="flex gap-3 justify-end py-4">
                <button
                  onClick={() => previewFile(file)}
                  className="text-brand-secondary-font hover:text-brand-primary"
                >
                  <EyeIcon className="w-5 h-5" />
                </button>
                {downloadFile && (
                  <button
                    onClick={() => downloadFile(file)}
                    className="text-brand-secondary-font hover:text-brand-primary"
                  >
                    <DownloadIcon className="w-5 h-5" />
                  </button>
                )}
                <button
                  onClick={() => deleteFile(file)}
                  className="text-brand-secondary-font hover:text-red-500"
                >
                  <Trash2Icon className="w-5 h-5" />
                </button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
