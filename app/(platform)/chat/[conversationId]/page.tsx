"use client";
import ChatInput from "@/app/(platform)/_components/ChatInput";
import ChatMessagesList from "@/app/(platform)/_components/ChatMessagesList";
import { communicationApi } from "@/app/api/communication";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useIsMobile } from "@/hooks/use-mobile";
import { useEmployeeManagementStore } from "@/hooks/useEmployeeManagementStore";
import { useSSEStream } from "@/hooks/useSSEStream";
import { cn } from "@/lib/utils";
import { filterAndTransformMessages } from "@/lib/utils/messageTransform";
import TokenInfoIcon from "@/public/assets/Icons-components/TokenInfoIcon";
import { GLOBAL_AGENT, LIMIT, LOCAL_STORAGE_KEYS } from "@/shared/constants";
import { AIChatMode } from "@/shared/enums";
import { Employee } from "@/shared/interfaces";
import { employeeChatRoute } from "@/shared/routes";
import { useQuery } from "@tanstack/react-query";
import { ArrowDownIcon, ArrowUpIcon } from "lucide-react";
import { useParams, useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";

export default function ChatPage() {
  const params = useParams();
  const conversationId = Array.isArray(params.conversationId)
    ? params.conversationId[0]
    : params.conversationId;
  const {
    initializeChatSession,
    setSelectedEmployeeId,
    setSessionData,
    resetStream,
    chat: { sessions },
    employee: { selectedId },
    loadChatHistory,
    getSessionData,
    isSessionExpired,
    setInputOutputTokens,
    setIsLoadingMoreMessages,
    loadMoreMessages,
    setTyping,
    setThinkingStatusText,
  } = useEmployeeManagementStore();
  const isMobile = useIsMobile();
  const router = useRouter();
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [contextWindow, setContextWindow] = useState(1000000);
  const session = conversationId
    ? sessions[GLOBAL_AGENT.id + conversationId]
    : undefined;
  const chatStarted = session?.chatStarted;
  useSSEStream({ sessionId, enabled: !!sessionId });

  // Pending message/attachment send logic (from previous route)
  useEffect(() => {
    const pendingMessage = localStorage.getItem(
      LOCAL_STORAGE_KEYS.RUH_GLOBAL_AGENT_PENDING_MESSAGE
    );
    const sendPendingMessage = async () => {
      if (!pendingMessage || !sessionId) return;
      const { message, attachments, tools, selectedMode } =
        JSON.parse(pendingMessage);
      communicationApi.sendMessage(
        message,
        sessionId,
        attachments || [],
        selectedMode,
        tools || []
      );
      setTimeout(() => {
        localStorage.removeItem(
          LOCAL_STORAGE_KEYS.RUH_GLOBAL_AGENT_PENDING_MESSAGE
        );
      }, 1000);
    };

    sendPendingMessage();
    if (pendingMessage && conversationId) {
      setTyping(true);
      setThinkingStatusText("Thinking");
    }
  }, [sessionId]);

  // React Query for messages
  const { isFetching: isMessagesLoading, refetch: fetchMessages } = useQuery({
    queryKey: ["messages", conversationId],
    queryFn: () =>
      communicationApi.getMessages({
        conversationId: conversationId!,
        page: 1,
        limit: LIMIT,
      }),
    enabled: false,
  });

  // React Query for conversation data to get initial token counts (only fetch once)
  const { data: conversationData } = useQuery({
    queryKey: ["conversation", conversationId],
    queryFn: () => communicationApi.getConversation(conversationId!),
    enabled: !!conversationId,
    staleTime: Infinity, // Never refetch automatically
    gcTime: Infinity, // Never garbage collect
  });

  useEffect(() => {
    async function setupChat() {
      if (!conversationId) return;
      const employeeKey = GLOBAL_AGENT.id + conversationId;
      setSelectedEmployeeId(employeeKey);
      initializeChatSession(employeeKey);

      try {
        const existingSession = getSessionData(employeeKey);
        let sessId = null;

        if (!existingSession.sessionId || isSessionExpired(employeeKey)) {
          // Create new session
          sessId = await communicationApi.createSession(
            conversationId,
            true,
            undefined
          );
          setSessionData(employeeKey, conversationId, sessId || "");
          setSessionId(sessId || "");
        } else {
          // Use existing session
          sessId = existingSession.sessionId;
          setSessionData(employeeKey, conversationId, sessId || "");
          setSessionId(sessId || "");
        }
      } catch (error) {
        console.error("Error setting up chat:", error);
      }
    }
    setupChat();
  }, [conversationId]);

  useEffect(() => {
    if (!conversationId) return;
    const employeeKey = GLOBAL_AGENT.id + conversationId;
    const existingSession: any = getSessionData(employeeKey);
    const fetchMessagesData = async () => {
      try {
        const data: any = await fetchMessages();
        if (data?.data?.data && data.data.data.length > 0) {
          const transformedMessages = await filterAndTransformMessages(
            data.data.data,
            []
          );
          loadChatHistory(employeeKey, transformedMessages, {
            currentPage: data.data.metadata.currentPage,
            totalPages: data.data.metadata.totalPages,
            hasNextPage: data.data.metadata.hasNextPage,
            total: data.data.metadata.total,
            pageSize: data.data.metadata.pageSize,
            totalRawMessages: data.data.data.length,
          });
        }
      } catch (error) {}
    };
    if (
      (existingSession as any)?.messages?.length === 0 ||
      !(existingSession as any)?.messages
    ) {
      fetchMessagesData();
    } else {
      loadChatHistory(
        employeeKey,
        (existingSession as any)?.messages || [],
        session?.pagination || {
          currentPage: 1,
          totalPages: 1,
          hasNextPage: false,
          total: session?.messages.length || 0,
          pageSize: LIMIT,
          totalRawMessages: 0,
        }
      );
    }
  }, [conversationId, fetchMessages, loadChatHistory]);

  // Add load more messages function
  const handleLoadMoreMessages = useCallback(async () => {
    if (!conversationId) return;

    const employeeKey = GLOBAL_AGENT.id + conversationId;
    const session = sessions[employeeKey];
    if (!session || session.pagination.isLoadingMore) return;

    const nextPage =
      (session.pagination.currentPage + 1) * session.pagination.pageSize >
      session.pagination.totalRawMessages
        ? session.pagination.currentPage + 1
        : Math.floor(
            session.pagination.totalRawMessages / session.pagination.pageSize
          ) + 1;

    try {
      setIsLoadingMoreMessages(employeeKey, true);

      const response = await communicationApi.getMessages({
        conversationId: conversationId,
        page: nextPage,
        limit: session.pagination.pageSize, // Match the initial limit
        index: session.pagination.totalRawMessages,
      });

      if (response.data && response.data.length > 0) {
        const transformedMessages = await filterAndTransformMessages(
          response.data,
          []
        );

        loadMoreMessages(employeeKey, transformedMessages, {
          currentPage: response.metadata.currentPage,
          totalPages: response.metadata.totalPages,
          hasNextPage: response.metadata.hasNextPage,
          total: response.metadata.total,
          pageSize: response.metadata.pageSize,
          totalRawMessages:
            session.pagination.totalRawMessages + response.data.length,
        });
      }
    } catch (error) {
      console.error("Error loading more messages:", error);
      toast.error("Failed to load more messages");
    } finally {
      setIsLoadingMoreMessages(employeeKey, false);
    }
  }, [conversationId, sessions, setIsLoadingMoreMessages, loadMoreMessages]);

  // Initialize tokens from conversation API when data first becomes available
  useEffect(() => {
    if (conversationData && conversationId) {
      const employeeKey = GLOBAL_AGENT.id + conversationId;
      const session = sessions[employeeKey];
      // Only set initial tokens if they haven't been set yet
      if (
        session &&
        session.inputTokens === null &&
        session.outputTokens === null
      ) {
        setInputOutputTokens(
          employeeKey,
          conversationData.inputTokens || 0,
          conversationData.outputTokens || 0
        );
      }
    }
  }, [conversationData, conversationId, sessions, setInputOutputTokens]);

  const handleMentionHandoff = useCallback(
    ({
      message,
      attachments,
      mentionedEmployee,
    }: {
      message: string;
      attachments: any[];
      mentionedEmployee: Employee;
    }) => {
      localStorage.setItem(
        "RUH_GLOBAL_MENTION_HANDOFF",
        JSON.stringify({
          message,
          attachments,
          employeeId: mentionedEmployee.id,
        })
      );
      // Store employee in localStorage cache
      const cacheKey = LOCAL_STORAGE_KEYS.AGENT_DETAILS_CACHE;
      const cache = JSON.parse(localStorage.getItem(cacheKey) || "{}");
      cache[mentionedEmployee.id] = mentionedEmployee;
      localStorage.setItem(cacheKey, JSON.stringify(cache));
      localStorage.setItem(LOCAL_STORAGE_KEYS.IS_NEW_CHAT, "true");
      router.push(`${employeeChatRoute}/${mentionedEmployee.id}`);
    },
    []
  );

  return (
    <div
      className={cn(
        "w-full bg-background flex flex-col h-screen overflow-hidden"
      )}
    >
      <div className="flex-1 flex flex-col h-full pt-4 w-full fixed md:static">
        <div className="h-[40px] min-w-[100vw] md:hidden"></div>
        <div className="flex-1 overflow-auto flex flex-col w-full">
          {/* Token Usage Bar */}
          <div className="flex items-center justify-center px-4  pb-2.5 bg-brand-background border-b border-brand-stroke text-xs w-full">
            <div className="flex items-center overflow-x-auto scrollbar-hide">
              <div className="flex items-center gap-2 md:gap-[9px] flex-nowrap">
                <div className="hidden md:flex">
                  <TokenInfoIcon />
                </div>
                <div className="flex items-center justify-center gap-2 md:gap-3 px-2 md:px-[10px] h-[26px] rounded-[6px] bg-gray-100 flex-shrink-0">
                  <div className="text-secondary-font text-xs md:text-sm flex items-center justify-center">
                    Tokens:
                  </div>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center cursor-help">
                        <ArrowUpIcon className="w-4 h-4 md:w-5 md:h-5" />
                        <span className="text-xs md:text-sm text-brand-primary">
                          {session?.inputTokens || 0}
                        </span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Input tokens</p>
                    </TooltipContent>
                  </Tooltip>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center cursor-help">
                        <ArrowDownIcon className="w-4 h-4 md:w-5 md:h-5" />
                        <span className="text-xs md:text-sm text-brand-primary">
                          {session?.outputTokens || 0}
                        </span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Output tokens</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <div className="flex items-center justify-center gap-2 md:gap-3 px-2 md:px-[10px] h-[26px] rounded-[6px] bg-gray-100 flex-shrink-0">
                  <div className="text-secondary-font text-xs md:text-sm flex items-center justify-center">
                    Context:
                  </div>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div className="flex items-center cursor-help">
                        <span className="text-xs md:text-sm text-brand-primary">
                          {contextWindow >= 1000000
                            ? `${contextWindow / 1000000}M`
                            : `${contextWindow / 1000}k`}{" "}
                          (
                          {Math.min(
                            100,
                            parseFloat(
                              (
                                (((session?.inputTokens || 0) +
                                  (session?.outputTokens || 0)) /
                                  contextWindow) *
                                100
                              ).toFixed(2)
                            )
                          )}
                          %)
                        </span>
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        Context limit used, as this value gets higher, response
                        might be less accurate, please start a new chat for
                        better responses
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </div>
            </div>
          </div>

          {/* Messages area - full width with centered content */}
          <div className=" w-full flex-1 overflow-y-auto relative">
            <div className="h-full w-full relative md:w-[95%] lg:w-[90%] xl:w-[85%] max-w-5xl mx-auto px-2 md:px-3 lg:px-4 xl:px-5">
              <div className="w-full relative">
                <ChatMessagesList
                  isLoading={isMessagesLoading}
                  employee={GLOBAL_AGENT as Employee}
                  onLoadMore={handleLoadMoreMessages}
                  hasMoreMessages={session?.pagination.hasNextPage}
                  isLoadingMoreMessages={session?.pagination.isLoadingMore}
                />
              </div>
            </div>
          </div>
          {/* Input area - centered to match messages */}
          <div className="w-full md:w-[95%] lg:w-[90%] xl:w-[85%] max-w-5xl mx-auto px-2 md:px-3 lg:px-4 xl:px-5">
            <ChatInput
              chatStarted={chatStarted}
              employee={GLOBAL_AGENT as Employee}
              conversationId={conversationId}
              sessionId={sessionId}
              workflows={[]}
              showAIChatModeDropdown={true}
              onMentionHandoff={handleMentionHandoff}
              className="mb-2 md:mb-5"
            />
            <div className="h-[80px] md:hidden"></div>
          </div>
        </div>
      </div>
    </div>
  );
}
