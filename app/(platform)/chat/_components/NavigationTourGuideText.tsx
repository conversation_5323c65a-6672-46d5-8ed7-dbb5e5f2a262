import { useOrgStore } from "@/hooks/use-organization";
import React from "react";

export const NavigationTourGuideText: React.FC = () => {
  const { currentOrganization } = useOrgStore();

  const is_org_admin = currentOrganization?.isAdmin;

  if (is_org_admin) {
    return (
      <div className="flex flex-col gap-2">
        <p>Use the Sidebar to navigate between:</p>
        <p>
          - <strong>Home</strong> – your primary interface for getting things
          done.
        </p>
        <p>
          - <strong>Employees</strong> – to manage your AI team of employees.
        </p>
        <p>
          - <strong>Admin Panel</strong> – Customize your workspace—add members,
          set up departments, and build your knowledge base.
        </p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-2">
      <p>Use the Sidebar to navigate between:</p>
      <p>
        - <strong>Home</strong> – your primary interface for getting things
        done.
      </p>
      <p>
        - <strong>Employees</strong> – to manage your AI team of employees.
      </p>
    </div>
  );
};
