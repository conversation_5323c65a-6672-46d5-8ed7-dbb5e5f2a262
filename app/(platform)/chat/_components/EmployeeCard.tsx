"use client";

import React from "react";
import { capitalizeFirstLetter, cn } from "@/lib/utils";
import { Plus, SendHorizonalIcon } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { EmployeeAvatar } from "@/components/shared/EmployeeAvatar";
import { Employee } from "@/shared/interfaces";

interface EmployeeCardProps {
  type: "employee" | "add-new";
  employee?: Employee;
  onEmployeeSelect?: (employee: Employee) => void;
  onAddNewClick?: () => void;
  className?: string;
  isDisabledLaunchButton?: boolean;
}

export default function EmployeeCard({
  type,
  employee,
  onEmployeeSelect,
  onAddNewClick,
  className,
  isDisabledLaunchButton = true,
}: EmployeeCardProps) {
  const handleEmployeeSelect = () => {
    if (type === "employee" && employee && onEmployeeSelect) {
      onEmployeeSelect(employee);
    }
  };

  const handleAddNewClick = () => {
    if (type === "add-new" && onAddNewClick) {
      onAddNewClick();
    }
  };

  if (type === "add-new") {
    return (
      <div className={cn("flex-shrink-0 w-full relative", className)}>
        <div className="bg-gray-100 hover:bg-background-accent p-2 rounded-lg">
          <Card className="p-[14px] flex flex-col items-center relative gap-4 cursor-pointer rounded-sm border-brand-input min-h-[141px] h-full">
            {/* Department badge - empty for add-new */}
            <div className="flex justify-end w-full absolute top-2 right-2">
              <span className="text-xs font-medium text-white px-3 py-1 rounded-full opacity-0">
                NA
              </span>
            </div>

            {/* Header with icon and title */}
            <div className="flex flex-row gap-4 w-full pt-4 items-center">
              <div className="p-2 rounded-lg bg-brand-secondary/10">
                <EmployeeAvatar
                  src="/assets/logos/ruh-logo-no-text.svg"
                  name="Ruh AI"
                  className="w-17 h-17 m-0 rounded-sm bg-brand-primary/10"
                />
              </div>
              <div className="flex flex-col gap-2">
                <h1 className="!text-base text-text-primary font-satoshi-bold break-all">
                  Create New Employee
                </h1>
                <p className="text-[12px] font-satoshi-regular text-wrap text-text-secondary wrap-break-word">
                  Create a no-code, personalized AI employee for your specific
                  tasks.
                </p>
              </div>
            </div>

            {/* Action button */}
            <Button
              variant="outline"
              className="w-full bg-gradient-to-r hover:from-[var(--primary-hover)] hover:to-[var(--secondary-hover)] hover:text-white"
              onClick={handleAddNewClick}
            >
              <Plus className="w-4 h-4" />
              Create Now
            </Button>
          </Card>
        </div>
      </div>
    );
  }

  if (type === "employee" && employee) {
    return (
      <div className={cn("flex-shrink-0 w-full relative", className)}>
        <div className="bg-gray-100 hover:bg-background-accent p-2 rounded-lg">
          <Card className="p-[14px] flex flex-col items-center relative gap-[14px] cursor-pointer rounded-sm border-brand-input min-h-[141px] h-full">
            {/* Department badge */}
            <div className="flex justify-end w-full absolute top-2 right-2">
              <span className="max-w-65 text-xs font-medium text-black bg-gray-100 px-3 py-1 rounded-full line-clamp-1">
                {(employee as any).agent_topic_type || "Marketing"}
              </span>
            </div>

            {/* Header with avatar and name */}
            <div className="flex flex-row gap-4 w-full pt-4 items-center">
              <div className="p-2 rounded-lg bg-brand-secondary/10">
                <EmployeeAvatar
                  src={employee.avatar}
                  name={employee.name}
                  className="w-17 h-17 rounded-sm"
                />
              </div>
              <div className="flex flex-col gap-2">
                <h1 className="!text-base text-text-primary font-satoshi-bold break-all">
                  {capitalizeFirstLetter(employee.name)}
                </h1>
                <p className="text-[12px] font-satoshi-regular text-wrap text-text-secondary wrap-break-word">
                  {employee.description.length > 50
                    ? `${employee.description.substring(0, 50)}...`
                    : employee.description}
                </p>
              </div>
            </div>

            {/* Action button */}
            {isDisabledLaunchButton && (
              <Button
                variant="outline"
                className="w-full bg-gradient-to-r hover:from-[var(--primary-hover)] hover:to-[var(--secondary-hover)] hover:text-white"
                onClick={handleEmployeeSelect}
              >
                Launch Employee
                <SendHorizonalIcon className="w-4 h-4" />
              </Button>
            )}
          </Card>
        </div>
      </div>
    );
  }

  return null;
}