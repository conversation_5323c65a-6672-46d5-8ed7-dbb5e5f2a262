"use client";

import React, { useEffect } from "react";
import { cn, resetEmployeeCreateState } from "@/lib/utils";
import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { agentApi } from "@/app/api/agent";
import { useQuery } from "@tanstack/react-query";
import { useIsMobile } from "@/hooks/use-mobile";
import { useIsTablet } from "@/hooks/use-tablet";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import EmployeeCard from "./EmployeeCard";
import { useRouter } from "next/navigation";
import { useEmployeeManagementStore } from "@/hooks/useEmployeeManagementStore";
import { Employee } from "@/shared/interfaces";
import { createEmployeeRoute, employeeChatRoute } from "@/shared/routes";
import { LOCAL_STORAGE_KEYS } from "@/shared/constants";
interface EmployeeSelectorProps {
  className?: string;
}

export default function EmployeeSelector({ className }: EmployeeSelectorProps) {
  const {
    employee: { items: employees },
    setEmployees,
  } = useEmployeeManagementStore();
  const {
    /* openModal */
  } = useEmployeeCreateStore();
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const router = useRouter();

  const {
    data: agentsResponse,
    isLoading: isLoadingAgents,
    error: agentsError,
  } = useQuery({
    queryKey: ["agents"],
    queryFn: () => agentApi.getAgents(1, 20, false, false),
  });

  const handleEmployeeSelect = (employee: Employee) => {
    // Store employee in localStorage cache
    const cacheKey = LOCAL_STORAGE_KEYS.AGENT_DETAILS_CACHE;
    const cache = JSON.parse(localStorage.getItem(cacheKey) || "{}");
    cache[employee.id] = employee;
    localStorage.setItem(cacheKey, JSON.stringify(cache));
    localStorage.setItem(LOCAL_STORAGE_KEYS.IS_NEW_CHAT, "true");
    router.push(`${employeeChatRoute}/${employee.id}`);
  };

  const handleAddNewClick = () => {
    router.push(createEmployeeRoute);
    resetEmployeeCreateState();
  };

  // Create array of all items (add new card first, then employees)
  const allItems = [{ type: "add-new" }, ...employees];
  const showSlider = allItems.length > 2;

  useEffect(() => {
    if (agentsResponse?.data) {
      setEmployees(agentsResponse.data as any[]);
    }
  }, [agentsResponse, setEmployees]);

  return (
    <div className={cn("w-full", className)}>
      {/* Loading state */}
      {isLoadingAgents && (
        <div className="flex flex-col justify-center items-center py-12 space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-4 border-gray-200 border-t-purple-600 border-r-purple-600"></div>
        </div>
      )}

      {/* Employee Cards with Shadcn Carousel */}
      {!isLoadingAgents && !agentsError && (
        <div className="w-full">
          {/* Mobile view - freely scrollable cards */}
          {isMobile ? (
            <Carousel
              opts={{
                align: "start",
                slidesToScroll: 1,
                dragFree: true,
                containScroll: false,
              }}
              className="w-full relative max-w-sm mx-auto"
            >
              <CarouselContent className="flex">
                {allItems.map((item: any) => (
                  <CarouselItem
                    key={item.type === "add-new" ? "add-new" : item.id}
                    className="basis-full pl-4 pr-4"
                  >
                    {item.type === "add-new" ? (
                      <EmployeeCard
                        type="add-new"
                        onAddNewClick={handleAddNewClick}
                        className="w-full"
                      />
                    ) : (
                      <EmployeeCard
                        type="employee"
                        employee={item}
                        onEmployeeSelect={handleEmployeeSelect}
                        className="w-full"
                      />
                    )}
                  </CarouselItem>
                ))}
              </CarouselContent>
              {/* No navigation buttons */}
            </Carousel>
          ) : showSlider ? (
            <Carousel
              opts={{
                align: "start",
                slidesToScroll: 1,
                dragFree: false,
                containScroll: "trimSnaps",
              }}
              className={`w-full relative ${isTablet ? "max-w-[400px]" : "max-w-[800px]"} mx-auto px-8`}
            >
              <CarouselContent className="flex -ml-2 sm:-ml-4">
                {allItems.map((item: any) => (
                  <CarouselItem
                    key={item.type === "add-new" ? "add-new" : item.id}
                    className={
                      isTablet
                        ? "basis-full w-full pl-2 sm:pl-4"
                        : "basis-1/2 w-1/2 pl-2 sm:pl-4"
                    }
                  >
                    {item.type === "add-new" ? (
                      <EmployeeCard
                        type="add-new"
                        onAddNewClick={handleAddNewClick}
                        className="w-full"
                      />
                    ) : (
                      <EmployeeCard
                        type="employee"
                        employee={item}
                        onEmployeeSelect={handleEmployeeSelect}
                        className="w-full"
                      />
                    )}
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="flex absolute -left-4 top-1/2 -translate-y-1/2 bg-white border border-gray-200 shadow-md hover:shadow-lg h-8 w-8 sm:h-10 sm:w-10 z-10" />
              <CarouselNext className="flex absolute -right-4 top-1/2 -translate-y-1/2 bg-white border border-gray-200 shadow-md hover:shadow-lg h-8 w-8 sm:h-10 sm:w-10 z-10" />
            </Carousel>
          ) : (
            <div className="flex gap-12 justify-center">
              {allItems.map((item: any) => (
                <div
                  key={item.type === "add-new" ? "add-new" : item.id}
                  className="justify-center"
                >
                  {item.type === "add-new" ? (
                    <EmployeeCard
                      type="add-new"
                      onAddNewClick={handleAddNewClick}
                    />
                  ) : (
                    <EmployeeCard
                      type="employee"
                      employee={item}
                      onEmployeeSelect={handleEmployeeSelect}
                    />
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
