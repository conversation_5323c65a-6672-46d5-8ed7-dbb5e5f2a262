"use client";
import { useEffect, useState } from "react";
import { ArchiveX, LoaderIcon, Plus } from "lucide-react";
import { EmployeeBenchContent } from "../_components/EmployeeBenchContent";
import { useQuery } from "@tanstack/react-query";
import { agentApi } from "@/app/api/agent";
import { AgentBase } from "@/shared/interfaces";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { createEmployeeRoute } from "@/shared/routes";
import { resetEmployeeCreateState } from "@/lib/utils";
import { LOCAL_STORAGE_KEYS } from "@/shared/constants";
import { AnalyticsEvents, Employee } from "@/shared/enums";
import { useMixpanelTrack } from "@/hooks/useMixPanelTrack";

export default function EmployeesPage() {
  const pageSize = 10;
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("all-employees");
  const [benchEmployees, setBenchEmployees] = useState<AgentBase[]>([]);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isInitialLoading, setIsInitialLoading] = useState(true);
  const router = useRouter();
  const track = useMixpanelTrack();
  // Fetch initial bench employees data
  const { data: benchEmployeesResponse, isFetching: isLoadingBenchEmployees } =
    useQuery({
      queryKey: ["benched", page],
      queryFn: () => agentApi.getAgents(page, pageSize, true),
      staleTime: 5 * 60 * 1000,
      // enabled: page === 1, // Only fetch automatically for first page
    });

  // Handle initial data load
  useEffect(() => {
    if (benchEmployeesResponse?.data && page === 1) {
      setBenchEmployees(benchEmployeesResponse.data);
      setHasMore(benchEmployeesResponse.data.length === pageSize);
    }
  }, [benchEmployeesResponse, page]);

  const handleLoadMore = async () => {
    setIsInitialLoading(false);
    setIsLoadingMore(true);
    const nextPage = page + 1;

    try {
      const response = await agentApi.getAgents(nextPage, pageSize, true);

      if (response.data && response.data.length > 0) {
        setBenchEmployees((prev) => {
          const newEmployees = [...prev, ...response.data];
          return newEmployees;
        });
        setHasMore(response.data.length === pageSize);
      } else {
        setHasMore(false);
      }
      setPage(nextPage);
    } catch (error) {
      console.error("Error loading more bench employees:", error);
    } finally {
      setIsLoadingMore(false);
    }
  };

  const filteredBenchEmployees =
    activeTab === "employee-bench"
      ? benchEmployees.filter((employee) =>
          (employee as { name: string }).name
            .toLowerCase()
            .includes(searchQuery.toLowerCase())
        )
      : benchEmployees;

  if (isInitialLoading && isLoadingBenchEmployees) {
    return (
      <div className="flex flex-col gap-4 items-center justify-center h-screen">
        <LoaderIcon className="w-10 h-10 animate-spin text-brand-primary" />
        <h1 className="text-brand-primary-font text-xl font-semibold text-center">
          Please wait while we are fetching your agents
        </h1>
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col h-full">
      {/* Container for search input and tabs */}
      <div className="flex justify-between items-center bg-brand-card border border-brand-stroke gap-4 py-[15px] px-[43px] w-full mt-[50px] md:mt-0">
        <div className="flex gap-[15px] items-center">
          <ArchiveX
            color="var(--color-brand-primary)"
            className="w-[36px] h-[36px] bg-color-light rounded-[4px] p-1"
            strokeWidth={1}
          />
          <p>Employee Bench</p>
        </div>
        <Button
          variant="primary"
          onClick={() => {
            resetEmployeeCreateState();
            localStorage.setItem(
              LOCAL_STORAGE_KEYS.ORIGIN_FOR_CREATE_AND_EDIT_AGENT,
              Employee.CARD
            );
            router.push(createEmployeeRoute);
            track(AnalyticsEvents.CLICKED_CREATE_EMPLOYEE);
          }}
        >
          <Plus />
          Create An Employee
        </Button>
      </div>

      {/* Tab content with background color */}
      <div className="flex flex-col h-full bg-brand-background rounded-lg p-4">
        <div className="w-full h-[calc(100vh-100px)] overflow-y-auto">
          <EmployeeBenchContent
            employees={filteredBenchEmployees}
            searchQuery={searchQuery}
            setIsInitialLoading={setIsInitialLoading}
          />
          {hasMore && (
            <div className="flex justify-center mt-4">
              <Button
                variant="primary"
                onClick={handleLoadMore}
                disabled={isLoadingMore}
              >
                {isLoadingMore ? (
                  <>
                    <LoaderIcon className="w-4 h-4 animate-spin mr-2" />
                    Loading
                  </>
                ) : (
                  "Load More"
                )}
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
