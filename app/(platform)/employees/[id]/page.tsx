"use client";

import { useParams } from "next/navigation";
import { CreateEmployeeStepsLayout } from "../_components/CreateEmployeeStepsLayout";
import { useEffect } from "react";
import { useEmployeeStore } from "@/hooks/use-employee";
import { useQuery } from "@tanstack/react-query";
import { agentApi } from "@/app/api/agent";
import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { LOCAL_STORAGE_KEYS } from "@/shared/constants";

export default function CreateEmployeePage() {
  const { setCurrentEmployeeId, currentEmployeeId } = useEmployeeStore();
  const { setData, setOriginForCreateAndEdit } = useEmployeeCreateStore();

  const params = useParams();
  useEffect(() => {
    // To set the current employee id from the url parameters into the global state
    const employeeId = params.id;
    if (typeof employeeId === "string") {
      setCurrentEmployeeId(employeeId);
    } else {
      setCurrentEmployeeId(null);
      console.error("Employee ID not found or invalid in URL parameters.");
    }
  }, [params.id, setCurrentEmployeeId]);
  const { data: agentDetails, isFetching: isLoading } = useQuery({
    queryKey: ["agent", currentEmployeeId],

    queryFn: () =>
      currentEmployeeId ? agentApi.getAgentDetails(currentEmployeeId) : null,
    staleTime: 5 * 60 * 100,
    enabled: !!currentEmployeeId, // Only run the query if we have an employee ID
  });

  useEffect(() => {
    if (agentDetails) {
      let editAgentOrigin = localStorage.getItem(
        LOCAL_STORAGE_KEYS.ORIGIN_FOR_CREATE_AND_EDIT_AGENT
      );
      if (editAgentOrigin) {
        setOriginForCreateAndEdit(editAgentOrigin);
        localStorage.removeItem(
          LOCAL_STORAGE_KEYS.ORIGIN_FOR_CREATE_AND_EDIT_AGENT
        );
      }
      setData(agentDetails?.agent as any);
    }
  }, [agentDetails]);
  return (
    <CreateEmployeeStepsLayout
      isLoading={isLoading}
      agentData={agentDetails?.agent}
      isEdit={true}
    />
  );
}
