"use client";

import React, { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import EmployeeChatInterface from "../../_components/EmployeeChatInterface";
import { AgentDetailsResponse } from "@/shared/interfaces";
import { agentApi } from "@/app/api/agent";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { LOCAL_STORAGE_KEYS } from "@/shared/constants";
import { LoadingSpinner } from "@/components/shared/LoadingSpinner";

export default function EmployeeDetailPage() {
  const params = useParams();
  const employeeId = params.agentId as string;
  const [localEmployee, setLocalEmployee] = useState<any | null>(null);
  const [contextWindow, setContextWindow] = useState<number | null>(null);
  const [hasLocal, setHasLocal] = useState(false);

  // On mount, check localStorage for agent data
  useEffect(() => {
    if (employeeId) {
      const cacheKey = LOCAL_STORAGE_KEYS.AGENT_DETAILS_CACHE;
      const cache = JSON.parse(localStorage.getItem(cacheKey) || "{}");
      if (cache[employeeId]) {
        setLocalEmployee(cache[employeeId]);
        setContextWindow(cache[employeeId]?.model_data?.context_window || null);
        setHasLocal(true);
      }
    }
  }, [employeeId]);

  const { data: agentDetails, isLoading } = useQuery<AgentDetailsResponse>({
    queryKey: ["agent", employeeId],
    queryFn: () => {
      if (!employeeId) throw new Error("No employeeId");
      return agentApi.getAgentDetails(employeeId, false, false);
    },
    enabled: !!employeeId,
    staleTime: 60000,
    gcTime: 300000,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  });

  // When agentDetails changes, update localStorage and state
  useEffect(() => {
    if (agentDetails?.agent?.id) {
      const cacheKey = LOCAL_STORAGE_KEYS.AGENT_DETAILS_CACHE;
      const cache = JSON.parse(localStorage.getItem(cacheKey) || "{}");
      cache[agentDetails.agent.id] = agentDetails.agent;
      localStorage.setItem(cacheKey, JSON.stringify(cache));
      setLocalEmployee(agentDetails.agent);
      setContextWindow(agentDetails.agent?.model_data?.context_window || null);
    }
  }, [agentDetails]);

  // If we have local data, show chat immediately (even if loading)
  if (hasLocal && localEmployee) {
    return (
      <div className="h-screen w-full bg-background">
        <div className="flex-1 flex flex-col h-full">
          <EmployeeChatInterface
            employee={localEmployee}
            contextWindow={contextWindow}
          />
        </div>
      </div>
    );
  }

  // Otherwise, show skeleton while loading
  if (isLoading) {
    return (
      <div className="h-screen w-full bg-background">
        <div className="flex-1 flex flex-col h-full p-4 space-y-4">
          <LoadingSpinner message="Loading..." size="lg" />;
        </div>
      </div>
    );
  }

  // If loaded from API, show chat
  if (agentDetails?.agent) {
    return (
      <div className="h-screen w-full bg-background">
        <div className="flex-1 flex flex-col h-full">
          <EmployeeChatInterface
            employee={agentDetails.agent as any}
            contextWindow={agentDetails.agent.model_data?.context_window}
          />
        </div>
      </div>
    );
  }

  // Fallback (should not happen)
  return null;
}
