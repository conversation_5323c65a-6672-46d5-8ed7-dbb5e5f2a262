"use client";

import React, {
  useState,
  useEffect,
  useImperative<PERSON><PERSON><PERSON>,
  forwardRef,
} from "react";
import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { agentApi } from "@/app/api/agent";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import {
  agentRoute,
  dashboardRoute,
  employeeChatRoute,
  employeesRoute,
  redirectionAfterCreateEmployee,
} from "@/shared/routes";
import { ArrowLeft, InfoIcon, Pencil, Trash2, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Slider } from "@/components/ui/slider";
import { LoadingSpinner } from "@/components/shared/LoadingSpinner";
import { PublishAgentModal } from "@/components/modals/PublishAgentModal";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useEmployeeEditStore } from "@/hooks/useEmployeeEditStore";
import AddVariableModal from "@/components/modals/AddVariableModal";
import { validateProfileRequiredFields } from "./CreateEmployeeProfileFormPage";
import { mcpApi } from "@/app/api/mcp";
import { getAgentEditPayload } from "@/lib/utils";
import { LOCAL_STORAGE_KEYS } from "@/shared/constants";
import { Employee } from "@/shared/enums";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

// Define the form values type
type FormValues = {
  model_provider?: string;
  model_name?: string;
  ruh_credentials: boolean;
  agent_capabilities: {
    capabilities?: Array<{
      title: string;
      description: string;
    }>;
  };
  variables: Array<{
    name: string;
    description: string;
    type: string;
    default_value?: string;
  }>;
  temperature: number;
  max_tokens: number;
};

// Define the extended schema with additional fields
const advancedSettingsSchema = z
  .object({
    model_provider: z.string().optional(),
    model_name: z.string().optional(),
    ruh_credentials: z.boolean(),
    agent_capabilities: z
      .object({
        capabilities: z
          .array(
            z.object({
              title: z.string(),
              description: z.string(),
            })
          )
          .optional()
          .nullable()
          .default([]),
      })
      .optional()
      .nullable()
      .default({ capabilities: [] }),
    variables: z
      .array(
        z.object({
          name: z.string(),
          description: z.string(),
          type: z.string(),
          default_value: z.string().optional(),
        })
      )
      .default([]),
    temperature: z.number().min(0).max(2).default(0),
    max_tokens: z.number().min(0).max(8192).default(0),
  })
  .superRefine((data, ctx) => {
    if (!data.ruh_credentials) {
      if (!data.model_provider) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ["model_provider"],
          message: "Model provider is required",
        });
      }
      if (!data.model_name) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ["model_name"],
          message: "Model name is required",
        });
      }
    }
  }) as z.ZodType<FormValues>;

interface CreateEmployeeAdvancedSettingsFormPageProps {
  agentData?: any;
  isEdit?: boolean;
  isLoading?: boolean;
  onSubmitAgent: (formData: any, provider?: string, model?: string) => void;
  isCreatingAgent?: boolean;
  isUpdatingAgent?: boolean;
}

const CreateEmployeeAdvancedSettingsFormPage = forwardRef<
  { saveToStore: () => void },
  CreateEmployeeAdvancedSettingsFormPageProps
>(({ onSubmitAgent, isCreatingAgent, isUpdatingAgent, ...props }, ref) => {
  const {
    setData,
    formStep,
    setFormStep,
    setCheckRequiredField,
    data,
    isPublishModalOpen,
    pendingSaveStep,
    setPendingSaveStep,
    openPublishModal,
    closePublishModal,
    originForCreateAndEdit,
  } = useEmployeeCreateStore();
  const queryClient = useQueryClient();
  const router = useRouter();
  const { closeModal } = useEmployeeEditStore();
  // State for form fields
  const [ruhCredentials, setRuhCredentials] = useState(
    data.ruh_credentials || false
  );
  const [selectedProvider, setSelectedProvider] = useState<string>(
    data.model_provider || ""
  );
  const [selectedModel, setSelectedModel] = useState(data.model_name || "");
  const [showAddCapability, setShowAddCapability] = useState(false);
  const [capabilityTitle, setCapabilityTitle] = useState("");
  const [capabilityDescription, setCapabilityDescription] = useState("");
  const [showAddVariable, setShowAddVariable] = useState(false);
  const [variableName, setVariableName] = useState("");
  const [variableDescription, setVariableDescription] = useState("");
  const [variableType, setVariableType] = useState("text");
  const [variableDefault, setVariableDefault] = useState("");
  const [editingVariableIndex, setEditingVariableIndex] = useState<
    number | null
  >(null);
  const [showEditSaveChangesModal, setShowEditSaveChangesModal] =
    useState<boolean>(false);

  // Provider/model API state
  const [providers, setProviders] = useState<any[]>([]);
  const [models, setModels] = useState<any[]>([]);
  const [isLoadingProviders, setIsLoadingProviders] = useState(false);
  const [isLoadingModels, setIsLoadingModels] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(advancedSettingsSchema as any),
    defaultValues: {
      model_provider: data.model_provider || "",
      model_name: data.model_name || "",
      ruh_credentials: false,
      agent_capabilities: {
        capabilities: data?.agent_capabilities?.capabilities || [],
      },
      temperature: data?.temperature || 0.7,
      max_tokens: data?.max_tokens || 1024,
      variables: data.variables || [],
    },
  });

  useImperativeHandle(ref, () => ({
    saveToStore: () => {
      const values = form.getValues();
      const mapped = {
        ...values,
        ruh_credentials: ruhCredentials,
        model_provider: selectedProvider,
        model_name: selectedModel,
        variables: (values.variables || []).map((v) => ({
          ...v,
          default_value: v.default_value ?? "",
        })),
        // Ensure capabilities_data is properly structured and never null
        agent_capabilities: {
          capabilities: (values.agent_capabilities?.capabilities || []).map(
            (cap) => ({
              title: cap.title,
              description: cap.description,
            })
          ),
        },
      };
      setData(mapped);
    },
  }));

  // Initialize data when in edit mode or from store data
  useEffect(() => {
    if (props.isEdit && data) {
      if (data.ruh_credentials !== undefined) {
        setRuhCredentials(data.ruh_credentials);
        form.setValue("ruh_credentials", data.ruh_credentials);
      }
      if (data.model_provider) {
        setSelectedProvider(data.model_provider);
        form.setValue("model_provider", data.model_provider);
      }
      if (data.model_name) {
        setSelectedModel(data.model_name);
        form.setValue("model_name", data.model_name);
      }
      if (data.temperature !== undefined) {
        form.setValue("temperature", data.temperature);
      }
      if (data.max_tokens !== undefined) {
        form.setValue("max_tokens", data.max_tokens);
      }
    } else {
      // Initialize from store data when not in edit mode
      if (data.ruh_credentials !== undefined) {
        setRuhCredentials(data.ruh_credentials);
        form.setValue("ruh_credentials", data.ruh_credentials);
      }
      if (data.model_provider) {
        setSelectedProvider(data.model_provider);
        form.setValue("model_provider", data.model_provider);
      }
      if (data.model_name) {
        setSelectedModel(data.model_name);
        form.setValue("model_name", data.model_name);
      }
      if ((data as any).temperature !== undefined) {
        form.setValue("temperature", (data as any).temperature);
      }
      if ((data as any).max_tokens !== undefined) {
        form.setValue("max_tokens", (data as any).max_tokens);
      }
    }
  }, [props.isEdit, data, form]);

  useEffect(() => {
    if (pendingSaveStep === 5) {
      const values = form.getValues();
      const mapped: any = {
        ...values,
        // ruh_credentials: ruhCredentials,
        // model_provider: selectedProvider,
        // model_name: selectedModel,
        variables: (values.variables || []).map((v) => ({
          ...v,
          default_value: v.default_value ?? "",
        })),
        // Ensure capabilities_data is properly structured and never null
        agent_capabilities: {
          capabilities: (values.agent_capabilities?.capabilities || []).map(
            (cap) => ({
              title: cap.title,
              description: cap.description,
            })
          ),
        },
      };
      if (props?.isEdit) {
        let diff = getAgentEditPayload(data || {}, mapped);
        if (Object.keys(diff)?.length) {
          setShowEditSaveChangesModal(true);
        } else {
          setPendingSaveStep(null);
        }
      } else {
        setData(mapped);
        setPendingSaveStep(null);
      }
    }
  }, [
    pendingSaveStep,
    setData,
    setPendingSaveStep,
    form,
    ruhCredentials,
    selectedProvider,
    selectedModel,
  ]);

  useEffect(() => {
    if (!isUpdatingAgent) {
      setPendingSaveStep(null);
    }
  }, [isUpdatingAgent]);

  const { data: providersData } = useQuery({
    queryKey: ["providers"],
    queryFn: () => mcpApi.getProviders(1, 60),
    staleTime: 30 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
  });

  // Fetch providers and models on mount
  useEffect(() => {
    const fetchProvidersAndModels = async () => {
      setIsLoadingProviders(true);
      try {
        const providerList = providersData?.providers || [];
        setProviders(providerList);
        if (providerList?.length && form.getValues("model_provider")) {
          //check provider list have model or not
          let providerExist = providerList?.filter(
            (item) => item?.provider == form.getValues("model_provider")
          );
          if (!providerExist?.length) return;
        }
        if (providerList.length > 0) {
          let providerExist = providerList?.filter(
            (item) => item?.provider == form.getValues("model_provider")
          );
          const firstProvider = providerList[0];
          setSelectedProvider(
            providerExist[0]?.provider || firstProvider.provider
          );
          form.setValue(
            "model_provider",
            providerExist[0]?.provider || firstProvider.provider
          );
          setIsLoadingModels(true);
          try {
            const modelRes = await mcpApi.getModels(
              providerExist[0]?.id || firstProvider.id,
              1,
              60
            );
            const modelList = modelRes?.models || [];
            setModels(modelList);
            if (modelList?.length && data?.model_name) {
              //check provider list have model or not
              let modelExist = modelList?.filter(
                (item) => item?.model == data?.model_name
              );
              if (!modelExist?.length) {
                return;
              }
            }
            if (modelList.length > 0) {
              let modelExist = modelList?.filter(
                (item) => item?.model == form.getValues("model_name")
              );
              setSelectedModel(modelExist[0]?.model || modelList[0].model);
              form.setValue(
                "model_name",
                modelExist[0]?.model || modelList[0].model
              );
            }
          } finally {
            setIsLoadingModels(false);
          }
        }
      } finally {
        setIsLoadingProviders(false);
      }
    };
    fetchProvidersAndModels();
  }, [providersData]);

  // Fetch models when provider changes (user selection)
  useEffect(() => {
    if (!selectedProvider) return;
    setIsLoadingModels(true);
    const fetchModels = async () => {
      try {
        let providerExist = providers?.filter(
          (item) => item?.provider == selectedProvider
        );

        const modelRes = await mcpApi.getModels(providerExist[0].id, 1, 60);
        const modelList = modelRes?.models || [];
        setModels(modelList);
        if (modelList.length > 0) {
          setSelectedModel(modelList[0].model);
          form.setValue("model_name", modelList[0].model);
          // setData({
          //   ...form.getValues(),
          //   model_provider: selectedProvider as ModelProvider,
          //   model_name: modelList[0].id,
          //   capabilities_data: { capabilities: form.getValues().capabilities_data?.capabilities || [] },
          //   variables: (form.getValues().variables || []).map(v => ({ ...v, default_value: v.default_value ?? "" })),
          // });
        } else {
          setSelectedModel("");
          form.setValue("model_name", "");
        }
      } finally {
        setIsLoadingModels(false);
      }
    };
    // Only fetch if not initial mount (skip if already handled by first effect)
    if (providers.length > 0) {
      fetchModels();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedProvider]);

  const handleModalPublish = (provider: string, model: string) => {
    closePublishModal();
    onSubmit(form.getValues(), provider, model);
  };
  const handleModalDiscard = () => {
    closePublishModal();
  };
  const onSubmit = (
    formData: FormValues,
    provider?: string,
    model?: string
  ) => {
    onSubmitAgent({ ...data, ...formData }, provider, model);
  };
  const handlePublishClick = async () => {
    const profileErrors = validateProfileRequiredFields(data);
    if (profileErrors.length > 0) {
      // Redirect to profile step and show validation errors
      setFormStep(1);
      setCheckRequiredField(true);
      profileErrors.forEach((error) => {
        toast.error(error.message, {
          duration: 4000,
          position: "top-center",
        });
      });
      return;
    } else {
      const isValid = await form.trigger();
      if (!isValid) {
        const errors = form.formState.errors;
        Object.values(errors).forEach((error) => {
          if (error?.message) {
            toast.error(error.message, {
              duration: 4000,
              position: "top-center",
            });
          }
        });
        return;
      }
      if (props.isEdit) {
        onSubmit(form.getValues());
      } else {
        setData(form.getValues() as any);
        openPublishModal();
      }
    }
  };

  // event change functions

  const handleEditVariable = (variable: any, idx: number) => {
    setEditingVariableIndex(idx);
    setVariableName(variable.name);
    setVariableDescription(variable.description);
    setVariableType(variable.type);
    setVariableDefault(variable.default_value || "");
    setShowAddVariable(true);
  };

  const handleCancelVariable = () => {
    setShowAddVariable(false);
    setEditingVariableIndex(null);
    setVariableName("");
    setVariableDescription("");
    setVariableType("text");
    setVariableDefault("");
  };

  const handleVariablesUpdate = (
    newVariable: FormValues["variables"][0],
    index?: number
  ) => {
    const currentVariables = form.getValues("variables") || [];
    if (typeof index === "number") {
      const updated = [...currentVariables];
      updated[index] = newVariable;
      form.setValue("variables", updated, { shouldDirty: true });
    } else {
      form.setValue("variables", [...currentVariables, newVariable], {
        shouldDirty: true,
      });
    }
    setVariableName("");
    setVariableDescription("");
    setVariableType("text");
    setVariableDefault("");
    setShowAddVariable(false);
    setEditingVariableIndex(null);
  };

  const handleVariableDelete = (index: number) => {
    const currentVariables = form.getValues("variables") || [];
    form.setValue(
      "variables",
      currentVariables.filter((_, i) => i !== index),
      { shouldDirty: true }
    );
  };

  const handleAddCapability = () => {
    if (capabilityTitle && capabilityDescription) {
      const currentCapabilities =
        form.getValues("agent_capabilities.capabilities") || [];
      form.setValue(
        "agent_capabilities.capabilities",
        [
          ...currentCapabilities,
          {
            title: capabilityTitle,
            description: capabilityDescription,
          },
        ],
        { shouldDirty: true }
      );
      setCapabilityTitle("");
      setCapabilityDescription("");
      setShowAddCapability(false);
    }
  };

  const handleRemoveCapability = (idx: number) => {
    const currentCapabilities =
      form.getValues("agent_capabilities.capabilities") || [];
    const updated = [...currentCapabilities];
    updated.splice(idx, 1);
    form.setValue("agent_capabilities.capabilities", updated, {
      shouldDirty: true,
    });
  };

  if (props.isLoading) {
    return <LoadingSpinner message="Loading advanced settings..." />;
  }

  return (
    <div className="flex flex-col h-full p-[18px] md:p-[25px] gap-[17px]">
      <div className="flex justify-end md:flex-row md:justify-end md:items-center mt-[50px] md:mt-0">
        {/* <Button
          variant="ghost"
          className="text-brand-primary hover:text-brand-primary/90 hover:bg-purple-50 p-0"
          onClick={() => setFormStep(formStep - 1)}
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back
        </Button> */}
        <div className="flex gap-2">
          <Button
            variant="outline"
            className="px-6 w-auto"
            onClick={() => {
              if (originForCreateAndEdit == Employee.CARD) {
                router.push(employeesRoute);
              } else {
                if (props?.isEdit) {
                  // Store employee in localStorage cache
                  const cacheKey = LOCAL_STORAGE_KEYS.AGENT_DETAILS_CACHE;
                  const cache = JSON.parse(
                    localStorage.getItem(cacheKey) || "{}"
                  );
                  cache[props?.agentData?.id] = props?.agentData;
                  localStorage.setItem(cacheKey, JSON.stringify(cache));
                  localStorage.setItem(LOCAL_STORAGE_KEYS.IS_NEW_CHAT, "true");
                  router.push(`${employeeChatRoute}/${props?.agentData?.id}`);
                } else {
                  router.push(dashboardRoute);
                }
              }
            }}
          >
            Close
          </Button>
          <Button
            variant="primary"
            className="px-6 w-auto"
            disabled={isCreatingAgent || isUpdatingAgent}
            onClick={handlePublishClick}
          >
            {isCreatingAgent || isUpdatingAgent ? (
              <>
                <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {props.isEdit ? "Saving..." : "Publishing..."}
              </>
            ) : props.isEdit ? (
              "Save Changes"
            ) : (
              "Publish Changes"
            )}
          </Button>
        </div>
      </div>
      {isPublishModalOpen && (
        <PublishAgentModal
          open={isPublishModalOpen}
          onOpenChange={closePublishModal}
          onPublish={handleModalPublish}
          onDiscard={handleModalDiscard}
        />
      )}
      <div className="flex-1 overflow-y-auto">
        <div className="max-w-4xl min-w-full">
          <div className="bg-white rounded-lg border border-gray-200 px-[32px] py-[24px] shadow-sm">
            <div className="flex-shrink-1 md:flex-shrink-0 mb-6">
              <h1> Advanced Settings</h1>
              <p className="text-text-secondary text-sm">
                For those who want to customize each aspect of their employees.
              </p>
            </div>
            <Form {...form}>
              <Accordion type="multiple" className="w-full space-y-4">
                <AccordionItem
                  value="language-provider"
                  className="bg-background border border-gray-200 rounded-lg"
                >
                  <AccordionTrigger className="px-4 font-satoshi-bold no-underline hover:no-underline">
                    Language Provider and Model
                  </AccordionTrigger>
                  <AccordionContent className="bg-white p-4 rounded-bl-lg rounded-br-lg">
                    <div className="mb-4 text-gray-600 text-sm">
                      Select and configure the large-language model (LLM) your
                      agent will use to generate responses and solutions. Choose
                      from pre-integrated providers or add your own for more
                      flexibility.
                    </div>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-6">
                      <FormField
                        control={form.control}
                        name="model_provider"
                        render={({ field }) => (
                          <FormItem className="flex flex-col gap-2">
                            <FormLabel className="text-sm font-satoshi-bold data-[error=true]:text-gray-900">
                              Select LLM Provider
                            </FormLabel>
                            <FormControl>
                              <Select
                                value={selectedProvider}
                                onValueChange={async (value) => {
                                  setSelectedProvider(value);
                                  field.onChange(value);
                                  form.setValue("model_name", "");
                                }}
                                disabled={isLoadingProviders}
                              >
                                <SelectTrigger className="w-full text-sm aria-invalid:border-gray-200 aria-invalid:ring-0">
                                  {isLoadingProviders ? (
                                    <span className="flex items-center">
                                      Loading...
                                    </span>
                                  ) : (
                                    <SelectValue placeholder="Select a provider" />
                                  )}
                                </SelectTrigger>
                                <SelectContent>
                                  {providers.map((provider) => (
                                    <SelectItem
                                      key={provider.id}
                                      value={provider.provider}
                                    >
                                      {provider.provider}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <div className="min-h-[20px]">
                              <FormMessage />
                            </div>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="model_name"
                        render={({ field }) => (
                          <FormItem className="flex flex-col gap-2">
                            <FormLabel className="text-sm font-satoshi-bold data-[error=true]:text-gray-900">
                              Select Language Model
                            </FormLabel>
                            <FormControl>
                              <Select
                                value={selectedModel}
                                onValueChange={(value) => {
                                  setSelectedModel(value);
                                  field.onChange(value);
                                  // setData({
                                  //   ...form.getValues(),
                                  //   model_provider:
                                  //     selectedProvider as ModelProvider,
                                  //   model_name: value,
                                  //   capabilities_data: {
                                  //     capabilities:
                                  //       form.getValues().capabilities_data
                                  //         ?.capabilities || [],
                                  //   },
                                  //   variables: (
                                  //     form.getValues().variables || []
                                  //   ).map((v) => ({
                                  //     ...v,
                                  //     default_value: v.default_value ?? "",
                                  //   })),
                                  // });
                                }}
                                disabled={isLoadingModels}
                              >
                                <SelectTrigger className="w-full text-sm aria-invalid:border-gray-200 aria-invalid:ring-0">
                                  {isLoadingModels ? (
                                    <span className="flex items-center">
                                      Loading...
                                    </span>
                                  ) : (
                                    <SelectValue placeholder="Select a model" />
                                  )}
                                </SelectTrigger>
                                <SelectContent>
                                  {models?.length ? (
                                    models.map((model) => (
                                      <SelectItem
                                        key={model.id}
                                        value={model.model}
                                      >
                                        {model.model}
                                      </SelectItem>
                                    ))
                                  ) : (
                                    <p className="text-sm text-text-secondary">
                                      No models found
                                    </p>
                                  )}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <div className="min-h-[20px]">
                              <FormMessage />
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex flex-col gap-6 mt-4">
                      <FormField
                        control={form.control}
                        name="temperature"
                        render={({ field }) => (
                          <FormItem className="flex flex-col gap-2">
                            <div className="flex flex-row justify-between gap-10">
                              <div>
                                <FormLabel className="text-sm font-satoshi-bold">
                                  Temperature
                                </FormLabel>
                                <p className="tex-sm text-text-secondary">
                                  How creative or deterministic the agent’s
                                  responses will be. Lower values (0.1-1.0) make
                                  replies more focused and reliable, while
                                  higher values (1.1-2.0) lead to more varied or
                                  imaginative outputs.
                                </p>
                              </div>
                              <FormControl>
                                <Input
                                  type="number"
                                  name="temperature"
                                  value={form.watch("temperature") ?? 0}
                                  onChange={(e) => {
                                    let value = parseFloat(e.target.value);
                                    if (isNaN(value)) value = 0;
                                    value = Math.max(0, Math.min(2, value));
                                    form.setValue("temperature", value, {
                                      shouldDirty: true,
                                      shouldValidate: true,
                                    });
                                  }}
                                  min={0}
                                  max={2}
                                  step={0.1}
                                  className="text-center max-w-[80px] text-sm h-8 aria-invalid:border-gray-200 aria-invalid:ring-0"
                                />
                              </FormControl>
                            </div>

                            <Slider
                              value={[field.value ?? 0]}
                              onValueChange={(value) => {
                                form.setValue("temperature", value[0], {
                                  shouldDirty: true,
                                });
                              }}
                              max={2}
                              min={0}
                              step={0.1}
                              className="w-full mt-2"
                            />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="max_tokens"
                        render={({ field }) => (
                          <FormItem className="flex flex-row gap-2">
                            <div className="flex flex-col">
                              <FormLabel className="text-sm font-satoshi-bold">
                                Maximum Output Tokens
                              </FormLabel>
                              <p className="tex-sm text-text-secondary">
                                The highest number of tokens that an agent's
                                model can produce. It's crucial to set this
                                value wisely: if it's too low, the agent may
                                struggle to function, and if it's set above the
                                model's capacity, it could lead to errors.
                              </p>
                            </div>
                            <FormControl>
                              <Input
                                type="number"
                                name="max_tokens"
                                value={field.value || ""}
                                onChange={(e) =>
                                  field.onChange(parseInt(e.target.value))
                                }
                                min="0"
                                max="4096"
                                step="100"
                                className="text-center max-w-[80px] text-sm h-8 aria-invalid:border-gray-200 aria-invalid:ring-0"
                                disabled={form.getValues("ruh_credentials")}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem
                  value="agent-capabilities"
                  className="bg-background border border-gray-200 rounded-lg"
                >
                  <AccordionTrigger className="px-4 font-satoshi-bold  no-underline hover:no-underline">
                    Agent Capabilities
                  </AccordionTrigger>
                  <AccordionContent className="bg-white p-5 min-h-[231px] rounded-bl-lg rounded-br-lg">
                    <div className="mb-6">
                      <p className="text-text-secondary text-sm">
                        Add specific skills to your agent, like search,
                        summarization, or content creation. You can also provide
                        detailed descriptions for each capability to guide your
                        agent's behavior
                      </p>
                    </div>
                    <FormField
                      control={form.control}
                      name="agent_capabilities.capabilities"
                      render={({ field }) => (
                        <FormItem>
                          {showAddCapability && (
                            <div className="space-y-4 flex flex-col gap-6 mb-6">
                              <div className="flex flex-col gap-1 m-0">
                                <label className="text-sm font-satoshi-bold">
                                  Title
                                </label>
                                <Input
                                  className="text-sm"
                                  placeholder="Capability title"
                                  value={capabilityTitle}
                                  onChange={(e) =>
                                    setCapabilityTitle(e.target.value)
                                  }
                                  maxLength={1000}
                                />
                                <span className="text-sm text-text-secondary">
                                  1000 max characters
                                </span>
                              </div>
                              <div className="flex flex-col gap-1 m-0">
                                <label className="text-sm font-satoshi-bold">
                                  Description
                                </label>
                                <Textarea
                                  placeholder="Capability description"
                                  value={capabilityDescription}
                                  onChange={(e) =>
                                    setCapabilityDescription(e.target.value)
                                  }
                                  maxLength={1000}
                                />
                                <span className="text-sm text-text-secondary">
                                  1000 max characters
                                </span>
                              </div>
                              <div className="flex gap-1 m-0 justify-end">
                                <div className="flex flex-col lg:flex-row gap-2 w-full">
                                  <Button
                                    type="button"
                                    className="lg:w-[270px] w-full"
                                    variant="tertiary"
                                    onClick={() => {
                                      setShowAddCapability(false);
                                      setCapabilityTitle("");
                                      setCapabilityDescription("");
                                    }}
                                  >
                                    Cancel
                                  </Button>
                                  <Button
                                    type="button"
                                    variant="primary"
                                    className="flex items-center gap-2 px-8 lg:w-[270px] w-full"
                                    onClick={handleAddCapability}
                                    disabled={
                                      !capabilityTitle.trim() ||
                                      !capabilityDescription.trim()
                                    }
                                  >
                                    <span className="text-lg font-satoshi-bold">
                                      +
                                    </span>
                                    {"  "}
                                    Add Your Capabilities
                                  </Button>
                                </div>
                              </div>
                            </div>
                          )}
                          <div className="flex flex-col gap-6">
                            {(field.value || []).length > 0 ? (
                              <div className="space-y-3">
                                {(field.value || []).map(
                                  (cap: any, idx: number) => (
                                    <div
                                      key={idx}
                                      className="relative p-4 border border-gray-200 rounded-lg bg-gray-50"
                                    >
                                      <h4 className="text-sm font-satoshi-bold">
                                        {cap.title}
                                      </h4>
                                      <p className="text-sm text-text-secondary mt-1">
                                        {cap.description}
                                      </p>
                                      <button
                                        type="button"
                                        aria-label="Remove capability"
                                        onClick={() =>
                                          handleRemoveCapability(idx)
                                        }
                                        className="ml-4 p-2 rounded-full hover:bg-gray-100 transition absolute right-4 top-1/2 -translate-y-1/2"
                                      >
                                        <X className="w-4 h-4 text-gray-400" />
                                      </button>
                                    </div>
                                  )
                                )}
                              </div>
                            ) : (
                              <>
                                {!showAddCapability && (
                                  <div className="text-center text-gray-500">
                                    No capabilities configured yet
                                  </div>
                                )}
                              </>
                            )}

                            {!showAddCapability && (
                              <div className="flex justify-center">
                                <Button
                                  type="button"
                                  variant="primary"
                                  onClick={() => setShowAddCapability(true)}
                                  className="mb:w-[270px] w-[200px] self-center mt-6"
                                >
                                  + Add Your Capabilities
                                </Button>
                              </div>
                            )}
                          </div>
                        </FormItem>
                      )}
                    />
                  </AccordionContent>
                </AccordionItem>
                <AccordionItem
                  value="agent-variables"
                  className="bg-background border border-gray-200 rounded-lg !border-b-1"
                >
                  <AccordionTrigger className="px-4 font-satoshi-bold  no-underline hover:no-underline">
                    Agent Variables
                  </AccordionTrigger>
                  <AccordionContent className="bg-white p-4 rounded-bl-lg rounded-br-lg">
                    <FormField
                      control={form.control}
                      name="variables"
                      render={({ field }) => (
                        <FormItem className="flex flex-col gap-6">
                          {field.value.length === 0 && !showAddVariable && (
                            <div className="flex flex-col items-center py-8">
                              <span className="text-gray-500 mb-4">
                                No variables configured yet
                              </span>
                              <Button
                                className="md:w-[270px] w-[200px]"
                                variant="primary"
                                onClick={() => setShowAddVariable(true)}
                              >
                                + Add Your Variable
                              </Button>
                            </div>
                          )}
                          {showAddVariable && (
                            <AddVariableModal
                              open={showAddVariable}
                              onClose={() => {
                                handleCancelVariable();
                              }}
                              onSave={(newVariable, index) => {
                                handleVariablesUpdate(
                                  newVariable,
                                  index ?? undefined
                                );
                              }}
                              editingIndex={editingVariableIndex}
                              variableName={variableName}
                              setVariableName={setVariableName}
                              variableDescription={variableDescription}
                              setVariableDescription={setVariableDescription}
                              variableType={variableType}
                              setVariableType={setVariableType}
                              variableDefault={variableDefault}
                              setVariableDefault={setVariableDefault}
                            />
                          )}
                          {field.value.length > 0 && !showAddVariable && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                              {field.value.map((variable, idx) => (
                                <div
                                  key={idx}
                                  className="bg-gray-50 rounded-lg p-4 border border-gray-200 flex flex-col gap-2 relative"
                                >
                                  <div className="flex justify-between items-start">
                                    <div className="font-satoshi-bold">
                                      {variable.name}
                                    </div>
                                    <div className="flex gap-2">
                                      <button
                                        type="button"
                                        className="p-1 hover:bg-gray-200 rounded"
                                        aria-label="Edit variable"
                                        onClick={() => {
                                          handleEditVariable(variable, idx);
                                        }}
                                      >
                                        <Pencil size={16} />
                                      </button>
                                      <button
                                        type="button"
                                        className="p-1 hover:bg-gray-200 rounded"
                                        aria-label="Delete variable"
                                        onClick={() =>
                                          handleVariableDelete(idx)
                                        }
                                      >
                                        <Trash2 size={16} />
                                      </button>
                                    </div>
                                  </div>
                                  <div className="text-gray-600">
                                    {variable.description}
                                  </div>
                                  <div className="flex gap-2 items-center">
                                    <span className="bg-purple-100 text-purple-700 rounded px-2 py-1 text-xs font-satoshi-bold">
                                      {variable.type}
                                    </span>
                                    {variable.default_value && (
                                      <span className="text-xs text-gray-500">
                                        Default: {variable.default_value}
                                      </span>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                          {field.value.length > 0 && !showAddVariable && (
                            <div className="flex justify-center">
                              <Button
                                variant="primary"
                                className="self-center md:w-[270px] w-[200px]"
                                onClick={() => setShowAddVariable(true)}
                              >
                                + Add Your Variable
                              </Button>
                            </div>
                          )}
                        </FormItem>
                      )}
                    />
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </Form>
          </div>
        </div>
        <Dialog open={showEditSaveChangesModal}>
          <DialogContent className="max-w-4xl max-h-[90vh] bg-[var(--card-color)] border border-[var(--border-default)] [&>button]:hidden">
            <DialogHeader>
              <DialogTitle className="text-[var(--text-primary)] font-satoshi-bold">
                You have unsaved changes
              </DialogTitle>
            </DialogHeader>

            <div className="py-4 space-y-4">
              <div className="flex items-start space-x-4">
                <div className="p-2 rounded-full bg-amber-100">
                  <InfoIcon className="h-6 w-6 text-amber-600" />
                </div>
                <div className="space-y-2">
                  <p className="text-[var(--text-primary)]">
                    You've made changes to the advanced settings that haven't
                    been saved yet.
                  </p>
                  <p className="text-[var(--text-secondary)] text-sm">
                    If you save these changes, the employee's advanced settings
                    will be updated with your modifications. If you cancel, your
                    changes will be discarded and the settings will remain
                    unchanged.
                  </p>
                </div>
              </div>
            </div>

            <DialogFooter className="gap-2">
              <Button
                type="button"
                variant="secondary"
                onClick={() => {
                  setPendingSaveStep(null);
                }}
              >
                Cancel
              </Button>
              <Button
                type="button"
                variant="primary"
                onClick={() => {
                  setShowEditSaveChangesModal(false);
                  handlePublishClick();
                }}
              >
                Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
});

export { CreateEmployeeAdvancedSettingsFormPage };
