import { communicationApi } from "@/app/api/communication";
import CustomTable from "@/components/shared/CustomTable";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { EllipsisVertical, Search, X } from "lucide-react";
import moment from "moment";
import Image from "next/image";
import React, { useEffect, useState } from "react";
import { Employee } from "@/shared/interfaces";

interface ConversationHistoryProps {
  employee: Employee;
  onConversationSelect?: (conversationId: string) => void;
}

const ConversationHistory: React.FC<ConversationHistoryProps> = ({
  employee,
  onConversationSelect,
}) => {
  const pageSize = 5;
  const [searchText, setSearchText] = useState("");
  const [debouncedSearchText, setDebouncedSearchText] = useState("");
  const [page, setPage] = useState(0);
  const queryClient = useQueryClient();

  // Fetch conversations
  const { data: conversationsDataRaw, isLoading } = useQuery({
    queryKey: [
      "conversations",
      employee.id,
      debouncedSearchText,
      page,
      pageSize,
    ],
    queryFn: () =>
      communicationApi.getAgentConversations({
        agentId: employee.id,
        search: debouncedSearchText,
        page: page + 1,
        limit: pageSize,
      }),
    staleTime: 0,
  });
  const conversationsData =
    conversationsDataRaw &&
    typeof conversationsDataRaw === "object" &&
    "data" in conversationsDataRaw &&
    "metadata" in conversationsDataRaw
      ? (conversationsDataRaw as { data: any[]; metadata: { total: number } })
      : { data: [], metadata: { total: 0 } };

  // Delete conversation mutation
  const deleteConversationMutation = useMutation({
    mutationFn: (id: string) => communicationApi.deleteConversation(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["conversations"] });
    },
  });

  // Debounce search
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearchText(searchText);
    }, 800);
    return () => {
      clearTimeout(handler);
    };
  }, [searchText]);

  const handleDelete = (id: string) => {
    deleteConversationMutation.mutate(id);
  };

  const tableColumns = [
    {
      key: "title",
      label: "Title",
      render: (row: any) => row.title,
    },
    {
      key: "createdAt",
      label: "Date Created",
      render: (row: any) => moment(row.createdAt).format("MMM DD YYYY hh:mm A"),
    },
    {
      key: "credits",
      label: "Credit Used",
      render: () => (
        <div className="flex items-center gap-1">
          <Image
            src={`/assets/icons/coin.svg`}
            alt="coin"
            width={20}
            height={20}
            className="w-4 h-4"
          />
          <span className="text-sm">0</span>
        </div>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      render: (row: any) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button type="button" className="p-1">
              <EllipsisVertical className="w-4 h-4 cursor-pointer" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={(e) => {
                handleDelete(row.id);
                e.stopPropagation();
              }}
            >
              <X className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="flex-shrink-0 flex flex-col gap-6">
        <div className="flex flex-col mb-4">
          <h1 className="font-satoshi-bold">{employee.name}'s Conversations</h1>
          <p className="text-sm text-text-secondary font-satoshi-regular">
            All conversations will be displayed here for monitoring and
            management purposes.
          </p>
        </div>
        {/* Search */}
        <div className="relative w-full">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
          <Input
            onChange={(event) => setSearchText(event.target.value)}
            value={searchText}
            placeholder="Search conversations"
            className="pl-12 h-12 text-base bg-white border-purple-200 rounded-xl focus:border-purple-400"
          />
        </div>
      </div>
      <div className="mt-2">
        <CustomTable
          onPageChange={setPage}
          totalPages={Math.ceil(
            (conversationsData.metadata.total || 0) / pageSize
          )}
          isLoading={isLoading || deleteConversationMutation.isPending}
          columns={tableColumns}
          data={conversationsData.data.filter((result) => !!result.title)}
          pageSize={pageSize}
          totalRecords={conversationsData.metadata.total || 0}
          isPagination={true}
          rowClickable={true}
          onRowClick={(row) =>
            onConversationSelect && onConversationSelect(row.id)
          }
        />
      </div>
    </div>
  );
};

export default ConversationHistory;
