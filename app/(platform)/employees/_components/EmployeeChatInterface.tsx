"use client";

import { communication<PERSON><PERSON> } from "@/app/api/communication";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { useEmployeeManagementStore } from "@/hooks/useEmployeeManagementStore";
import { useSSEStream } from "@/hooks/useSSEStream";
import { Employee, MCPInDB } from "@/shared/interfaces";
import {
  ArrowDownIcon,
  ArrowUpIcon,
  Bot,
  Edit,
  History,
  LoaderIcon,
  Plus,
} from "lucide-react";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import ChatInput from "../../_components/ChatInput";
import ChatMessagesList from "../../_components/ChatMessagesList";

import { workflowApi } from "@/app/api/workflow";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useIsMobile } from "@/hooks/use-mobile";
import { useOutOfCreditsModalStore } from "@/hooks/use-out-of-credits-modal";
import { useIsTablet } from "@/hooks/use-tablet";
import { useWorkflowStore } from "@/hooks/use-workflow";
import { useWorkflowSSEStream } from "@/hooks/useWorkflowSSEStream";
import { resetEmployeeCreateState } from "@/lib/utils";
import { filterAndTransformMessages } from "@/lib/utils/messageTransform";
import TokenInfoIcon from "@/public/assets/Icons-components/TokenInfoIcon";
import {
  LIMIT,
  LOCAL_STORAGE_KEYS,
  TONE_LOADING_MESSAGES,
} from "@/shared/constants";
import { SenderType } from "@/shared/enums";
import { useQuery } from "@tanstack/react-query";
import { motion } from "framer-motion";
import { useRouter, useSearchParams } from "next/navigation";
import RightSidePanel from "../../_components/RightSidePanel";
import ConversationHistory from "./ConversationHistory";
import EmployeeInfoCard from "./EmployeeInfoCard";
import { WorkflowForm } from "./workflow/WorkflowForm";
import { employeeChatRoute } from "@/shared/routes";
import { mcpApi } from "@/app/api/mcp";

interface EmployeeChatInterfaceProps {
  employee: Employee;
  contextWindow?: number | null;
}

export default function EmployeeChatInterface({
  employee,
  contextWindow: propContextWindow,
}: EmployeeChatInterfaceProps) {
  // State management
  const {
    employee: { selectedId },
    chat: { sessions },
    setSessionData,
    getSessionData,
    isSessionExpired,
    loadChatHistory,
    initializeChatSession,
    addWorkflowMessage,
    isSplitView,
    addMessage,
    setIsSplitView,
    setSelectedEmployeeId,
    setInputOutputTokens,
    setIsLoadingMoreMessages,
    loadMoreMessages,
  } = useEmployeeManagementStore();
  const [conversationId, setConversationId] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("chat");
  const [isInitializing, setIsInitializing] = useState(false);
  const session = selectedId ? sessions[selectedId] : null;
  const chatStarted = session?.chatStarted || false;
  const [isWorkflowLoading, setIsWorkflowLoading] = useState(false);
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const [activeWorkflowId, setActiveWorkflowId] = useState<string | null>(null);
  const [allUnauthenticatedTools, setAllUnauthenticatedTools] = useState<
    MCPInDB[]
  >([]);
  // Out of Credits Modal store
  const { openModal: openOutOfCreditsModal } = useOutOfCreditsModalStore();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [contextWindow, setContextWindow] = useState(
    propContextWindow || 1000000 // 1M tokens as fallback
  );
  const [isNewConversationLoading, setIsNewConversationLoading] =
    useState(false);
  // Fetch workflows for the employee
  const { data: workflowData, isLoading: isWorkflowsLoading } = useQuery({
    queryKey: ["workflows", employee?.workflow_ids],
    queryFn: () => workflowApi.getWorkflowsByIds(employee?.workflow_ids || []),
    enabled: !!employee?.workflow_ids && employee?.workflow_ids.length > 0,
    staleTime: 60000, // Data considered fresh for 1 minute
    gcTime: 300000, // Keep in cache for 5 minutes after component unmounts

    // Prevent unnecessary refetches:
    refetchOnWindowFocus: false, // Don't refetch when user returns to tab
    refetchOnMount: false, // Don't refetch when component remounts
  });

  // React Query for conversation data to get initial token counts (only fetch once)
  const { data: conversationData } = useQuery({
    queryKey: ["conversation", conversationId],
    queryFn: () => communicationApi.getConversation(conversationId!),
    enabled: !!conversationId,
    staleTime: Infinity, // Never refetch automatically
    gcTime: Infinity, // Never garbage collect
  });

  const { isLoading: isMessagesLoading, refetch: fetchMessages } = useQuery({
    queryKey: ["messages", conversationId],
    queryFn: () =>
      communicationApi.getMessages({
        conversationId: conversationId!,
        page: 1,
        limit: LIMIT,
      }),
    enabled: false,
  });

  // Workflow modal state
  const {
    openWorkflowStartingForm,
    setOpenWorkflowStartingForm,
    workflowSteps,
    activeCorrelationId,
    setAllWorkflows,
    allWorkflows,
    currentWorkflowName,
  } = useWorkflowStore();

  useWorkflowSSEStream({
    correlationId: activeCorrelationId,
    enabled: !!activeCorrelationId,
  });

  // Handler for starting workflow (to be passed to WorkflowForm)
  const handleStartWorkflow = async (selectedWorkflowId: string) => {
    setOpenWorkflowStartingForm(false);
    addWorkflowMessage({
      available_nodes: workflowSteps,
      stepStatus: {},
      approvalRequired: true,
      approved: false,
      selectedWorkflowId,
    });
    setActiveWorkflowId(selectedWorkflowId);
  };

  useEffect(() => {
    setIsSplitView(false);
    setSessionId(null);
  }, [searchParams]);

  // Check all add tools authentications
  useEffect(() => {
    if (employee?.mcp_server_ids?.length) {
      mcpApi.getMcpsByIds(employee?.mcp_server_ids).then((response) => {
        if (response.success && response.mcps) {
          const unauthenticatedTools = response.mcps?.filter(
            (item: any) => item?.integrations?.length && !item?.is_connected
          );
          setAllUnauthenticatedTools(unauthenticatedTools);
        }
      });
    }
  }, [employee]);

  // Update context window when prop changes
  useEffect(() => {
    if (propContextWindow !== undefined) {
      setContextWindow(propContextWindow || 1000000); // 1M tokens as fallback
    }
  }, [propContextWindow]);

  useEffect(() => {
    if (conversationId) {
      const fetchMessagesData = async () => {
        try {
          const data: any = await fetchMessages();
          if (data?.data?.data && data.data.data.length > 0) {
            const transformedMessages = await filterAndTransformMessages(
              data.data.data,
              allWorkflows
            );
            loadChatHistory(
              employee.id + "::" + conversationId,
              transformedMessages,
              {
                currentPage: data.data.metadata.currentPage,
                totalPages: data.data.metadata.totalPages,
                hasNextPage: data.data.metadata.hasNextPage,
                total: data.data.metadata.total,
                pageSize: data.data.metadata.pageSize,
                totalRawMessages: data.data.data.length,
              }
            );
          }
        } catch (error) {
          console.error("Error fetching messages:", error);
        }
      };
      if (session?.messages.length === 0 || !session?.messages) {
        fetchMessagesData();
      } else {
        loadChatHistory(
          employee.id + "::" + conversationId,
          session?.messages || [],
          session?.pagination || {
            currentPage: 1,
            totalPages: 1,
            hasNextPage: false,
            total: session?.messages.length || 0,
            pageSize: LIMIT,
            totalRawMessages: 0,
          }
        );
      }
    }
  }, [
    conversationId,
    employee?.id,
    fetchMessages,
    loadChatHistory,
    filterAndTransformMessages,
    allWorkflows,
  ]);

  useEffect(() => {
    if (workflowData?.workflows.length) {
      setAllWorkflows(workflowData.workflows);
    }
  }, [workflowData]);

  // Initialize tokens from conversation API when data first becomes available
  useEffect(() => {
    if (conversationData && conversationId && selectedId) {
      const session = sessions[selectedId];
      // Only set initial tokens if they haven't been set yet
      if (
        session &&
        session.inputTokens === null &&
        session.outputTokens === null
      ) {
        setInputOutputTokens(
          selectedId,
          conversationData.inputTokens || 0,
          conversationData.outputTokens || 0
        );
      }
    }
  }, [
    conversationData,
    conversationId,
    selectedId,
    sessions,
    setInputOutputTokens,
  ]);

  useEffect(() => {
    const initializeChat = async (
      employeeKey: string,
      conversationId: string
    ) => {
      setIsInitializing(true);
      try {
        const existingSession = getSessionData(employeeKey);
        if (
          existingSession.conversationId === conversationId &&
          existingSession.sessionId &&
          !isSessionExpired(employeeKey)
        ) {
          setSessionId(existingSession.sessionId);
          setSessionData(
            employeeKey,
            conversationId,
            existingSession.sessionId
          );
        } else {
          const newSessionId = await communicationApi.createSession(
            conversationId,
            undefined,
            true,
            employee.id
          );
          setSessionId(newSessionId);
          setSessionData(employeeKey, conversationId, newSessionId);
        }
      } catch (error) {
        toast.error("Could not load the specified conversation");
      } finally {
        setIsInitializing(false);
      }
    };

    const initializeConversation = async () => {
      setIsInitializing(true);
      try {
        const conversationsData = await communicationApi.getConversations({
          agentId: employee.id,
        });
        let currentConversationId: string;
        if (conversationsData?.data && conversationsData.data.length > 0) {
          currentConversationId = conversationsData.data[0].id;
        } else {
          currentConversationId = await communicationApi.createConversation(
            employee.id
          );
        }
        const employeeKey = employee.id + "::" + currentConversationId;
        setSelectedEmployeeId(employeeKey);
        initializeChatSession(employeeKey);
        setConversationId(currentConversationId);
        const newSessionId = await communicationApi.createSession(
          currentConversationId,
          undefined,
          true,
          employee.id
        );
        setSessionId(newSessionId);
        setSessionData(employeeKey, currentConversationId, newSessionId);
      } catch (error) {
        toast.error("Could not initialize conversation with AI employee");
      } finally {
        setIsInitializing(false);
      }
    };

    if (employee?.id && searchParams.get("conversationId")) {
      const employeeKey =
        employee.id + "::" + searchParams.get("conversationId");
      setSelectedEmployeeId(employeeKey);
      initializeChatSession(employeeKey);
      setConversationId(searchParams.get("conversationId")!);
    }

    if (
      employee?.id &&
      conversationId &&
      conversationId === searchParams.get("conversationId")
    ) {
      const employeeKey = employee.id + "::" + conversationId;
      initializeChat(employeeKey, conversationId);
    }

    if (
      employee?.id &&
      !conversationId &&
      localStorage.getItem(LOCAL_STORAGE_KEYS.IS_NEW_CHAT)
    ) {
      initializeConversation();
    }
  }, [employee?.id, searchParams, conversationId]);

  useEffect(() => {
    const executeWorkflow = async () => {
      const payload = {
        sessionId: sessionId || "",
        workflowId: activeWorkflowId || "",
        correlationId: activeCorrelationId || "",
        message: `${currentWorkflowName || ""} workflow started`,
        conversationId: conversationId || "",
      };
      const response = await communicationApi.executeDirectWorkflow(payload);
      if (response) {
        toast.success("Workflow execution started");
        setActiveWorkflowId(null);
      }
    };
    if (
      activeWorkflowId &&
      activeCorrelationId &&
      sessionId &&
      conversationId
    ) {
      executeWorkflow();
    }
  }, [activeWorkflowId, activeCorrelationId, sessionId, conversationId]);

  // SSE connection - use sessionId if available, fallback to conversationId
  const { isSseConnected } = useSSEStream({
    sessionId,
    enabled: !!conversationId && !isInitializing,
  });

  const handleNewConversation = async () => {
    localStorage.removeItem(LOCAL_STORAGE_KEYS.IS_NEW_CHAT);
    setActiveTab("chat");
    setIsNewConversationLoading(true);
    try {
      const newConversationId = await communicationApi.createConversation(
        employee.id
      );
      router.push(
        `${employeeChatRoute}/${employee.id}?conversationId=${newConversationId}`
      );
      router.refresh();
    } catch (error) {
      console.error("Error creating new conversation:", error);
    } finally {
      setIsNewConversationLoading(false);
    }
  };

  const handleConversationSelect = (conversationId: string) => {
    setActiveTab("chat");
    localStorage.removeItem(LOCAL_STORAGE_KEYS.IS_NEW_CHAT);
    router.push(
      `${employeeChatRoute}/${employee.id}?conversationId=${conversationId}`
    );
  };

  useEffect(() => {
    // --- Mention handoff from global chat ---
    const handoffRaw = localStorage.getItem("RUH_GLOBAL_MENTION_HANDOFF");
    if (handoffRaw) {
      try {
        const handoff = JSON.parse(handoffRaw);
        if (handoff.employeeId === employee.id) {
          // Send the message and attachments in this chat context
          (async () => {
            if (!sessionId) return;

            // Check credits before sending message
            const hasCredits =
              await communicationApi.checkCreditsBeforeSending();
            if (!hasCredits) {
              openOutOfCreditsModal();
              localStorage.removeItem("RUH_GLOBAL_MENTION_HANDOFF");
              return;
            }

            try {
              await communicationApi.sendMessage(
                handoff.message,
                sessionId,
                handoff.attachments || []
              );
              addMessage({
                content: handoff.message || "",
                senderType: SenderType.USER,
                attachments: handoff.attachments,
              });
            } catch (err) {
            } finally {
              localStorage.removeItem("RUH_GLOBAL_MENTION_HANDOFF");
            }
          })();
        }
      } catch {}
    }
    // eslint-disable-next-line
  }, [employee?.id, sessionId]);

  // Add load more messages function
  const handleLoadMoreMessages = useCallback(async () => {
    if (!conversationId || !selectedId) return;

    const session = sessions[selectedId];
    if (!session || session.pagination.isLoadingMore) return;

    const nextPage =
      (session.pagination.currentPage + 1) * session.pagination.pageSize >
      session.pagination.totalRawMessages
        ? session.pagination.currentPage + 1
        : Math.floor(
            session.pagination.totalRawMessages / session.pagination.pageSize
          ) + 1;

    try {
      setIsLoadingMoreMessages(selectedId, true);

      const response = await communicationApi.getMessages({
        conversationId: conversationId,
        page: nextPage,
        limit: LIMIT, // You can adjust this limit
        index: session.messages.length,
      });

      if (response.data && response.data.length > 0) {
        const transformedMessages = await filterAndTransformMessages(
          response.data,
          allWorkflows || []
        );

        loadMoreMessages(selectedId, transformedMessages, {
          currentPage: response.metadata.currentPage,
          totalPages: response.metadata.totalPages,
          hasNextPage: response.metadata.hasNextPage,
          total: response.metadata.total,
          pageSize: response.metadata.pageSize,
          totalRawMessages:
            session.pagination.totalRawMessages + response.data.length,
        });
      }
    } catch (error) {
      console.error("Error loading more messages:", error);
      toast.error("Failed to load more messages");
    } finally {
      setIsLoadingMoreMessages(selectedId, false);
    }
  }, [
    conversationId,
    selectedId,
    sessions,
    setIsLoadingMoreMessages,
    loadMoreMessages,
    allWorkflows,
  ]);

  return (
    <div
      className={`flex flex-col h-full pt-4 md:pt-0 ${activeTab === "history" ? "static" : "fixed"} md:static`}
    >
      {/* Header */}
      {isMobile && <div className="h-[50px] min-w-[100vw]"></div>}

      <div className="flex flex-col gap-3 lg:flex-row lg:items-center justify-between p-2 md:p-4 border-b border-brand-stroke bg-white">
        <div className="flex flex-1/3 items-center gap-3">
          <Avatar className="w-10 h-10">
            <AvatarImage src={employee?.avatar} alt={employee?.name} />
            <AvatarFallback className="bg-gradient-to-br from-purple-500 to-purple-600 text-white text-sm font-medium">
              {employee?.name.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          <div className="flex items-center gap-2">
            <h3 className="font-semibold text-brand-primary-font line-clamp-1 ">
              {employee?.name}, {employee?.agent_topic_type || "AI Assistant"}
            </h3>
          </div>
        </div>

        <div className="flex justify-between lg:justify-around lg:flex-2/3 items-center  gap-4">
          {/* Tabs */}
          <div className="flex  items-center gap-2 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => {
                setActiveTab("chat");
              }}
              className={`flex items-center gap-2 px-3 py-[6px] text-[12px] rounded-md text-sm font-medium transition-all duration-200 font-satoshi-regular ${
                activeTab === "history"
                  ? "text-gray-600 hover:text-gray-900"
                  : "bg-white text-gray-900 shadow-sm"
              }`}
            >
              <Bot className="h-4 w-4" />
              Chat
            </button>
            <button
              onClick={() => setActiveTab("history")}
              className={`flex w-auto items-center gap-2 px-3 py-[6px] rounded-md text-[12px] font-medium transition-all duration-200 font-satoshi-regular ${
                activeTab === "history"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              <History className="h-4 w-4" />
              History
            </button>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end items-center gap-3">
            {chatStarted && (
              <Button
                variant="primary"
                size="sm"
                onClick={handleNewConversation}
                className="px-[6px] py-[10px] flex gap-0"
                disabled={isNewConversationLoading}
              >
                {isNewConversationLoading ? (
                  <LoaderIcon className="h-4 w-4 mr-2" />
                ) : (
                  <Plus className="h-4 w-4 mr-2" />
                )}
                New Task
              </Button>
            )}

            <Button
              variant="linkSecondary"
              className="text-primary hover:text-secondary !p-0 m-0 h-auto w-auto"
              size="sm"
              onClick={() => {
                resetEmployeeCreateState();
                router.push(`/employees/${employee.id}`);
              }}
            >
              <Edit className="h-4 w-4 mr-2" />
            </Button>
          </div>
        </div>
      </div>
      {activeTab === "history" ? (
        <div className="flex-1 flex flex-col m-0 lg:overflow-hidden px-[12px] lg:px-[35px] py-[24px] overflow-auto">
          <ConversationHistory
            employee={employee}
            onConversationSelect={handleConversationSelect}
          />
        </div>
      ) : (
        <>
          {/* Token Usage Bar - Only visible in chat tab */}
          {chatStarted ? (
            <div className="flex items-center justify-center px-2 md:px-9 py-2.5 bg-brand-background border-b border-brand-stroke text-xs w-full">
              <div className="flex items-center ">
                <div className="flex items-center gap-[9px]">
                  <TokenInfoIcon />
                  <div className="flex align-center justify-center gap-3 px-[10px] h-[26px]  rounded-[6px] bg-gray-100">
                    <div className="text-secondary-font text-sm flex items-center justify-center">
                      Tokens:
                    </div>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center cursor-help">
                          <ArrowUpIcon width={20} />
                          <span className="text-sm text-brand-primary">
                            {session?.inputTokens || 0}
                          </span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Input tokens</p>
                      </TooltipContent>
                    </Tooltip>

                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center cursor-help">
                          <ArrowDownIcon width={20} />
                          <span className="text-sm text-brand-primary">
                            {session?.outputTokens || 0}
                          </span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Output tokens</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                  <div className="flex align-center justify-center gap-3 px-[10px] h-[26px]  rounded-[6px] bg-gray-100">
                    <div className="text-secondary-font text-sm flex items-center justify-center">
                      Context window:
                    </div>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="flex items-center cursor-help">
                          <span className="text-sm text-brand-primary">
                            {propContextWindow === null ||
                            propContextWindow === undefined
                              ? "1M"
                              : contextWindow >= 1000000
                                ? `${contextWindow / 1000000}M`
                                : `${contextWindow / 1000}k`}{" "}
                            (
                            {Math.min(
                              100,
                              parseFloat(
                                (
                                  (((session?.inputTokens || 0) +
                                    (session?.outputTokens || 0)) /
                                    contextWindow) *
                                  100
                                ).toFixed(2)
                              )
                            )}
                            %)
                          </span>
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>
                          Context limit used, as this value gets higher,
                          response might be less accurate, please start a new
                          chat for better responses
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            !isMessagesLoading &&
            conversationId && (
              <div className="flex-1 w-[80%] mx-auto">
                <EmployeeInfoCard
                  allUnauthenticatedTools={allUnauthenticatedTools}
                  employee={employee}
                  isVisible={!chatStarted}
                  workflows={workflowData?.workflows || []}
                  isWorkflowsLoading={isWorkflowsLoading}
                  conversationId={conversationId || ""}
                />
              </div>
            )
          )}
          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="flex-1 flex flex-col overflow-hidden"
          >
            <TabsContent
              value="chat"
              className={`flex-1 flex m-0  overflow-hidden transition-all duration-300 ease-in-out ${
                isSplitView ? "flex-row w-full" : "w-full flex-col"
              }`}
            >
              {/* Left: Chat */}
              <motion.div
                animate={{
                  width: isSplitView
                    ? isTablet || isMobile
                      ? "100%"
                      : "50%"
                    : "100%",
                }}
                transition={{ duration: 0.3, ease: "easeInOut" }}
                className="relative h-full flex flex-col"
              >
                {/* Messages area - full width with centered content */}
                <div className="flex-1 w-full overflow-hidden relative">
                  <div className="h-full w-full overflow-y-auto relative">
                    <div
                      className={`${
                        isSplitView
                          ? "w-full"
                          : "w-full md:w-[95%] lg:w-[90%] xl:w-[85%] max-w-5xl  md:px-3 lg:px-4 xl:px-5"
                      } mx-auto px-2 relative`}
                    >
                      <ChatMessagesList
                        isLoading={isMessagesLoading || !conversationId}
                        employee={employee}
                        onLoadMore={handleLoadMoreMessages}
                        hasMoreMessages={session?.pagination.hasNextPage}
                        isLoadingMoreMessages={
                          session?.pagination.isLoadingMore
                        }
                      />
                    </div>
                  </div>
                </div>

                {/* Input area - centered to match messages */}
                <div
                  className={`${
                    isSplitView
                      ? "w-full"
                      : "w-full md:w-[95%] lg:w-[90%] xl:w-[85%] max-w-5xl  md:px-3 lg:px-4 xl:px-5"
                  } mx-auto px-2`}
                >
                  {employee && conversationId && (
                    <ChatInput
                      chatStarted={chatStarted}
                      employee={employee}
                      conversationId={conversationId}
                      sessionId={sessionId}
                      workflows={workflowData?.workflows || []}
                      className="mb-2 md:mb-5"
                    />
                  )}
                  {isMobile && <div className="h-[80px]"></div>}
                </div>
              </motion.div>
              {/* Right: Panel - Desktop (lg and above) */}
              {!isTablet && (
                <div className={`${isSplitView ? "w-[50%]" : "w-full"}`}>
                  <RightSidePanel isOpen={isSplitView} />
                </div>
              )}

              {/* Right: Panel - Mobile and Tablet (md and below) as Sheet */}
              {isTablet && (
                <Sheet open={isSplitView} onOpenChange={setIsSplitView}>
                  <SheetContent side="right" className="w-full sm:max-w-md p-0">
                    <RightSidePanel isOpen={true} />
                  </SheetContent>
                </Sheet>
              )}
            </TabsContent>

            <TabsContent
              value="history"
              className="flex-1 flex flex-col m-0 overflow-hidden px-[35px] py-[24px]"
            >
              <ConversationHistory
                employee={employee}
                onConversationSelect={handleConversationSelect}
              />
            </TabsContent>
          </Tabs>
        </>
      )}

      <WorkflowForm
        onStartWorkflow={handleStartWorkflow}
        isWorkflowStreaming={isWorkflowLoading}
      />
    </div>
  );
}
