"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useWorkflowStore } from "@/hooks/use-workflow";
import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { cn } from "@/lib/utils";
import { sanitizeString } from "@/services/helper";
import { Employee, MCPInDB, WorkflowInDB } from "@/shared/interfaces";
import { Database, Workflow, Wrench, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface EmployeeInfoCardProps {
  employee: Employee;
  isVisible: boolean;
  workflows?: WorkflowInDB[];
  isWorkflowsLoading?: boolean;
  onWorkflowClick?: (workflow: WorkflowInDB) => void;
  conversationId?: string;
  allUnauthenticatedTools?: MCPInDB[];
}

export default function EmployeeInfoCard({
  employee,
  isVisible,
  workflows = [],
  isWorkflowsLoading = false,
  onWorkflowClick,
  conversationId,
  allUnauthenticatedTools,
}: EmployeeInfoCardProps) {
  const [isViewMoreOpen, setIsViewMoreOpen] = useState(false);
  const [showAuthWarning, setShowAuthWarning] = useState(true);

  const {
    setOpenWorkflowStartingForm,
    setCurrentWorkflow,
    setCurrentWorkflowName,
    setWorkflowValues,
    setWorkflowSteps,
  } = useWorkflowStore();
  const router = useRouter();

  const handlePaletteItemClick = (workflow: WorkflowInDB) => {
    if (!employee) return;
    setCurrentWorkflow(employee.id + "::" + conversationId, workflow.id);
    setCurrentWorkflowName(workflow.name);
    setWorkflowValues(workflow.start_nodes || []);
    setWorkflowSteps(workflow.available_nodes);
    setOpenWorkflowStartingForm(true);
  };

  // Add Workflow button handler
  const handleAddWorkflow = () => {
    useEmployeeCreateStore.getState().setFormStep(4); // Workflow step
    router.push(`/employees/${employee.id}`);
  };

  // Add Tools button handler
  const handleAddTools = () => {
    useEmployeeCreateStore.getState().setFormStep(3); // MCP/Tools step
    router.push(`/employees/${employee.id}`);
  };

  const handleAddKnowledgeBase = () => {
    useEmployeeCreateStore.getState().setFormStep(2); // Knowledge Base step
    router.push(`/employees/${employee.id}`);
  };

  const hasMoreWorkflows = workflows && workflows.length > 3;
  const displayedWorkflows = workflows.slice(0, 3);
  const remainingWorkflows = workflows.slice(3);

  return (
    <div
      className={cn(
        "transition-all duration-300 ease-in-out bg-brand-background relative",
        isVisible ? "opacity-100 max-h-96" : "opacity-0 max-h-0 overflow-hidden"
      )}
    >
      {allUnauthenticatedTools &&
        allUnauthenticatedTools.length > 0 &&
        showAuthWarning && (
          <div className="bg-red-100 p-4 mt-2 rounded-md z-10 top-0 left-0 right-0 absolute">
            <button
              onClick={() => setShowAuthWarning(false)}
              className="absolute top-4 right-2 text-gray-600 hover:text-gray-900"
              aria-label="Close"
            >
              <X size={18} />
            </button>
            <p className="text-red-800 pr-6 text-sm flex justify-center">
              You have some tools which required authentication to use them.
            </p>
          </div>
        )}
      <div className="flex flex-col items-center justify-start md:justify-center py-8 md:py-16 px-4 md:px-8 overflow-auto max-h-[300px] md:max-h-full scrollbar-hide">
        {/* Large Avatar */}
        <Avatar className="w-24 h-24 mb-6">
          <AvatarImage
            src={employee.avatar}
            alt={employee.name}
            className="object-cover"
          />
          <AvatarFallback className="bg-gradient-to-br from-purple-500 to-purple-600 text-white text-3xl font-semibold">
            {employee.name.charAt(0).toUpperCase()}
          </AvatarFallback>
        </Avatar>

        {/* Employee Name and Title */}
        <div className="text-center mb-6">
          <h1 className="text-2xl font-bold  mb-2 line-clamp-2">
            {employee.name}, {employee.agent_topic_type || "AI Assistant"}
          </h1>
          <p className="text-text-secondary line-clamp-2">
            {employee.description ||
              `Meet ${
                employee.name
              }, your go-to expert for everything ${employee.agent_topic_type?.toLowerCase()}! ${
                employee.name
              } can do`}{" "}
            ✏️
          </p>
        </div>

        {/* Workflow Buttons */}
        <div className="flex flex-col items-center gap-4 mb-6 pb-4 w-full mx-auto">
          <div className="flex flex-wrap gap-4 w-[90%] items-center justify-center mx-auto">
            {workflows && workflows.length > 0 ? (
              <>
                {displayedWorkflows.map((workflow) => (
                  <Button
                    key={workflow.id}
                    variant="secondary"
                    className="flex-shrink-0"
                    onClick={() => handlePaletteItemClick(workflow)}
                  >
                    {sanitizeString(workflow.name)}
                  </Button>
                ))}

                {hasMoreWorkflows && (
                  <Dialog
                    open={isViewMoreOpen}
                    onOpenChange={setIsViewMoreOpen}
                  >
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        className="flex-shrink-0 text-brand-primary border-brand-primary hover:bg-brand-card-hover"
                      >
                        View More ({remainingWorkflows.length})
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="bg-brand-card border-brand-stroke md:max-w-[500px]">
                      <DialogHeader>
                        <DialogTitle className="text-brand-primary-font">
                          All Workflows for {employee.name}
                        </DialogTitle>
                      </DialogHeader>
                      <div className="flex flex-wrap gap-3 max-h-96 overflow-y-auto">
                        {workflows.map((workflow) => (
                          <Button
                            key={workflow.id}
                            variant="secondary"
                            className="flex-shrink-0"
                            onClick={() => {
                              handlePaletteItemClick(workflow);
                              setIsViewMoreOpen(false);
                            }}
                          >
                            {sanitizeString(workflow.name)}
                          </Button>
                        ))}
                      </div>
                    </DialogContent>
                  </Dialog>
                )}
              </>
            ) : (
              <div className="flex gap-4 w-full items-center justify-center flex-wrap">
                <Button
                  onClick={handleAddWorkflow}
                  variant="primary"
                  className="flex-shrink-0"
                >
                  <Workflow className="!h-4 !w-4" />
                  Add Workflow
                </Button>

                {employee.mcps && employee.mcps.length === 0 && (
                  <Button
                    onClick={handleAddTools}
                    variant="primary"
                    className="flex-shrink-0"
                  >
                    <Wrench className="!h-4 !w-4" />
                    Add Tools
                  </Button>
                )}

                {employee.files && employee.files.length === 0 && (
                  <Button
                    onClick={handleAddKnowledgeBase}
                    variant="primary"
                    className="flex-shrink-0"
                  >
                    <Database className="!h-4 !w-4" />
                    Knowledge Base
                  </Button>
                )}
              </div>
            )}
          </div>

          {/* Instruction Text */}
          {workflows && workflows.length > 0 && (
            <div className="text-center mt-2">
              <p className="text-text-secondary text-sm">
                💡 To trigger workflows anytime, type{" "}
                <span className="font-medium text-brand-primary">"/"</span> in
                the input box to select
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
