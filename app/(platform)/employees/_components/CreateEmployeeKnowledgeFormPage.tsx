"use client";

import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { createEmployeeProfileSchema } from "@/lib/schemas/createEmployee";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { FileUpload } from "../../_components/FileUpload";
import {
  ArrowLeft,
  EyeIcon,
  Trash2,
  Plus,
  PackageIcon,
  X,
  Globe,
  MoreVertical,
  MoreHorizontal,
  Brain,
} from "lucide-react";
import {
  useState,
  useRef,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from "react";
import { KnowledgeTable } from "../../_components/KnowledgeTable";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Image from "next/image";
import { EmployeeAvatar } from "@/components/shared/EmployeeAvatar";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { AgentKnowledgeUpdatePayload } from "@/shared/interfaces";
import { agentApi } from "@/app/api/agent";
import { toast } from "sonner";
import { useEmployeeEditStore } from "@/hooks/useEmployeeEditStore";
import { LoadingSpinner } from "@/components/shared/LoadingSpinner";
import { PublishAgentModal } from "@/components/modals/PublishAgentModal";
import { validateProfileRequiredFields } from "./CreateEmployeeProfileFormPage";
import {
  agentRoute,
  dashboardRoute,
  employeeChatRoute,
  employeesRoute,
  redirectionAfterCreateEmployee,
} from "@/shared/routes";
import { useRouter } from "next/navigation";
import { getAgentEditPayload } from "@/lib/utils";
import { LOCAL_STORAGE_KEYS } from "@/shared/constants";
import { AnalyticsEvents, Employee } from "@/shared/enums";
import { useMixpanelTrack } from "@/hooks/useMixPanelTrack";

const createKnowledgeSchema = createEmployeeProfileSchema.pick({
  files: true,
  urls: true,
});

type CreateKnowledgeSchema = z.infer<typeof createKnowledgeSchema>;

// Interface for component props
interface CreateEmployeeKnowledgeFormPageProps {
  agentData?: any;
  isEdit?: boolean;
  isLoading?: boolean;
  onSubmitAgent: (formData: any, provider?: string, model?: string) => void;
  isCreatingAgent?: boolean;
  isUpdatingAgent?: boolean;
}

// Interface for file metadata
interface FileMetadata {
  url: string;
  name: string;
  size: number;
  uploadDate: Date;
}

// Helper to get filename from GCS URL
const getFileNameFromUrl = (url: string): string => {
  if (typeof url !== "string") {
    return "Unknown file";
  }
  const fileName = url.split("/").pop();
  return fileName || "Unknown file";
};

const CreateEmployeeKnowledgeFormPage = forwardRef(
  (
    {
      onSubmitAgent,
      isCreatingAgent,
      isUpdatingAgent,
      ...props
    }: CreateEmployeeKnowledgeFormPageProps,
    ref
  ) => {
    const {
      setData,
      formStep,
      setFormStep,
      setCheckRequiredField,
      data,
      isPublishModalOpen,
      openPublishModal,
      closePublishModal,
      pendingSaveStep,
      setPendingSaveStep,
      originForCreateAndEdit,
    } = useEmployeeCreateStore();
    const queryClient = useQueryClient();
    const [urls, setUrls] = useState<string[]>([]);
    const [openFilesTable, setOpenFilesTable] = useState(false);
    const [openUrlsTable, setOpenUrlsTable] = useState(false);
    const [urlError, setUrlError] = useState<string | null>(null);
    const [isSubmittingForm, setIsSubmittingForm] = useState(false);
    const [isFileUploading, setIsFileUploading] = useState(false);
    const [isImportModalOpen, setIsImportModalOpen] = useState(false);
    const [importUrl, setImportUrl] = useState("");
    const [fileMetadata, setFileMetadata] = useState<FileMetadata[]>([]);
    const track = useMixpanelTrack();
    const { closeModal } = useEmployeeEditStore();
    const router = useRouter();
    const form = useForm<CreateKnowledgeSchema>({
      resolver: zodResolver(createKnowledgeSchema),
      defaultValues: {
        files: data.files || [],
        urls: data.urls || [],
      },
    });

    useImperativeHandle(ref, () => ({
      saveToStore: () => {
        setData(form.getValues());
      },
    }));

    // Initialize form with agentData when in edit mode
    useEffect(() => {
      if (props.isEdit && data) {
        const formData = {
          files: data.files || [],
          urls: data.urls || [],
        };
        form.reset(formData);

        // Also update local state for URLs
        setUrls(data.urls || []);

        // Initialize file metadata if available
        if (data.files && data.files.length > 0) {
          const metadata: FileMetadata[] = data.files.map((file: any) => ({
            url: file?.file,
            name: getFileNameFromUrl(file?.file),
            size: file?.size, // Size not available from agentData
            uploadDate: new Date(file?.created_at), // Use current date as fallback
          }));
          setFileMetadata(metadata);
        }
      }
    }, [props.isEdit, data, form]);

    useEffect(() => {
      if (pendingSaveStep === 2) {
        setData({
          ...data,
          ...form.getValues(),
          // files: (form.getValues("files") || []).map((file: any) => file?.file),
          // urls: form.getValues("urls") || [],
        });
        setPendingSaveStep(null);
      }
    }, [pendingSaveStep, setData, setPendingSaveStep, form]);

    // this function is for check website url is correct.
    const validateWebsiteUrl = (
      url: string
    ): { isValid: boolean; error?: string } => {
      if (!url.trim()) {
        return { isValid: false, error: "URL is required" };
      }

      // Basic URL validation using Zod
      const urlSchema = z.string().url("Please enter a valid URL");

      try {
        urlSchema.parse(url);
      } catch (error) {
        return {
          isValid: false,
          error: "Please enter a valid URL (e.g., https://example.com)",
        };
      }

      // Additional validation for website URLs
      try {
        const urlObj = new URL(url);

        // Check if protocol is http or https
        if (!["http:", "https:"].includes(urlObj.protocol)) {
          return {
            isValid: false,
            error: "URL must use HTTP or HTTPS protocol",
          };
        }

        // Check if hostname is valid (not empty and contains at least one dot)
        if (!urlObj.hostname || !urlObj.hostname.includes(".")) {
          return { isValid: false, error: "Please enter a valid website URL" };
        }
        // Check for common invalid patterns
        if (urlObj.hostname.length < 3) {
          return { isValid: false, error: "Please enter a valid website URL" };
        }

        return { isValid: true };
      } catch (error) {
        return { isValid: false, error: "Please enter a valid website URL" };
      }
    };

    const onSubmit = async (
      formData: any,
      provider?: string,
      model?: string
    ) => {
      onSubmitAgent(
        {
          ...data,
          ...formData,
          files: formData.files || [],
          urls: formData.urls || [],
        },
        provider,
        model
      );
    };

    // Helper function to format file size
    const formatFileSize = (bytes: number): string => {
      if (bytes === 0) return "0 B";
      const k = 1024;
      const sizes = ["B", "KB", "MB", "GB"];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
    };

    // For GCS uploaded files (stored in form.files)
    const handleDeleteGcsFile = (urlToDelete: any) => {
      const currentFiles = form.getValues("files") || [];
      if (props.isEdit) {
        updateAgentKnowledge({
          data: {
            files: currentFiles
              .filter((file: any) => file?.file !== urlToDelete)
              ?.map((file: any) => file?.file),
          },
        });
        return;
      }
      form.setValue(
        "files",
        currentFiles.filter((file: any) => file?.file !== urlToDelete),
        { shouldValidate: true, shouldDirty: true }
      );
      // Also remove from metadata
      setFileMetadata((prev) =>
        prev.filter((file: any) => file?.file !== urlToDelete)
      );
    };

    const handlePreviewGcsFile = (file: any) => {
      window.open(file, "_blank");
    };

    // For manually added URLs
    const deleteManualUrl = (urlToDelete: string) => {
      const currentFormUrls = form.getValues("urls") || [];
      form.setValue(
        "urls",
        currentFormUrls.filter((u) => u !== urlToDelete),
        { shouldValidate: true, shouldDirty: true }
      );
      setUrls((prevUrls) => prevUrls.filter((u) => u !== urlToDelete));
    };

    const previewManualUrl = (url: string) => {
      window.open(url, "_blank");
    };

    const handleImportWebsite = () => {
      if (!importUrl.trim()) {
        setUrlError("URL is required");
        return;
      }

      // Validate the website URL
      const validation = validateWebsiteUrl(importUrl.trim());
      if (!validation.isValid) {
        setUrlError(validation.error || "Invalid URL");
        return;
      }

      // Clear any previous errors
      setUrlError(null);

      const currentFormUrls = form.getValues("urls") || [];

      // Check if URL already exists
      if (currentFormUrls.includes(importUrl.trim())) {
        setUrlError("This URL has already been added");
        return;
      }

      // Add the validated URL
      form.setValue("urls", [...currentFormUrls, importUrl.trim()], {
        shouldValidate: true,
        shouldDirty: true,
      });

      setImportUrl("");
      setIsImportModalOpen(false);
    };

    const watchedGcsFiles: any = form.watch("files") || [];
    const watchedManualUrls = form.watch("urls") || [];

    const handlePublishClick = async () => {
      const profileErrors = validateProfileRequiredFields(data);
      if (profileErrors.length > 0) {
        // Redirect to profile step and show validation errors
        setFormStep(1);
        setCheckRequiredField(true);
        profileErrors.forEach((error) => {
          toast.error(error.message, {
            duration: 4000,
            position: "top-center",
          });
        });
        return;
      }
      const isValid = await form.trigger();
      if (!isValid) {
        const errors = form.formState.errors;
        Object.values(errors).forEach((error) => {
          if (error?.message) {
            toast.error(error.message, {
              duration: 4000,
              position: "top-center",
            });
          }
        });
        return;
      }

      if (props.isEdit) {
        // In edit mode, directly call the API without modal
        onSubmit(form.getValues());
      } else {
        // In add mode, open the modal
        openPublishModal();
      }
    };

    const handleModalPublish = (provider: string, model: string) => {
      closePublishModal();
      onSubmit(form.getValues(), provider, model);
    };

    const handleModalDiscard = () => {
      closePublishModal();
    };

    //update data api
    const {
      mutate: updateAgentKnowledge,
      isPending: isUpdatingAgentKnowledge,
    } = useMutation({
      mutationFn: ({ data }: { data: any }) =>
        agentApi.updateAgentCombined(props?.agentData?.id, data),
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["agents"] });
        queryClient.invalidateQueries({
          queryKey: ["agent", props?.agentData?.id],
        });
        toast.success("Knowledge updated successfully", {
          position: "top-center",
        });
      },
      onError: (error) => {
        toast.error(error.message || "Failed to update knowledge", {
          position: "top-center",
        });
      },
    });

    if (openFilesTable) {
      return (
        <KnowledgeTable
          files={watchedGcsFiles.map((file: any) =>
            getFileNameFromUrl(file?.file)
          )}
          closeTable={() => setOpenFilesTable(false)}
          deleteFile={(fileUrl) => handleDeleteGcsFile(fileUrl as string)}
          previewFile={(fileUrl) => handlePreviewGcsFile(fileUrl as string)}
        />
      );
    }

    if (openUrlsTable) {
      return (
        <KnowledgeTable
          files={watchedManualUrls}
          closeTable={() => setOpenUrlsTable(false)}
          deleteFile={(url) => deleteManualUrl(url as string)}
          previewFile={(url) => previewManualUrl(url as string)}
        />
      );
    }

    if (props.isLoading) {
      return <LoadingSpinner message="Loading knowledge data..." />;
    }
    if (isUpdatingAgentKnowledge) {
      return <LoadingSpinner message="Updating knowledge..." />;
    }

    return (
      <div className="flex flex-col h-full md:p-[25px] p-[18px] gap-[17px]">
        {/* Top Header */}
        <div className="flex flex-col justify-end items-start md:flex-row md:justify-end md:items-center mt-[50px] md:mt-0">
          {/* <Button
            variant="ghost"
            className="text-brand-primary hover:text-brand-primary/90 hover:bg-purple-50 p-0 font-medium"
            onClick={() => setFormStep(formStep - 1)}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button> */}

          <div className="flex gap-2">
            <Button
              variant="outline"
              className="px-6 w-auto"
              onClick={() => {
                if (originForCreateAndEdit == Employee.CARD) {
                  router.push(employeesRoute);
                } else {
                  if (props?.isEdit) {
                    // Store agentData in localStorage cache
                    const cacheKey = LOCAL_STORAGE_KEYS.AGENT_DETAILS_CACHE;
                    const cache = JSON.parse(
                      localStorage.getItem(cacheKey) || "{}"
                    );
                    if (props?.agentData?.id) {
                      cache[props.agentData.id] = props.agentData;
                      localStorage.setItem(cacheKey, JSON.stringify(cache));
                    }
                    localStorage.setItem(
                      LOCAL_STORAGE_KEYS.IS_NEW_CHAT,
                      "true"
                    );
                    router.push(`${employeeChatRoute}/${props?.agentData?.id}`);
                  } else {
                    router.push(dashboardRoute);
                  }
                }
              }}
            >
              Close
            </Button>
            {!props?.isEdit && (
              <Button
                variant="primary"
                className="px-6 w-auto"
                disabled={isCreatingAgent || isUpdatingAgent}
                onClick={handlePublishClick}
              >
                {isCreatingAgent || isUpdatingAgent ? (
                  <>
                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    {props?.isEdit ? "Saving..." : "Publishing..."}
                  </>
                ) : props?.isEdit ? (
                  "Save Changes"
                ) : (
                  "Publish Changes"
                )}
              </Button>
            )}
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          <div className="max-w-4xl min-w-full">
            {/* Main White Container */}
            <div className="bg-white rounded-lg border border-gray-200 px-[10px] md:px-[32px] py-[24px] shadow-sm ">
              {/* Agent Info Card */}
              <div className="flex flex-row gap-2 items-start mb-8 w-full justify-between">
                <div className="flex-shrink-1 md:flex-shrink-0">
                  <h1>Knowledge</h1>
                  <p className="text-text-secondary text-sm">
                    {watchedGcsFiles.length > 0 || watchedManualUrls.length > 0
                      ? `Upload your data relationships`
                      : `Add information from multiple sources for stronger, more contextualized outputs`}
                  </p>
                </div>

                {/* Action Buttons - Always visible in top right */}
                {(watchedGcsFiles.length > 0 ||
                  watchedManualUrls.length > 0) && (
                  <div className="flex md:flex-row flex-col gap-4">
                    <FileUpload
                      onUploadSuccess={(gcsUrls) => {
                        const currentFiles = form.getValues("files") || [];
                        if (props.isEdit) {
                          updateAgentKnowledge({
                            data: {
                              files: [
                                ...currentFiles.map((file: any) => file?.file),
                                ...gcsUrls.map((file: any) => file?.file),
                              ],
                            },
                          });
                          return;
                        }
                        form.setValue("files", [...currentFiles, ...gcsUrls], {
                          shouldValidate: true,
                          shouldDirty: true,
                        });

                        const newMetadata: FileMetadata[] = gcsUrls.map(
                          (file: any) => ({
                            url: file?.file,
                            name: getFileNameFromUrl(file?.file),
                            size: file?.size, // Would need to be passed from FileUpload component
                            uploadDate: new Date(file?.created_at),
                          })
                        );
                        setFileMetadata((prev) => [...prev, ...newMetadata]);
                      }}
                      gcsPathPrefix="employee_knowledge/"
                      onUploadingStateChange={setIsFileUploading}
                      acceptedFileTypes=".pdf,.doc,.docx,.txt,.md"
                      className="hidden"
                    />
                    <Button
                      onClick={() => {
                        // Trigger the FileUpload component
                        track(AnalyticsEvents.ADDED_KNOWLEDGE, {
                          ...data,
                          ...form.getValues(),
                        });
                        const fileInput = document.querySelector(
                          'input[type="file"]'
                        ) as HTMLInputElement;
                        fileInput?.click();
                      }}
                      variant={"tertiary"}
                      className="px-6"
                      disabled={isFileUploading}
                    >
                      {isFileUploading ? (
                        <>
                          <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          Uploading...
                        </>
                      ) : (
                        <>
                          <Plus className="w-4 h-4 mr-2" />
                          Upload Files
                        </>
                      )}
                    </Button>
                    {/* <Button
                      variant={"tertiary"}
                      className="px-6"
                      onClick={() => {
                        setIsImportModalOpen(true);
                        setUrlError(null);
                        setImportUrl("");
                      }}
                    >
                      <Globe className="w-4 h-4 mr-2" />
                      Import website
                    </Button> */}
                    {/* <Button
                      variant="tertiary"
                      className="px-6 text-text-secondary"
                    >
                      <Brain className="w-4 h-4 mr-2" />
                      Add Existing Knowledge
                    </Button> */}
                  </div>
                )}
              </div>

              {/* Knowledge Section */}
              {watchedGcsFiles.length > 0 || watchedManualUrls.length > 0 ? (
                /* Show uploaded files/URLs in table format */
                <div className="space-y-6">
                  {/* Files Table */}
                  {watchedGcsFiles.length > 0 && (
                    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                      {/* Table Header */}
                      <div className="grid grid-cols-12 gap-4 p-4 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700">
                        <div className="col-span-5">Name</div>
                        <div className="col-span-2">File Size</div>
                        <div className="col-span-3">Date Created</div>
                        <div className="col-span-2"></div>
                      </div>

                      {/* Table Rows */}
                      <div className="divide-y divide-gray-200">
                        {watchedGcsFiles.map((gcsFile: any, index: number) => (
                          <div
                            key={index}
                            className="grid grid-cols-12 gap-4 p-4 hover:bg-gray-50 items-center"
                          >
                            <div className="col-span-5 flex items-center gap-3">
                              <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center flex-shrink-0">
                                <PackageIcon className="w-4 h-4 text-blue-600" />
                              </div>
                              <span className="text-sm font-medium text-gray-900 truncate">
                                {getFileNameFromUrl(gcsFile?.file)}
                              </span>
                            </div>
                            <div className="col-span-2 text-sm text-gray-600">
                              {gcsFile?.size > 0
                                ? formatFileSize(gcsFile?.size)
                                : "Unknown size"}
                            </div>
                            <div className="col-span-3 text-sm text-gray-600">
                              {new Date(gcsFile?.created_at).toLocaleDateString(
                                "en-US",
                                {
                                  month: "short",
                                  day: "numeric",
                                  year: "numeric",
                                }
                              )}
                            </div>
                            <div className="col-span-2 flex justify-end">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="text-gray-400 hover:text-gray-600"
                                  >
                                    <MoreHorizontal className="w-4 h-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={() =>
                                      handlePreviewGcsFile(gcsFile?.file)
                                    }
                                    className="cursor-pointer"
                                  >
                                    <EyeIcon className="w-4 h-4 mr-2" />
                                    Preview
                                  </DropdownMenuItem>

                                  <DropdownMenuItem
                                    onClick={() =>
                                      handleDeleteGcsFile(gcsFile?.file)
                                    }
                                    className="cursor-pointer"
                                  >
                                    <Trash2 className="w-4 h-4 mr-2" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* URLs Table */}
                  {watchedManualUrls.length > 0 && (
                    <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                      {/* Table Header */}
                      <div className="grid grid-cols-12 gap-4 p-4 bg-gray-50 border-b border-gray-200 text-sm font-medium text-gray-700">
                        <div className="col-span-5">Name</div>
                        <div className="col-span-2">Type</div>
                        <div className="col-span-3">{/* Date Created */}</div>
                        <div className="col-span-2"></div>
                      </div>

                      {/* Table Rows */}
                      <div className="divide-y divide-gray-200">
                        {watchedManualUrls.map((url, index) => (
                          <div
                            key={index}
                            className="grid grid-cols-12 gap-4 p-4 hover:bg-gray-50 items-center"
                          >
                            <div className="col-span-5 flex items-center gap-3">
                              <div className="w-8 h-8 bg-green-100 rounded flex items-center justify-center flex-shrink-0">
                                <Globe className="w-4 h-4 text-green-600" />
                              </div>
                              <span className="text-sm font-medium text-gray-900 truncate">
                                {url}
                              </span>
                            </div>
                            <div className="col-span-2 text-sm text-gray-600">
                              Website
                            </div>
                            <div className="col-span-3 text-sm text-gray-600">
                              {/* {new Date().toLocaleDateString("en-US", {
                                month: "short",
                                day: "numeric",
                                year: "numeric",
                              })} */}
                            </div>
                            <div className="col-span-2 flex justify-end">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    className="text-gray-400 hover:text-gray-600"
                                  >
                                    <MoreHorizontal className="w-4 h-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem
                                    onClick={() => previewManualUrl(url)}
                                    className="cursor-pointer"
                                  >
                                    <EyeIcon className="w-4 h-4 mr-2" />
                                    Preview
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => deleteManualUrl(url)}
                                    className="cursor-pointer text-red-600 hover:text-red-700"
                                  >
                                    <Trash2 className="w-4 h-4 mr-2" />
                                    Remove
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                /* Empty State with Large White Box */
                <div className="flex flex-col items-center justify-center py-16 gap-4">
                  {/* Large White Box with Tool Icons Grid */}
                  <Image
                    src={"/assets/dashboard/create-agent.svg"}
                    width={216}
                    height={120}
                    alt={"create-agent-img"}
                  />

                  {/* Upload Files Button */}
                  <div className="">
                    <FileUpload
                      onUploadSuccess={(gcsUrls) => {
                        const currentFiles = form.getValues("files") || [];
                        if (props.isEdit) {
                          updateAgentKnowledge({
                            data: {
                              files: [
                                ...currentFiles.map((file: any) => file?.file),
                                ...gcsUrls.map((file: any) => file?.file),
                              ],
                            },
                          });
                          return;
                        }
                        form.setValue("files", [...currentFiles, ...gcsUrls], {
                          shouldValidate: true,
                          shouldDirty: true,
                        });

                        const newMetadata: FileMetadata[] = gcsUrls.map(
                          (file: any) => ({
                            url: file?.file,
                            name: getFileNameFromUrl(file?.file),
                            size: file?.size, // Would need to be passed from FileUpload component
                            uploadDate: new Date(file?.created_at),
                          })
                        );
                        setFileMetadata((prev) => [...prev, ...newMetadata]);
                      }}
                      gcsPathPrefix="employee_knowledge/"
                      onUploadingStateChange={setIsFileUploading}
                      acceptedFileTypes=".pdf,.doc,.docx,.txt,.md"
                      className="hidden"
                    />
                    <Button
                      onClick={() => {
                        // Trigger the FileUpload component
                        track(AnalyticsEvents.ADDED_KNOWLEDGE, {
                          ...data,
                          ...form.getValues(),
                        });
                        const fileInput = document.querySelector(
                          'input[type="file"]'
                        ) as HTMLInputElement;
                        fileInput?.click();
                      }}
                      className="!bg-brand-primary hover:!bg-brand-primary/90 text-white px-6 min-w-[270px]"
                      disabled={isFileUploading}
                    >
                      {isFileUploading ? (
                        <>
                          <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          Uploading...
                        </>
                      ) : (
                        <>
                          <Plus className="w-4 h-4 mr-2" />
                          Upload Files
                        </>
                      )}
                    </Button>
                  </div>

                  {/* Or Text */}
                  {/* <p className="text-gray-500 text-sm ">or</p> */}

                  {/* Action Buttons */}
                  <div className="flex gap-4">
                    {/* <Button
                      variant="outline"
                      className="px-6 text-text-secondary"
                    >
                      <Brain className="w-4 h-4 mr-2" />
                      Add Existing Knowledge
                    </Button> */}
                    {/* <Button
                      variant="outline"
                      className="px-6 text-text-secondary"
                      onClick={() => {
                        setIsImportModalOpen(true);
                        setUrlError(null);
                        setImportUrl("");
                      }}
                    >
                      <Globe className="w-4 h-4 mr-2" />
                      Import website
                    </Button> */}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Import Website Modal */}
        <Dialog
          open={isImportModalOpen}
          onOpenChange={(open) => {
            setIsImportModalOpen(open);
            if (!open) {
              // Clear form and errors when modal closes
              setImportUrl("");
              setUrlError(null);
            }
          }}
        >
          <DialogContent className="md:min-w-[668px] min-w-[300px]">
            <DialogHeader className="flex gap-2">
              <DialogTitle className="text-xl font-semibold">
                Import content from website
              </DialogTitle>
              <p className="text-sm text-gray-600">
                Import data from external sources as knowledge.
              </p>
            </DialogHeader>
            <div className="space-y-4 mt-4">
              <div>
                <label className="text-sm font-satoshi-bold block mb-2">
                  Website URL
                </label>
                <Input
                  placeholder="https://ruh.ai"
                  value={importUrl}
                  onChange={(e) => {
                    setImportUrl(e.target.value);
                    // Clear error when user starts typing
                    if (urlError) {
                      setUrlError(null);
                    }
                  }}
                  className={`w-full text-sm ${
                    urlError ? "border-error focus:border-error" : ""
                  }`}
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      handleImportWebsite();
                    }
                  }}
                />
                {urlError && (
                  <p className="text-sm text-error mt-1">{urlError}</p>
                )}
              </div>
              <div className="flex justify-end gap-3 pt-4">
                <Button
                  variant="tertiary"
                  className="min-w-[142px]"
                  onClick={() => {
                    setIsImportModalOpen(false);
                    setImportUrl("");
                    setUrlError(null);
                  }}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  className="min-w-[142px]"
                  onClick={handleImportWebsite}
                  disabled={!importUrl.trim()}
                >
                  Import
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {!props.isEdit && isPublishModalOpen && (
          <PublishAgentModal
            open={isPublishModalOpen}
            onOpenChange={closePublishModal}
            onPublish={handleModalPublish}
            onDiscard={handleModalDiscard}
          />
        )}
      </div>
    );
  }
);

export { CreateEmployeeKnowledgeFormPage };
