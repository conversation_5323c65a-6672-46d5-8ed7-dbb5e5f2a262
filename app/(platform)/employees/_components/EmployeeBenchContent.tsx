"use client";
import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import Image from "next/image";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import { publish_employee } from "@/shared/constants";
import { agentRoute } from "@/shared/routes";
import {
  LoaderIcon,
  MoreHorizontal,
  PencilIcon,
  UserRoundPlusIcon,
} from "lucide-react";
import { AgentBase } from "@/shared/interfaces";
import { EmployeeAvatar } from "@/components/shared/EmployeeAvatar";
import { agentApi } from "@/app/api/agent";
import { useRouter } from "next/navigation";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@radix-ui/react-dropdown-menu";
import { But<PERSON> } from "@/components/ui/button";
import { communicationApi } from "@/app/api/communication";
import { TaskStatus } from "@/shared/enums";
import MdcIcon from "@/public/assets/Icons-components/MdcIcon";
import { useQueryClient } from "@tanstack/react-query";

interface EmployeeBenchContentProps {
  employees: AgentBase[];
  onEmployeesChange?: () => void; // Callback to notify parent component of changes
  searchQuery: string;
  setIsInitialLoading: (isInitialLoading: boolean) => void;
}

export const EmployeeBenchContent = ({
  employees: initialEmployees,
  searchQuery,
  setIsInitialLoading,
}: EmployeeBenchContentProps) => {
  // Local state for employees
  const [employees, setEmployees] = useState(initialEmployees);
  const [loading, setLoading] = useState(false);

  const router = useRouter();
  const queryClient = useQueryClient();

  // Update local state when prop changes
  useEffect(() => {
    setEmployees(initialEmployees);
  }, [initialEmployees]);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<AgentBase | null>(
    null
  );

  const handlePublishClick = (e: React.MouseEvent, employee: AgentBase) => {
    e.preventDefault();
    e.stopPropagation();
    setSelectedEmployee(employee);
    setIsDialogOpen(true);
  };

  const handlePublishEmployee = async () => {
    if (selectedEmployee) {
      setLoading(true);
      const response = await agentApi.updateAgentSettings(selectedEmployee.id, {
        is_bench_employee: false,
      });

      if (response.success) {
        setIsInitialLoading(true);
        queryClient.invalidateQueries({ queryKey: ["agents"] });
        queryClient.invalidateQueries({ queryKey: ["benched"] });
        setIsDialogOpen(false);
        router.push(agentRoute);
      }
      setLoading(false);
    }
  };

  const getAgentTasksData = (employee: AgentBase) => [
    {
      name: "Total Tasks",
      value: employee.task_counts?.total || 0,
    },
    {
      name: "In progress",
      value: employee.task_counts?.inProgress || 0,
    },
    {
      name: "Completed",
      value: employee.task_counts?.completed || 0,
    },
  ];

  return (
    <div className="w-full">
      {/* Employee cards */}
      <div className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-2">
        {employees.map((employee, index) => (
          <div
            key={index}
            className="bg-gray-100 hover:bg-background-accent p-2 rounded-lg"
          >
            <Card className="p-[14px] flex flex-col items-center relative gap-4 cursor-pointer rounded-sm border-brand-input min-h-[141px] h-full">
              <div className="flex flex-row gap-4 w-full">
                <div className="hover:bg-brand-clicked hover:text-brand-primary p-2 rounded-lg flex flex-col items-center justify-start gap-2 bg-brand-clicked text-brand-primary">
                  <EmployeeAvatar
                    src={employee.avatar}
                    name={employee.name}
                    className="w-14 h-14 rounded-sm"
                  />
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger
                    className="absolute right-0 top-0"
                    asChild
                  >
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    align="end"
                    className="flex absolute top-0 right-0 flex-col rounded-[6px] z-[1000] gap-1 bg-brand-card border border-border-muted w-[200px] mr-2"
                  >
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(`/employees/${employee.id}`);
                      }}
                      className="flex flex-row items-center py-[6px] px-3 gap-2 hover:bg-gray-100"
                    >
                      <PencilIcon className="w-4 h-4" />
                      <DropdownMenuItem className="cursor-pointer font-satoshi-regular text-[14px] hover:border-0">
                        Edit Employee
                      </DropdownMenuItem>
                    </div>
                    <div
                      onClick={(e) => handlePublishClick(e, employee)}
                      className="flex flex-row py-[6px] px-3 items-center gap-2 hover:bg-gray-100"
                    >
                      <UserRoundPlusIcon className="w-4 h-4" />
                      <DropdownMenuItem className="cursor-pointer font-satoshi-regular text-[14px] hover:border-0">
                        Publish Employee
                      </DropdownMenuItem>
                    </div>
                  </DropdownMenuContent>
                </DropdownMenu>
                <div>
                  <h1 className="text-[16px] break-all line-clamp-1">
                    {employee.name}
                  </h1>
                  <p className="text-[12px] font-satoshi-regular text-wrap text-text-secondary line-clamp-1 break-all">
                    {employee.description}
                  </p>
                </div>
              </div>
              <div className="flex flex-row gap-2">
                {getAgentTasksData(employee)?.map((item, index) => (
                  <div
                    key={index}
                    className="p-[6px] rounded-[8px] bg-color-light-secondary flex flex-col justify-center items-center"
                  >
                    <p className="text-[12px] text-wrap">{item.value}</p>
                    <p className="text-[10px] font-satoshi-regular">
                      {item.name}
                    </p>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        ))}
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="flex flex-col items-center justify-center gap-8 w-full p-6 overflow-hidden bg-background rounded-lg min-w-[300px] lg:min-w-[950px]">
          <DialogHeader className="flex flex-row items-center justify-center gap-2 text-center">
            <DialogTitle></DialogTitle>
            <span className="text-card-foreground font-sans text-lg font-semibold leading-7 text-center font-primary">
              Are you sure you want to publish this employee,{" "}
              {selectedEmployee?.name} the {selectedEmployee?.agent_topic_type},
              to your workforce?
            </span>
          </DialogHeader>
          <div className="mx-auto flex flex-col gap-8">
            {/* <div className="flex items-center justify-center">
              {selectedEmployee && <EmployeeDescriptionCard {...selectedEmployee} />}
            </div> */}
            <Image
              src={publish_employee}
              alt="Unpublish Employee UI"
              width={180}
              height={180}
              className="rounded-sm"
            />
            <div className="flex flex-col md:flex-row w-full flex-1 gap-2">
              <SecondaryButton
                className="flex-1"
                onClick={() => setIsDialogOpen(false)}
              >
                Cancel
              </SecondaryButton>
              <PrimaryButton
                className="flex-1"
                onClick={handlePublishEmployee}
                disabled={loading}
              >
                {loading ? <LoaderIcon className="animate-spin" /> : "Yes"}
              </PrimaryButton>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {employees.length === 0 && searchQuery === "" && (
        <div className="max-w-3xl mx-auto py-14 flex flex-col gap-10 w-full justify-center items-center">
          <MdcIcon />
          <h2 className="text-brand-primary-font text-xl font-semibold text-center leading-[140%]">
            Welcome to the Employee Bench, where your AI employees are sent when
            they have no work (i.e., when you unpublish them!)
          </h2>
          {/* <Image
            src={emptyBenchContainer}
            alt="Empty Bench Container"
            width={250}
            height={250}
          /> */}
        </div>
      )}
    </div>
  );
};
