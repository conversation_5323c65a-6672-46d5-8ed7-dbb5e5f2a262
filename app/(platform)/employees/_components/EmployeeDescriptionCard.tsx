"use client";

import { Card } from "@/components/ui/card";
import { AgentBase } from "@/shared/interfaces";
import { EmployeeAvatar } from "@/components/shared/EmployeeAvatar";
import { useRouter } from "next/navigation";
import { employeeChatRoute } from "@/shared/routes";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@radix-ui/react-dropdown-menu";
import { Button } from "@/components/ui/button";
import {
  ArchiveX,
  MoreHorizontal,
  PencilIcon,
  Plus,
  PlusIcon,
  LoaderIcon,
  UserRoundMinusIcon,
} from "lucide-react";
import { useState } from "react";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import { LOCAL_STORAGE_KEYS, unpublish_employee } from "@/shared/constants";
import { agent<PERSON><PERSON> } from "@/app/api/agent";
import { resetEmployeeCreateState } from "@/lib/utils";
import { useQueryClient } from "@tanstack/react-query";
import { Employee } from "@/shared/enums";

interface EmployeeDescriptionCardProps extends AgentBase {
  cardFor?: string;
  setPage: (page: number) => void;
  setIsInitialLoading: (isInitialLoading: boolean) => void;
}

export const EmployeeDescriptionCard = ({
  cardFor,
  setPage,
  setIsInitialLoading,
  ...employee
}: EmployeeDescriptionCardProps) => {
  const router = useRouter();
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const queryClient = useQueryClient();

  const handleEmployeeClick = (id: string) => {
    // Store employee in localStorage cache
    const cacheKey = LOCAL_STORAGE_KEYS.AGENT_DETAILS_CACHE;
    const cache = JSON.parse(localStorage.getItem(cacheKey) || "{}");
    cache[id] = employee;
    localStorage.setItem(cacheKey, JSON.stringify(cache));
    localStorage.setItem(LOCAL_STORAGE_KEYS.IS_NEW_CHAT, "true");
    router.push(`${employeeChatRoute}/${id}`);
  };

  const handleUnpublishEmployee = async () => {
    if (employee.id) {
      setLoading(true);
      const response = await agentApi.updateAgentSettings(employee.id, {
        is_bench_employee: true,
      });

      if (response.success) {
        setPage(1);
        setIsInitialLoading(true);
        queryClient.invalidateQueries({ queryKey: ["benched"] });
        queryClient.invalidateQueries({ queryKey: ["agents"] });
        setIsDialogOpen(false);
      }
      setLoading(false);
    }
  };

  const handleUnpublishClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDialogOpen(true);
  };

  const agentTasksData = [
    {
      name: "Total Tasks",
      value: employee.task_counts?.total || 0,
    },
    {
      name: "In progress",
      value: employee.task_counts?.inProgress || 0,
    },
    {
      name: "Completed",
      value: employee.task_counts?.completed || 0,
    },
  ];

  return (
    <div className="bg-gray-100 hover:bg-background-accent p-2 rounded-lg">
      <Card
        className="p-[14px] flex flex-col items-center relative gap-4 cursor-pointer  rounded-sm border-brand-input min-h-[141px] h-full"
        onClick={() => cardFor !== "create" && handleEmployeeClick(employee.id)}
      >
        <div className="flex flex-row gap-4 w-full">
          <div className="hover:bg-brand-clicked hover:text-brand-primary p-2 rounded-lg flex flex-col items-center justify-start gap-2  bg-brand-clicked h-[max-content] text-brand-primary ">
            {cardFor === "create" ? (
              <EmployeeAvatar
                src={"/assets/logos/ruh-logo-no-text.svg"}
                name={"Ruh AI"}
                className="w-14 h-14 m-0 rounded-sm"
              />
            ) : (
              <EmployeeAvatar
                src={employee.avatar}
                name={employee.name}
                className="w-14 h-14 rounded-sm"
              />
            )}
          </div>
          {cardFor !== "create" && (
            <DropdownMenu>
              <DropdownMenuTrigger className="absolute right-0 top-0" asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-gray-400 hover:text-gray-600"
                >
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                align="end"
                className="flex absolute top-0 right-0 flex-col rounded-[6px] z-[1000]  gap-1 bg-brand-card border border-border-muted w-[200px] mr-2"
              >
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    resetEmployeeCreateState();
                    router.push(`/employees/${employee.id}`);
                    localStorage.setItem(
                      LOCAL_STORAGE_KEYS.ORIGIN_FOR_CREATE_AND_EDIT_AGENT,
                      Employee.CARD
                    );
                  }}
                  className="flex flex-row items-center py-[6px] px-3 gap-2 hover:bg-gray-100"
                >
                  <PencilIcon className="w-4 h-4" />
                  <DropdownMenuItem className="cursor-pointer font-satoshi-regular text-[14px] hover:border-0">
                    Edit Employee
                  </DropdownMenuItem>
                </div>
                <div
                  onClick={handleUnpublishClick}
                  className="flex flex-row py-[6px] px-3 items-center gap-2 hover:bg-gray-100"
                >
                  <UserRoundMinusIcon className="w-4 h-4" />
                  <DropdownMenuItem className="cursor-pointer font-satoshi-regular text-[14px] hover:border-0 ">
                    Unpublish Employee
                  </DropdownMenuItem>
                </div>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
          <div>
            <h1 className="text-[16px] break-all line-clamp-1">
              {employee.name}
            </h1>
            <p className="text-[12px] font-satoshi-regular text-wrap text-text-secondary wrap-break-all line-clamp-1">
              {employee.description}
            </p>
          </div>
        </div>
        {cardFor !== "create" && (
          <div className="flex flex-row gap-2 ">
            {agentTasksData?.map((item, index) => (
              <div
                key={index}
                className=" p-[6px] rounded-[8px] bg-color-light-secondary flex flex-col justify-center  items-center"
              >
                <p className="text-[12px] text-wrap ">{item.value}</p>
                <p className="text-[10px] font-satoshi-regular">{item.name}</p>
              </div>
            ))}
          </div>
        )}
        {cardFor === "create" && (
          <Button
            variant="tertiary"
            className="w-full"
            onClick={() => {
              resetEmployeeCreateState();
              router.push(`/employees/create`);
            }}
          >
            <Plus />
            Create Now
          </Button>
        )}
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="flex flex-col space-y-4 items-center w-full p-6 overflow-hidden bg-background rounded-lg sm:max-w-[425px] md:max-w-[950px] rounded-sm">
          <DialogHeader className="flex flex-row items-center justify-center gap-3 text-center">
            <DialogTitle></DialogTitle>
            <span className="text-card-foreground font-sans text-lg font-semibold leading-7 font-primary">
              Are you sure you want to unpublish this employee?
            </span>
          </DialogHeader>
          <div className="w-2/3 mx-auto flex flex-col items-center justify-center gap-4">
            <Image
              src={unpublish_employee}
              alt="Unpublish Employee UI"
              width={180}
              height={180}
              className="rounded-sm"
            />
            <div className="flex flex-col md:flex-row w-full flex-1 gap-2 mt-4">
              <SecondaryButton
                className="flex-1"
                onClick={() => setIsDialogOpen(false)}
              >
                Cancel
              </SecondaryButton>
              <PrimaryButton
                className="flex-1"
                onClick={handleUnpublishEmployee}
                disabled={loading}
              >
                {loading ? (
                  <LoaderIcon className="animate-spin" />
                ) : (
                  "Unpublish"
                )}
              </PrimaryButton>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
