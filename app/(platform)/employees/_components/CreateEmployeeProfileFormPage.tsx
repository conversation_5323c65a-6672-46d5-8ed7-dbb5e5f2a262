"use client";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { createEmployeeProfileSchema } from "@/lib/schemas/createEmployee";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Textarea } from "@/components/ui/textarea";

import {
  useEffect,
  useImperativeHandle,
  forwardRef,
  useRef,
  useState,
} from "react";
import { toast } from "sonner";
import { EmployeeAvatarSelector } from "../../_components/EmployeeAvatarSelector";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { AgentCoreDetailsUpdatePayload } from "@/shared/interfaces";
import { agentA<PERSON> } from "@/app/api/agent";
import { useEmployeeEditStore } from "@/hooks/useEmployeeEditStore";
import { LoadingSpinner } from "@/components/shared/LoadingSpinner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { sanitizeString } from "@/services/helper";
import { Employee, EmployeeDepartment, EmployeeTone } from "@/shared/enums";
import { Button } from "@/components/ui/button";
import { PublishAgentModal } from "@/components/modals/PublishAgentModal";
import { useRouter } from "next/navigation";
import {
  agentRoute,
  dashboardRoute,
  employeeChatRoute,
  employeesRoute,
  redirectionAfterCreateEmployee,
} from "@/shared/routes";
import { InfoIcon, Maximize2, Pencil, Sparkles } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { generateEmployeeInstructions } from "@/app/api/llmActions";
import {
  Tooltip,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import * as TooltipPrimitive from "@radix-ui/react-tooltip";
import { useOrgStore } from "@/hooks/use-organization";
import { getAgentEditPayload } from "@/lib/utils";
import { LOCAL_STORAGE_KEYS } from "@/shared/constants";

const pickedSchema = createEmployeeProfileSchema.pick({
  name: true,
  description: true,
  system_message: true,
  avatar: true,
  tone: true,
  department: true,
  agent_topic_type: true,
});

// Apply superRefine for conditional validation
const createProfileSchema = pickedSchema.superRefine((data, ctx) => {
  if (!data.name) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ["name"],
      message: "Name is required",
    });
  }
  // if (!data.agent_topic_type) {
  //   ctx.addIssue({
  //     code: z.ZodIssueCode.custom,
  //     path: ["agent_topic_type"],
  //     message: "Role is required",
  //   });
  // }
  if (!data.description) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ["description"],
      message: "Description is required.",
    });
  }
  if (!data.department) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ["department"],
      message: "Department is required.",
    });
  }
  if (!data.system_message) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ["system_message"],
      message: "Instruction is required.",
    });
  } else if (data.system_message.length < 10) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      path: ["system_message"],
      message: "Instruction must be at least 10 characters.",
    });
  }
});

// Export validation function for use in other components
export const validateProfileRequiredFields = (data: any) => {
  const errors: { field: string; message: string }[] = [];

  if (!data.name || !data.name.trim()) {
    errors.push({ field: "name", message: "Name is required" });
  }

  if (!data.description || !data.description.trim()) {
    errors.push({ field: "description", message: "Description is required." });
  }
  if (!data.department || !data.department.trim()) {
    errors.push({ field: "department", message: "Department is required." });
  }

  if (!data.system_message || !data.system_message.trim()) {
    errors.push({
      field: "system_message",
      message: "Instruction is required.",
    });
  } else if (data.system_message.length < 10) {
    errors.push({
      field: "system_message",
      message: "Instruction must be at least 10 characters.",
    });
  }

  return errors;
};

type CreateProfileSchema = z.infer<typeof createProfileSchema>;

interface CreateEmployeeProfileFormPageProps {
  agentData?: {
    id: string;
    name?: string;
    description?: string;
    system_message?: string;
    avatar?: string;
    tone?: EmployeeTone;
    department?: any;
    agent_topic_type?: string;
  };
  isEdit?: boolean;
  isLoading?: boolean;
  onSubmitAgent: (formData: any, provider?: string, model?: string) => void;
  isCreatingAgent?: boolean;
  isUpdatingAgent?: boolean;
}

const CreateEmployeeProfileFormPage = forwardRef(
  (
    {
      onSubmitAgent,
      isCreatingAgent,
      isUpdatingAgent,
      ...props
    }: CreateEmployeeProfileFormPageProps,
    ref
  ) => {
    const {
      data,
      setData,
      checkRequiredField,
      formStep,
      setCheckRequiredField,
      setFormStep,
      isPublishModalOpen,
      openPublishModal,
      closePublishModal,
      pendingSaveStep,
      setPendingSaveStep,
      originForCreateAndEdit,
      setOriginForCreateAndEdit,
    } = useEmployeeCreateStore();

    const { closeModal } = useEmployeeEditStore();
    const queryClient = useQueryClient();
    const router = useRouter();
    const { currentOrganization } = useOrgStore();
    // State for the maximize modal
    const [isMaximizeModalOpen, setIsMaximizeModalOpen] = useState(false);
    const [tempSystemMessage, setTempSystemMessage] = useState("");
    const [isGeneratingInstruction, setIsGeneratingInstruction] =
      useState(false);
    const [showEditSaveChangesModal, setShowEditSaveChangesModal] =
      useState<boolean>(false);
    let { data: departments, isFetching: isLoadingDepartments } = useQuery<any>(
      {
        queryKey: ["departments"],
        queryFn: () => agentApi.getDepartments(),
        staleTime: 30 * 60 * 1000,
      }
    );

    const form = useForm<CreateProfileSchema>({
      resolver: zodResolver(createProfileSchema),
      mode: "onChange",
      defaultValues: {
        tone: data.tone,
        name: data.name || "Untitled Agent",
        description: data.description || "",
        system_message: data.system_message || "",
        avatar: data.avatar,
        agent_topic_type: data.agent_topic_type,
        department:
          data.department ||
          departments?.filter((item: any) => item?.name === "GENERAL")[0]?.id ||
          "",
      },
    });

    const openMaximizeModal = () => {
      setTempSystemMessage(form.getValues("system_message") || "");
      setIsMaximizeModalOpen(true);
    };

    const closeMaximizeModal = () => {
      setIsMaximizeModalOpen(false);
    };

    const saveSystemMessage = () => {
      form.setValue("system_message", tempSystemMessage);
      closeMaximizeModal();
    };

    useEffect(() => {
      if (formStep === 1 && checkRequiredField) {
        // Small delay to ensure form is fully initialized
        const timer = setTimeout(() => {
          form.trigger();
          // setCheckRequiredField(false)
        }, 100);
        return () => clearTimeout(timer);
      }
    }, [formStep]);

    useImperativeHandle(ref, () => ({
      saveToStore: () => {
        setData(form.getValues());
      },
    }));

    // Reset form values when agentData becomes available
    useEffect(() => {
      if (props.isEdit && data) {
        const {
          name = "",
          description = "",
          system_message = "",
          avatar = "",
          tone,
          agent_topic_type = undefined,
          department = undefined,
        } = data || {};
        // Add a small delay to ensure form is ready before setting values
        const timer = setTimeout(() => {
          form.reset({
            name,
            description,
            system_message,
            avatar,
            tone,
            agent_topic_type,
            department,
          });
        }, 100);
        return () => clearTimeout(timer);
      }
    }, [props.isEdit, data, form]);

    //retriving data from localstorage
    useEffect(() => {
      const savedFormData = localStorage.getItem("employeeToolsFormData");

      if (savedFormData) {
        let parsedData = JSON.parse(savedFormData);
        setData(parsedData);
        setFormStep(parsedData.formStep);
        if (props.isEdit) {
          localStorage.setItem(
            "addedTools",
            JSON.stringify(parsedData.mcp_server_ids)
          );
        }
        setOriginForCreateAndEdit(parsedData.originForCreateAndEdit);
        form.reset({
          name: parsedData.name || "",
          description: parsedData.description || "",
          system_message: parsedData.system_message || "",
          avatar: parsedData.avatar,
          tone: parsedData.tone,
          agent_topic_type: parsedData?.agent_topic_type,
          department: parsedData?.department,
        });
        localStorage.removeItem("employeeToolsFormData");
      }
    }, []);

    useEffect(() => {
      if (pendingSaveStep === 1) {
        if (props?.isEdit) {
          let diff = getAgentEditPayload(data || {}, form.getValues());
          if (Object.keys(diff)?.length) {
            setShowEditSaveChangesModal(true);
          } else {
            setPendingSaveStep(null);
          }
        } else {
          setData(form.getValues());
          setPendingSaveStep(null);
        }
      }
    }, [pendingSaveStep, setData, setPendingSaveStep, form]);

    useEffect(() => {
      if (!isUpdatingAgent) {
        setPendingSaveStep(null);
      }
    }, [isUpdatingAgent]);

    useEffect(() => {
      if (departments?.length) {
        if (data.department?.length) {
          form.setValue("department", data.department);
        } else {
          const generalDepartmentData = departments?.filter(
            (item: any) => item?.name === "GENERAL"
          );
          if (generalDepartmentData?.length) {
            form.reset({
              ...data,
              ...form.getValues(),
              department: generalDepartmentData?.[0]?.id,
            });
            return;
          }
          form.reset({
            ...data,
            ...form.getValues(),
            department: departments?.[0]?.id,
          });
        }
      }
    }, [departments, data]);

    const onSubmit = (formData: any, provider?: string, model?: string) => {
      onSubmitAgent({ ...data, ...formData }, provider, model);
    };

    // Handle button click with validation
    const handlePublishClick = async () => {
      const isValid = await form.trigger();
      if (!isValid) {
        // Get all form errors and show them as toast notifications
        const errors = form.formState.errors;
        Object.values(errors).forEach((error) => {
          if (error?.message) {
            toast.error(error.message, {
              duration: 4000,
              position: "top-center",
            });
          }
        });
        return;
      }

      // Save form data to store first
      setData(form.getValues());

      if (props.isEdit) {
        // In edit mode, directly call the API without modal
        onSubmit(form.getValues());
      } else {
        // In add mode, open the modal
        openPublishModal();
      }
    };

    const handleModalPublish = (provider: string, model: string) => {
      closePublishModal();
      onSubmit(form.getValues(), provider, model);
    };

    const handleModalDiscard = () => {
      closePublishModal();
    };
    // Handler for Generate/Enhance instructions
    const handleGenerateInstructions = async () => {
      const description = form.getValues("description");
      const currentInstruction = form.getValues("system_message");
      if (!description || !description.trim()) {
        toast.error(
          "Please enter a description before generating instructions.",
          { position: "top-center" }
        );
        return;
      }
      setIsGeneratingInstruction(true);
      try {
        const improved = await generateEmployeeInstructions(
          description,
          currentInstruction && currentInstruction.length > 0
            ? currentInstruction
            : undefined
        );
        form.setValue("system_message", improved, { shouldDirty: true });
        toast.success("Instructions generated successfully!", {
          position: "top-center",
        });
      } catch (err: any) {
        toast.error(err.message || "Failed to generate instructions", {
          position: "top-center",
        });
      } finally {
        setIsGeneratingInstruction(false);
      }
    };

    if (props.isLoading) {
      return <LoadingSpinner message="Loading profile data..." />;
    }

    return (
      <div className="flex flex-col h-full p-[24px] gap-[17px]">
        {/* Top Header with Publish Button */}
        <div className="flex md:justify-end md:items-center justify-start mt-[50px] md:mt-0">
          <Button
            variant="outline"
            className="px-6 w-auto mr-2"
            onClick={() => {
              if (originForCreateAndEdit == Employee.CARD) {
                router.push(employeesRoute);
              } else {
                if (props?.isEdit) {
                  // Store agentData in localStorage cache
                  const cacheKey = LOCAL_STORAGE_KEYS.AGENT_DETAILS_CACHE;
                  const cache = JSON.parse(
                    localStorage.getItem(cacheKey) || "{}"
                  );
                  if (props?.agentData?.id) {
                    cache[props.agentData.id] = props.agentData;
                    localStorage.setItem(cacheKey, JSON.stringify(cache));
                  }
                  localStorage.setItem(LOCAL_STORAGE_KEYS.IS_NEW_CHAT, "true");
                  router.push(`${employeeChatRoute}/${props?.agentData?.id}`);
                } else {
                  router.push(dashboardRoute);
                }
              }
            }}
          >
            Close
          </Button>
          <Button
            variant="primary"
            className="px-6 w-auto"
            onClick={handlePublishClick}
            disabled={isCreatingAgent || isUpdatingAgent}
          >
            {isCreatingAgent || isUpdatingAgent ? (
              <>
                <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                {props?.isEdit ? "Saving..." : "Publishing..."}
              </>
            ) : props?.isEdit ? (
              "Save Changes"
            ) : (
              "Publish Changes"
            )}
          </Button>
        </div>
        {!props.isEdit && isPublishModalOpen && (
          <PublishAgentModal
            open={isPublishModalOpen}
            onOpenChange={closePublishModal}
            onPublish={(provider: string, model: string) =>
              handleModalPublish(provider, model)
            }
            onDiscard={handleModalDiscard}
          />
        )}
        <div className="flex-1 overflow-y-auto">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit((data) => onSubmit(data))}
              className="flex flex-col gap-6 max-w-4xl min-w-full"
            >
              {/* White Card Container with Avatar, Basic Info, and Instructions */}
              <div className="bg-white rounded-lg border border-gray-200 p-2 md:p-8 shadow-sm gap-[14px]">
                <div className="flex flex-col lg:flex-row items-start mb-6 gap-2">
                  {/* Avatar Section */}
                  <div className="flex-shrink-0">
                    <div className="relative group [&_p]:hidden [&_button]:border-0 [&_button]:!w-[72px] [&_button]:!h-[72px] [&_button]:!min-w-[72px] [&_button]:!min-h-[72px] [&_button]:rounded-md [&_img]:rounded-md flex flex-col items-center justify-center">
                      <EmployeeAvatarSelector
                        avatar={form.watch("avatar")}
                        setAvatar={(avatar) =>
                          form.setValue("avatar", avatar, { shouldDirty: true })
                        }
                      />
                      <span
                        className="text-primary text-xs items-center cursor-pointer"
                        onClick={() => {
                          // Trigger the avatar selector button click
                          const avatarButton = document.querySelector(
                            '[data-testid="avatar-selector-button"]'
                          ) as HTMLButtonElement;
                          if (avatarButton) {
                            avatarButton.click();
                          } else {
                            // Fallback: find any button within the avatar selector
                            const fallbackButton = document.querySelector(
                              ".group button"
                            ) as HTMLButtonElement;
                            if (fallbackButton) {
                              fallbackButton.click();
                            }
                          }
                        }}
                      >
                        Change Avatar
                      </span>
                    </div>
                  </div>

                  {/* Agent Title and Description */}
                  <div className="grid grid-cols-2 gap-2 justify-center items-center w-full">
                    <FormField
                      control={form.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem className="gap-1">
                          <FormLabel className="text-sm font-satoshi-bold block data-[error=true]:text-gray-700">
                            Name
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              className="text-sm"
                              placeholder="Untitled Agent..."
                              required
                            />
                          </FormControl>
                          <div className="min-h-[20px]">
                            <FormMessage />
                          </div>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="agent_topic_type"
                      render={({ field }) => (
                        <FormItem className="gap-1">
                          <FormLabel className="text-sm font-satoshi-bold block data-[error=true]:text-gray-700">
                            Role
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              className="text-sm"
                              placeholder="Enter role"
                            />
                          </FormControl>
                          <div className="min-h-[20px]">
                            <FormMessage />
                          </div>
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className=" flex flex-col gap-[14px] mb-[14px]">
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-satoshi-bold block data-[error=true]:text-gray-700">
                          Description
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            className={`resize-none h-[106px] p-[20px] bg-transparent placeholder:text-text-placeholder aria-invalid:border-error aria-invalid:ring-0`}
                            placeholder="Enter your basic, natural language description for what you want the AI employee to do"
                            required
                            aria-invalid={!!form.formState.errors.description}
                          />
                        </FormControl>
                        <FormMessage className="text-error" />
                      </FormItem>
                    )}
                  />

                  {/* Instructions Section - Now inside the white card */}
                  <FormField
                    control={form.control}
                    name="system_message"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-2 justify-between">
                          <FormLabel className="text-sm font-satoshi-bold text-gray-700 block data-[error=true]:text-gray-700">
                            Instructions
                          </FormLabel>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="flex items-center gap-2"
                            onClick={handleGenerateInstructions}
                            disabled={
                              isGeneratingInstruction ||
                              !form.watch("description") ||
                              form.watch("description").trim().split(" ")
                                .length < 5
                            }
                          >
                            <Sparkles className="h-4 w-4" />
                            <span className="text-sm">
                              {form.watch("system_message") &&
                              form.watch("system_message").length > 0
                                ? isGeneratingInstruction
                                  ? "Enhancing..."
                                  : "Enhance instructions"
                                : isGeneratingInstruction
                                  ? "Generating..."
                                  : "Generate instructions"}
                            </span>
                          </Button>
                        </div>
                        <FormControl>
                          <div className="relative">
                            <Textarea
                              {...field}
                              className="resize-none h-[194px] p-[20px] bg-transparent placeholder:text-text-placeholder aria-invalid:border-error aria-invalid:ring-0"
                              placeholder="Enter step-by-step, detailed instructions for what you want your employee to do - it will execute everything clearly."
                              required
                              aria-invalid={
                                !!form.formState.errors.system_message
                              }
                            />
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute bottom-2 right-2 h-8 w-8 text-[var(--text-secondary)] hover:text-[var(--primary)] hover:bg-[var(--color-light)] transition-colors"
                              onClick={openMaximizeModal}
                            >
                              <Maximize2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </FormControl>
                        <FormMessage className="text-error" />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-2 gap-2 justify-center items-center">
                  <FormField
                    control={form.control}
                    name="department"
                    render={({ field }) => (
                      <FormItem className="flex flex-col gap-2">
                        <FormLabel className="text-sm font-satoshi-bold data-[error=true]:text-gray-900">
                          Department
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <span tabIndex={0}>
                                  <InfoIcon className="w-4 h-4 cursor-pointer text-text-placeholder" />
                                </span>
                              </TooltipTrigger>
                              <TooltipPrimitive.Portal>
                                <TooltipPrimitive.Content
                                  data-slot="tooltip-content"
                                  sideOffset={8}
                                  className={
                                    "border border-brand-stroke animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance bg-black text-white max-w-[310px] p-3"
                                  }
                                  side="right"
                                >
                                  <span className="absolute left-0 rotate-270 -translate-x-1/2 top-1/2">
                                    <svg
                                      width="20"
                                      height="10"
                                      viewBox="0 0 20 10"
                                      className="text-black"
                                      fill="currentColor"
                                      xmlns="http://www.w3.org/2000/svg"
                                    >
                                      <polygon points="10,0 20,10 0,10" />
                                    </svg>
                                  </span>
                                  {currentOrganization?.isAdmin ? (
                                    <p>
                                      These are all the existing departments, to
                                      create a new one
                                      <span
                                        className="text-blue-500 cursor-pointer underline ml-1"
                                        onClick={() => {
                                          router.push(
                                            "/admin-settings/create-department"
                                          );
                                        }}
                                      >
                                        click here
                                      </span>
                                    </p>
                                  ) : (
                                    <span>
                                      Departments are managed by the admin.
                                      Contact your administrator to add a new
                                      one.
                                    </span>
                                  )}
                                </TooltipPrimitive.Content>
                              </TooltipPrimitive.Portal>
                            </Tooltip>
                          </TooltipProvider>
                        </FormLabel>
                        <FormControl>
                          <Select
                            {...field}
                            onValueChange={field.onChange}
                            disabled={isLoadingDepartments}
                            value={form.watch("department") || ""}
                          >
                            <SelectTrigger className="w-full text-sm aria-invalid:border-gray-200 aria-invalid:ring-0">
                              {isLoadingDepartments ? (
                                <span className="flex items-center">
                                  Loading...
                                </span>
                              ) : (
                                <SelectValue placeholder="Select a department" />
                              )}
                            </SelectTrigger>
                            <SelectContent>
                              {departments?.length > 0 ? (
                                departments.map((item: any) => (
                                  <SelectItem key={item.id} value={item.id}>
                                    {item.name}
                                  </SelectItem>
                                ))
                              ) : (
                                <p className="text-sm text-gray-500">
                                  No departments found
                                </p>
                              )}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <div className="min-h-[20px]">
                          {!isLoadingDepartments && <FormMessage />}
                        </div>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="tone"
                    render={({ field }) => (
                      <FormItem className="w-full">
                        <FormLabel className="font-satoshi-bold text-sm data-[error=true]:text-gray-900">
                          Tone
                        </FormLabel>
                        <FormControl>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <SelectTrigger className="text-sm aria-invalid:border-gray-200 aria-invalid:ring-0">
                              <SelectValue placeholder="Select the conversation tone of the employee" />
                            </SelectTrigger>
                            <SelectContent>
                              {Object.values(EmployeeTone).map((tone) => (
                                <SelectItem key={tone} value={tone}>
                                  {sanitizeString(tone)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <div className="min-h-[20px]">
                          <FormMessage />
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </form>
          </Form>
        </div>
        {/* Save edit changes modal */}

        <Dialog
          open={showEditSaveChangesModal}
        >
          <DialogContent className="max-w-4xl max-h-[90vh] bg-[var(--card-color)] border border-[var(--border-default)] [&>button]:hidden">
            <DialogHeader>
              <DialogTitle className="text-[var(--text-primary)] font-satoshi-bold">
                You have unsaved changes
              </DialogTitle>
            </DialogHeader>
            
            <div className="py-4 space-y-4">
              <div className="flex items-start space-x-4">
                <div className="p-2 rounded-full bg-amber-100">
                  <InfoIcon className="h-6 w-6 text-amber-600" />
                </div>
                <div className="space-y-2">
                  <p className="text-[var(--text-primary)]">
                    You've made changes to this employee profile that haven't been saved yet.
                  </p>
                  <p className="text-[var(--text-secondary)] text-sm">
                    If you save these changes, the employee profile will be updated with your modifications. 
                    If you cancel, your changes will be discarded and the profile will remain unchanged.
                  </p>
                </div>
              </div>
            </div>

            <DialogFooter className="gap-2">
              <Button
                type="button"
                variant="secondary"
                onClick={() => {
                  setPendingSaveStep(null);
                }}
              >
                Cancel
              </Button>
              <Button
                type="button"
                variant="primary"
                onClick={() => {
                  setShowEditSaveChangesModal(false);
                  handlePublishClick();
                }}
              >
                Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Maximize Modal for Instructions */}
        <Dialog
          open={isMaximizeModalOpen}
          onOpenChange={setIsMaximizeModalOpen}
        >
          <DialogContent className="max-w-4xl max-h-[90vh] bg-[var(--card-color)] border border-[var(--border-default)]">
            <DialogHeader>
              <DialogTitle className="text-[var(--text-primary)] font-satoshi-bold">
                Edit Instructions
              </DialogTitle>
            </DialogHeader>

            <div className="flex-1 overflow-hidden">
              <Textarea
                value={tempSystemMessage}
                onChange={(e) => setTempSystemMessage(e.target.value)}
                className="min-h-[400px] max-h-[400px] resize-none bg-transparent border border-[var(--border-default)] outline-none text-[var(--text-primary)] placeholder:text-[var(--text-placeholder)] p-4 focus-visible:ring-2 focus-visible:ring-[var(--primary)] focus-visible:ring-offset-2 focus-visible:border-[var(--primary)] transition-all"
                placeholder="Based on your description, you will receive a set of AI-generated detailed instructions followed by the employee - you can edit these as per your own customization"
              />
            </div>

            <DialogFooter className="gap-2">
              <Button
                type="button"
                variant="secondary"
                onClick={closeMaximizeModal}
              >
                Cancel
              </Button>
              <Button
                type="button"
                variant="primary"
                onClick={saveSystemMessage}
              >
                Save Changes
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    );
  }
);

export { CreateEmployeeProfileFormPage };
