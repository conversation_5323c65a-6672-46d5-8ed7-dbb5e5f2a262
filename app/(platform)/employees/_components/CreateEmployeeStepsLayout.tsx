"use client";

import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { CreateEmployeeProfileFormPage } from "./CreateEmployeeProfileFormPage";
import { CreateEmployeeKnowledgeFormPage } from "./CreateEmployeeKnowledgeFormPage";
import { CreateEmployeeToolsFormPage } from "./CreateEmployeeToolsFormPage";
import { CreateEmployeeWorkflowFormPage } from "./CreateEmployeeWorkflowFormPage";
import { CreateEmployeeAdvancedSettingsFormPage } from "./CreateEmployeeAdvancedSettingsFormPage";
import { LoadingSpinner } from "@/components/shared/LoadingSpinner";
import { agentApi } from "@/app/api/agent";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useEmployeeEditStore } from "@/hooks/useEmployeeEditStore";
import { getAgentEditPayload } from "@/lib/utils";
import { Dialog, DialogContent } from "@/components/ui/dialog";

import React, { useCallback, useEffect, useState } from "react";
import { LOCAL_STORAGE_KEYS } from "@/shared/constants";
import { markTourCompleted } from "@/lib/utils/tourUtils";
import { createEmployeeTourConfig } from "../../../../lib/utils/tourConfig";
import { useTourContext } from "@/lib/providers/TourProvider";
import { isTourCompleted } from "@/lib/utils/tourUtils";
import { useMixpanelTrack } from "@/hooks/useMixPanelTrack";
import { AnalyticsEvents } from "@/shared/enums";
import { employeeChatRoute } from "@/shared/routes";

interface CreateEmployeeStepsLayoutProps {
  agentData?: {
    id: string;
    name?: string;
    description?: string;
    system_message?: string;
    avatar?: string;
  };
  isEdit?: boolean;
  isLoading?: boolean;
}

export const CreateEmployeeStepsLayout = ({
  agentData,
  isEdit,
  isLoading,
}: CreateEmployeeStepsLayoutProps) => {
  const { formStep, setOriginForCreateAndEdit } = useEmployeeCreateStore();
  const queryClient = useQueryClient();
  const router = useRouter();
  const { closeModal } = useEmployeeEditStore();
  const [openDialog, setOpenDialog] = useState(false);
  const track = useMixpanelTrack();
  // Create mutation
  const { mutateAsync: createAgent, isPending: isCreatingAgent } = useMutation({
    mutationFn: agentApi.createAgent,
    onError: (error) => {
      toast.error(
        `Failed to create agent: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    },
  });

  // Edit mutation
  const { mutate: updateAgent, isPending: isUpdatingAgent } = useMutation({
    mutationFn: ({ id, diff }: { id: string; diff: any }) =>
      agentApi.updateAgentCombined(id, diff),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["agents"] });
      queryClient.invalidateQueries({ queryKey: ["agent", agentData?.id] });
      // useEmployeeCreateStore.getState().reset();
      toast.success("Employee profile updated successfully", {
        position: "top-center",
      });
      // router.back();
      closeModal();
    },
    onError: (error) => {
      toast.error(error.message || "Failed to update employee profile", {
        position: "top-center",
      });
    },
  });

  // Common handler
  const handleSubmitAgent = async (
    formData: any,
    provider?: string,
    model?: string
  ) => {
    if (isEdit) {
      let diff = getAgentEditPayload(agentData || {}, formData);
      if (Object.keys(diff).length > 0) {
        if (diff?.agent_capabilities) {
          diff["capabilities"] = diff?.agent_capabilities?.capabilities;
          delete diff["agent_capabilities"];
          updateAgent({ id: agentData?.id || "", diff });
          return;
        }
        updateAgent({ id: agentData?.id || "", diff });
      } else {
        toast.info("No changes to save.");
      }
    } else {
      try {
        const result: any = await createAgent({
          ...formData,
          model_provider: provider,
          model_name: model,
          files: formData?.files?.map((file: any) => file.file) || [],
        });
        if (result?.success) {
          toast.success("Agent created successfully", {
            position: "top-center",
          });
          track(AnalyticsEvents.CLICKED_PUBLISH_EMPLOYEE, {
            ...formData,
            model_provider: provider,
            model_name: model,
            files: formData?.files?.map((file: any) => file.file) || [],
            result: "success",
          });
          queryClient.invalidateQueries({ queryKey: ["agents"] });
          useEmployeeCreateStore.getState().reset();
          router.push(`${employeeChatRoute}/${result?.agent?.id}`);
          localStorage.setItem(LOCAL_STORAGE_KEYS.IS_NEW_CHAT, "true");
        }
      } catch (error) {
        track(AnalyticsEvents.CLICKED_PUBLISH_EMPLOYEE, {
          ...formData,
          model_provider: provider,
          model_name: model,
          files: formData?.files?.map((file: any) => file.file) || [],
          result: "failed",
          error: error,
        });
        // Error is handled by onError in mutation
        console.error(error);
      }
    }
  };
  const { startTour } = useTourContext();

  useEffect(() => {
    if (
      !isTourCompleted(LOCAL_STORAGE_KEYS.RUH_CREATE_EMPLOYEE_TOUR_COMPLETED)
    ) {
      handleStartTour();
    }
  }, []);

  useEffect(() => {
    let editAgentOrigin = localStorage.getItem(
      LOCAL_STORAGE_KEYS.ORIGIN_FOR_CREATE_AND_EDIT_AGENT
    );
    if (editAgentOrigin) {
      setOriginForCreateAndEdit(editAgentOrigin);
      localStorage.removeItem(
        LOCAL_STORAGE_KEYS.ORIGIN_FOR_CREATE_AND_EDIT_AGENT
      );
    }
  });
  const handleStartTour = useCallback(() => {
    startTour(createEmployeeTourConfig, {
      onComplete: () => {
        markTourCompleted(
          LOCAL_STORAGE_KEYS.RUH_CREATE_EMPLOYEE_TOUR_COMPLETED
        );
      },
      onSkip: () => {
        markTourCompleted(
          LOCAL_STORAGE_KEYS.RUH_CREATE_EMPLOYEE_TOUR_COMPLETED
        );
      },
    });
  }, [startTour]);

  // // Refs for each step
  // const profileRef = useRef<any>(null);
  // const knowledgeRef = useRef<any>(null);
  // const toolsRef = useRef<any>(null);
  // const workflowRef = useRef<any>(null);
  // const advancedRef = useRef<any>(null);

  const renderStepContent = () => {
    if (isLoading) {
      return <LoadingSpinner message="Loading agent data..." size="lg" />;
    }

    switch (formStep) {
      case 1:
        return (
          <CreateEmployeeProfileFormPage
            agentData={agentData}
            isEdit={isEdit}
            isLoading={isLoading}
            onSubmitAgent={handleSubmitAgent}
            isCreatingAgent={isCreatingAgent}
            isUpdatingAgent={isUpdatingAgent}
          />
        );
      case 2:
        return (
          <CreateEmployeeKnowledgeFormPage
            agentData={agentData}
            isEdit={isEdit}
            isLoading={isLoading}
            onSubmitAgent={handleSubmitAgent}
            isCreatingAgent={isCreatingAgent}
            isUpdatingAgent={isUpdatingAgent}
          />
        );
      case 3:
        return (
          <CreateEmployeeToolsFormPage
            agentData={agentData}
            isEdit={isEdit}
            isLoading={isLoading}
            onSubmitAgent={handleSubmitAgent}
            isCreatingAgent={isCreatingAgent}
            isUpdatingAgent={isUpdatingAgent}
          />
        );
      case 4:
        return (
          <CreateEmployeeWorkflowFormPage
            agentData={agentData}
            isEdit={isEdit}
            isLoading={isLoading}
            onSubmitAgent={handleSubmitAgent}
            isCreatingAgent={isCreatingAgent}
            isUpdatingAgent={isUpdatingAgent}
          />
        );
      case 5:
        return (
          <CreateEmployeeAdvancedSettingsFormPage
            agentData={agentData}
            isEdit={isEdit}
            isLoading={isLoading}
            onSubmitAgent={handleSubmitAgent}
            isCreatingAgent={isCreatingAgent}
            isUpdatingAgent={isUpdatingAgent}
          />
        );
      default:
        return (
          <CreateEmployeeProfileFormPage
            agentData={agentData}
            isEdit={isEdit}
            isLoading={isLoading}
            onSubmitAgent={handleSubmitAgent}
            isCreatingAgent={isCreatingAgent}
            isUpdatingAgent={isUpdatingAgent}
          />
        );
    }
  };

  return (
    <div className="flex h-screen bg-brand-background font-primary">
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {/* Content */}
        <div className="flex-1 flex">
          {/* Form Content */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Form Area */}
            <div className="flex-1 overflow-y-auto">
              <div className="max-h-[200px]">{renderStepContent()}</div>
            </div>
          </div>
        </div>
      </div>
      <Dialog open={openDialog} onOpenChange={setOpenDialog}>
        <DialogContent className="min-w-[240px] bg-white border border-border-default rounded-lg p-6"></DialogContent>
      </Dialog>
    </div>
  );
};
