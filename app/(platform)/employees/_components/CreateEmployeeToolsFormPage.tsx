"use client";

import React, {
  useState,
  useEffect,
  useImperative<PERSON>andle,
  forwardRef,
  useCallback,
} from "react";
import ToolCard from "../../_components/ToolCard";
import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { createEmployeeProfileSchema } from "@/lib/schemas/createEmployee";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Button } from "@/components/ui/button";

import { Plus, Package2, Loader2, PackageIcon, Check } from "lucide-react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { mcpApi } from "@/app/api/mcp";
import { apiKeyAndIntegration } from "@/app/api/apiKeysAndIntegration";
import { MCPInDB } from "@/shared/interfaces";
import { sanitizeString } from "@/services/helper";
import Image from "next/image";
import { agentApi } from "@/app/api/agent";
import { useEmployeeEditStore } from "@/hooks/useEmployeeEditStore";
import { toast } from "sonner";
import { LoadingSpinner } from "@/components/shared/LoadingSpinner";
import {
  ItemSelectionDialog,
  SelectableItem,
} from "@/components/shared/ItemSelectionDialog";
import { PublishAgentModal } from "@/components/modals/PublishAgentModal";
import { validateProfileRequiredFields } from "./CreateEmployeeProfileFormPage";
import { useRouter } from "next/navigation";
import {
  agentRoute,
  dashboardRoute,
  employeeChatRoute,
  employeesRoute,
  redirectionAfterCreateEmployee,
} from "@/shared/routes";
import { useUserStore } from "@/hooks/use-user";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { LOCAL_STORAGE_KEYS, marketplaceMcpsUrl } from "@/shared/constants";
import { getAgentEditPayload } from "@/lib/utils";
import { AnalyticsEvents, Employee } from "@/shared/enums";
import { useMixpanelTrack } from "@/hooks/useMixPanelTrack";

const createToolsSchema = createEmployeeProfileSchema.pick({
  mcp_server_ids: true,
});

type CreateToolsSchema = z.infer<typeof createToolsSchema>;

interface Tool extends MCPInDB {
  isAdded: boolean;
  is_connected?: boolean;
  integrations?: string[];
}

interface CreateEmployeeToolsFormPageProps {
  agentData?: any;
  isEdit?: boolean;
  isLoading?: boolean;
  onSubmitAgent: (formData: any, provider?: string, model?: string) => void;
  isCreatingAgent?: boolean;
  isUpdatingAgent?: boolean;
}

const CreateEmployeeToolsFormPage = forwardRef(
  (
    {
      onSubmitAgent,
      isCreatingAgent,
      isUpdatingAgent,
      ...props
    }: CreateEmployeeToolsFormPageProps,
    ref
  ) => {
    const {
      setData,
      setCheckRequiredField,
      formStep,
      setFormStep,
      data,
      isPublishModalOpen,
      openPublishModal,
      closePublishModal,
      pendingSaveStep,
      setPendingSaveStep,
      originForCreateAndEdit,
    } = useEmployeeCreateStore();

    const [tools, setTools] = useState<string[]>([]);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [hasMoreItems, setHasMoreItems] = useState(false);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [addedToolIds, setAddedToolIds] = useState<Set<string>>(
      new Set(data.mcp_server_ids || [])
    );
    const queryClient = useQueryClient();
    const [envKeyValues, setEnvKeyValues] = useState<{
      [toolId: string]: { [key: string]: string };
    }>({});
    const [fetchedToolIds, setFetchedToolIds] = useState<Set<string>>(
      new Set()
    );
    const [originalEnvKeyValues, setOriginalEnvKeyValues] = useState<{
      [toolId: string]: { [key: string]: string };
    }>({});
    const router = useRouter();
    const { user } = useUserStore();
    const [popularToolDialogOpen, setPopularToolDialogOpen] = useState(false);
    const [selectedPopularTool, setSelectedPopularTool] = useState<any | null>(
      null
    );
    const [isPopularToolLoading, setIsPopularToolLoading] = useState(false);
    const [disconnectingTool, setDisconnectingTool] = useState<any>({});
    const [toolConnectionStatus, setToolConnectionStatus] = useState<
      Record<string, boolean>
    >({});
    const [apiKeyConfig, setApiKeyConfig] = useState<{
      [integrationId: string]: any[];
    }>({});
    const [isOauthConnected, setIsOauthConnected] = useState<boolean>(false);
    const track = useMixpanelTrack();

    // Fetch selected tools data directly from API
    const [addedTools, setAddedTools] = useState<any[]>([]);

    // Function to refresh added tools data using getMcpsByIds
    const refreshAddedToolsData = useCallback(() => {
      const toolIds = Array.from(addedToolIds);
      if (toolIds.length > 0) {
        // Fetch the latest data for all added tools
        mcpApi.getMcpsByIds(toolIds).then((response) => {
          if (response.success && response.mcps) {
            // Update the addedTools state with the fresh data
            setAddedTools(response.mcps);

            // Update the local state with the fresh data
            queryClient.setQueryData(
              ["mcps", currentPage, debouncedSearchTerm],
              (oldData: any) => {
                if (!oldData) return oldData;

                // Create a map of updated MCPs by ID for quick lookup
                const updatedMcpsMap = new Map();
                response.mcps.forEach((mcp: any) => {
                  updatedMcpsMap.set(mcp.id, mcp);
                });

                // Update the data with the fresh MCP data
                const updatedData = { ...oldData };
                if (updatedData.data) {
                  updatedData.data = updatedData.data.map((mcp: any) => {
                    if (updatedMcpsMap.has(mcp.id)) {
                      return { ...mcp, ...updatedMcpsMap.get(mcp.id) };
                    }
                    return mcp;
                  });
                }

                return updatedData;
              }
            );
          }
        });
      } else {
        setAddedTools([]);
      }
    }, [
      addedToolIds,
      currentPage,
      debouncedSearchTerm,
      queryClient,
      setAddedTools,
    ]);

    // Initialize local state from store only on mount
    useEffect(() => {
      setAddedToolIds(new Set(data.mcp_server_ids || []));
      // Optionally, also setTools if needed
      setTools(data.mcp_server_ids || []);
      // eslint-disable-next-line
    }, []);

    // Function to fetch selected tools data
    const fetchSelectedToolsData = useCallback(() => {
      const toolIds = Array.from(addedToolIds);
      if (toolIds.length > 0) {
        mcpApi.getMcpsByIds(toolIds).then((response) => {
          if (response.success && response.mcps) {
            setAddedTools(response.mcps);
          }
        });
      } else {
        setAddedTools([]);
      }
    }, [addedToolIds]);

    // Initial fetch of selected tools data when component mounts
    useEffect(() => {
      if (data.mcp_server_ids && data.mcp_server_ids.length > 0) {
        fetchSelectedToolsData();
      }
    }, [fetchSelectedToolsData, data.mcp_server_ids]);

    const { mutate: getMcpsEnvsById, isPending: isGettingMcpsEnvsById } =
      useMutation({
        mutationFn: (integrationId: string) =>
          apiKeyAndIntegration.getApiKeyCredentials(integrationId),
        onSuccess: (response, integrationId) => {
          if (response) {
            // Store credentials if available
            if (response.credentials) {
              setEnvKeyValues((prev) => ({
                ...prev,
                [integrationId]: response.credentials,
              }));
              // Store original values for comparison
              setOriginalEnvKeyValues((prev) => ({
                ...prev,
                [integrationId]: response.credentials,
              }));
            }

            // Store API key configuration
            if (response.api_key_config) {
              setApiKeyConfig((prev) => ({
                ...prev,
                [integrationId]: response.api_key_config,
              }));
            }

            // Mark this integration as fetched
            setFetchedToolIds((prev) => new Set(prev).add(integrationId));

            // Set connection status
            setToolConnectionStatus((prev) => ({
              ...prev,
              [integrationId]: response?.is_connected || false,
            }));
          }
        },
        onError: (error) => {
          console.error("Failed to fetch environment key values:", error);
          toast.error("Failed to fetch environment key values", {
            position: "top-center",
          });
        },
      });

    const { mutate: addMcpEnvKeys, isPending: isAddingEnvKeys } = useMutation({
      mutationFn: ({
        mcpId,
        envKeyValues,
      }: {
        mcpId: string;
        envKeyValues: { key: string; value: string }[];
      }) => {
        // Convert array of key-value pairs to object format required by apiKeyAndIntegration
        const credentials: Record<string, string> = {};
        envKeyValues.forEach(({ key, value }) => {
          credentials[key] = value;
        });
        return apiKeyAndIntegration.addApiKeyCredentials(mcpId, credentials);
      },
      onSuccess: (response, { mcpId }) => {
        toast.success("Environment keys updated successfully", {
          position: "top-center",
        });
        getMcpsEnvsById(mcpId);
        queryClient.invalidateQueries({ queryKey: ["integrations"] });
        queryClient.invalidateQueries({ queryKey: ["connectedKeys"] });

        // Refresh the data for all added tools to ensure accurate connection status
        refreshAddedToolsData();
      },
      onError: (error) => {
        console.error("Failed to update environment keys:", error);
        toast.error("Failed to update environment keys", {
          position: "top-center",
        });
      },
    });

    //update data api
    const { mutate: updateAgentTool, isPending: isUpdatingAgentTool } =
      useMutation({
        mutationFn: ({ data }: { data: any }) =>
          agentApi.updateAgentCombined(props?.agentData?.id, data),
        onSuccess: () => {
          setIsModalOpen(false);
          queryClient.invalidateQueries({ queryKey: ["agents"] });
          queryClient.invalidateQueries({
            queryKey: ["agent", props?.agentData?.id],
          });
          toast.success("Tools updated successfully", {
            position: "top-center",
          });
        },
        onError: (error) => {
          toast.error(error.message || "Failed to update employee profile", {
            position: "top-center",
          });
        },
      });

    const form = useForm<CreateToolsSchema>({
      resolver: zodResolver(createToolsSchema),
      defaultValues: {
        mcp_server_ids: data.mcp_server_ids,
      },
    });

    // Initialize form with agentData when in edit mode
    useEffect(() => {
      if (props.isEdit && data && data.mcp_server_ids) {
        const mcpServerIds = data.mcp_server_ids || [];

        // Update form values
        form.reset({
          mcp_server_ids: mcpServerIds,
        });

        // Update local state
        setAddedToolIds(new Set(mcpServerIds));
        setTools(mcpServerIds);
      }
    }, [props.isEdit, data]);

    // Keep track of all fetched data across pages
    const [allFetchedData, setAllFetchedData] = useState<any[]>([]);

    const {
      data: mcpData,
      isLoading: isInitialLoading,
      error,
      refetch: refetchMcpTools,
    } = useQuery({
      queryKey: ["mcps", currentPage, debouncedSearchTerm],
      queryFn: () =>
        mcpApi.getMcpServersByUser(currentPage, 20, false, debouncedSearchTerm),
      staleTime: 30 * 60 * 100,
    });

    // Only show loading state for initial load (page 1), not for subsequent pages
    const isLoading = currentPage === 1 ? isInitialLoading : false;

    // Handle successful data fetching
    useEffect(() => {
      if (mcpData) {
        // When we get new data, append it to our allFetchedData if it's not the first page
        if (currentPage > 1) {
          setAllFetchedData((prev) => [...prev, ...(mcpData.data || [])]);
        } else {
          // If it's the first page, just set the data directly
          setAllFetchedData(mcpData.data || []);
        }

        // Update loading state and check if there are more items
        setIsLoadingMore(false);
        setHasMoreItems(mcpData.metadata?.hasNextPage || false);
      }
    }, [mcpData, currentPage]);

    // Handle error in data fetching
    useEffect(() => {
      if (error) {
        setIsLoadingMore(false);
        toast.error("Failed to load more items", {
          position: "top-center",
        });
      }
    }, [error]);

    const { data: popularTools, isLoading: isLoadingPopularTools } = useQuery({
      queryKey: ["marketplaceMcps"],
      queryFn: () => mcpApi.getMarketplaceMcps(1, 10),
      staleTime: 30 * 60 * 100,
    });

    const allTools: Tool[] = React.useMemo(() => {
      // Use the combined data from all pages instead of just the current page
      const mcpArray = currentPage === 1 ? mcpData?.data || [] : allFetchedData;
      return mcpArray.map((mcp: any) => ({
        ...mcp,
        isAdded: addedToolIds.has(mcp.id),
      }));
    }, [mcpData, addedToolIds, allFetchedData, currentPage]);

    // Handle load more functionality
    const handleLoadMore = () => {
      if (hasMoreItems && !isLoadingMore) {
        setIsLoadingMore(true);
        // Increment the page number to trigger the next query
        setCurrentPage((prev) => prev + 1);
      }
    };

    // Fetch selected tools data when addedToolIds changes
    useEffect(() => {
      fetchSelectedToolsData();
    }, [fetchSelectedToolsData]);

    // We're now using the API for search, so we don't need to filter locally
    const filteredTools = allTools;

    // Debounce search term to prevent excessive API calls
    useEffect(() => {
      const timer = setTimeout(() => {
        setDebouncedSearchTerm(searchTerm);
      }, 500); // 500ms delay

      return () => clearTimeout(timer);
    }, [searchTerm]);

    // Reset pagination when debounced search term changes
    useEffect(() => {
      setCurrentPage(1);
      // Reset the query to fetch with the new search term
      queryClient.invalidateQueries({
        queryKey: ["mcps", currentPage, debouncedSearchTerm],
      });
    }, [debouncedSearchTerm]);

    useEffect(() => {
      form.setValue("mcp_server_ids", tools);
    }, [tools]);

    // Fetch environment key values for tools with integrations
    // Only fetch once per integration to prevent infinite loops
    useEffect(() => {
      addedTools.forEach((tool: any) => {
        if (
          tool.integrations &&
          tool.integrations.length > 0 &&
          tool.integration_type &&
          tool.integration_type == "api_key" &&
          !fetchedToolIds.has(tool.integrations[0]) // Check if integration ID has been fetched
        ) {
          getMcpsEnvsById(tool.integrations[0]);
        }
      });
    }, [addedTools]); // Remove fetchedToolIds from dependencies to prevent re-renders

    const handleAddTool = (toolId: string) => {
      if (props.isEdit) {
        const newIds = new Set(addedToolIds).add(toolId);
        updateAgentTool({ data: { mcp_server_ids: Array.from(newIds) } });
        return;
      }
      setAddedToolIds((prevIds) => {
        const newIds = new Set(prevIds).add(toolId);
        setTools(Array.from(newIds));
        // Mark form as dirty
        form.setValue("mcp_server_ids", Array.from(newIds), {
          shouldDirty: true,
        });
        return newIds;
      });
    };

    const handleRemoveTool = (toolId: string) => {
      if (props.isEdit) {
        const newIds = new Set(addedToolIds);
        newIds.delete(toolId);
        updateAgentTool({ data: { mcp_server_ids: Array.from(newIds) } });
        return;
      }
      setAddedToolIds((prevIds) => {
        const newIds = new Set(prevIds);
        newIds.delete(toolId);
        setTools(Array.from(newIds));
        // Mark form as dirty
        form.setValue("mcp_server_ids", Array.from(newIds), {
          shouldDirty: true,
        });
        return newIds;
      });
      // Also remove from fetched tools and env values when tool is removed
      setFetchedToolIds((prev) => {
        const newSet = new Set(prev);
        newSet.delete(toolId);
        return newSet;
      });
      setEnvKeyValues((prev) => {
        const newValues = { ...prev };
        delete newValues[toolId];
        return newValues;
      });
      setOriginalEnvKeyValues((prev) => {
        const newValues = { ...prev };
        delete newValues[toolId];
        return newValues;
      });
    };

    const onSubmit = (formData: any, provider?: string, model?: string) => {
      onSubmitAgent({ ...data, ...formData }, provider, model);
    };

    const handlePublishClick = async () => {
      // First, check if we're not in edit mode and validate profile required fields
      // if (!props.isEdit) {
      const profileErrors = validateProfileRequiredFields(data);
      if (profileErrors.length > 0) {
        // Redirect to profile step and show validation errors
        setFormStep(1);
        setCheckRequiredField(true);
        profileErrors.forEach((error) => {
          toast.error(error.message, {
            duration: 4000,
            position: "top-center",
          });
        });
        return;
      }
      // }

      const isValid = await form.trigger();
      if (!isValid) {
        const errors = form.formState.errors;
        Object.values(errors).forEach((error) => {
          if (error?.message) {
            toast.error(error.message, {
              duration: 4000,
              position: "top-center",
            });
          }
        });
        return;
      }
      if (props.isEdit) {
        // In edit mode, directly call the API without modal
        onSubmit(form.getValues());
      } else {
        openPublishModal();
      }
    };

    const handleModalPublish = (provider: string, model: string) => {
      closePublishModal();
      onSubmit(form.getValues(), provider, model);
    };

    const handleModalDiscard = () => {
      closePublishModal();
    };

    // State to store OAuth authentication status for each tool
    const [oauthAuthStatus, setOauthAuthStatus] = useState<{
      [toolId: string]: {
        isAuthenticated: boolean;
        isLoading: boolean;
        toolName?: string;
        error?: string;
      };
    }>({});

    // Function to check OAuth authentication status for a specific tool
    const checkOAuthAuthStatus = useCallback(
      async (toolId: string, oauth_details: any) => {
        if (!oauth_details) return;

        // Set loading state
        setOauthAuthStatus((prev) => ({
          ...prev,
          [toolId]: {
            isAuthenticated: false,
            isLoading: true,
            toolName: oauth_details.tool_name,
          },
        }));

        try {
          // First check if the tool is already connected directly from the tool object
          const tool = allTools.find((t) => t.id === toolId);
          if (
            tool &&
            tool.integrations &&
            tool.integrations.length > 0 &&
            tool.is_connected
          ) {
            setOauthAuthStatus((prev) => ({
              ...prev,
              [toolId]: {
                isAuthenticated: true,
                isLoading: false,
                toolName: oauth_details.tool_name,
              },
            }));
            setIsOauthConnected(true);
            return;
          }

          // If not directly connected, check via API
          const data = await apiKeyAndIntegration.getOauthCredentials(toolId);

          setOauthAuthStatus((prev) => ({
            ...prev,
            [toolId]: {
              isAuthenticated:
                data?.user_integration_status?.is_connected || false,
              isLoading: false,
              toolName: oauth_details.tool_name,
            },
          }));

          setIsOauthConnected(
            data?.user_integration_status?.is_connected || false
          );
        } catch (error: any) {
          // Handle errors
          setOauthAuthStatus((prev) => ({
            ...prev,
            [toolId]: {
              isAuthenticated: false,
              isLoading: false,
              toolName: oauth_details.tool_name,
              error: "not_authenticated",
            },
          }));
          setIsOauthConnected(false);
        }
      },
      [allTools]
    );

    const {
      mutate: removeAuthCredentialDetail,
      isPending: isRemovingAuthCredential,
    } = useMutation({
      mutationFn: (tool: any) =>
        apiKeyAndIntegration.disconnectOauthCredentials(tool?.integrations[0]),
      onSuccess: (response) => {
        queryClient.invalidateQueries({ queryKey: ["mcps"] });
        queryClient.invalidateQueries({ queryKey: ["integrations"] });
        queryClient.invalidateQueries({ queryKey: ["connectedKeys"] });
        toast.success(response.message || "Credentials deleted successfully", {
          position: "top-center",
        });

        // For API key tools, refresh the env keys
        if (
          disconnectingTool?.integrations &&
          disconnectingTool?.integrations.length > 0 &&
          disconnectingTool?.integration_type == "api_key"
        ) {
          // Refresh the environment key values for this tool
          getMcpsEnvsById(disconnectingTool.integrations[0]);
        }

        // Re-check OAuth status for OAuth tools
        if (disconnectingTool?.id && disconnectingTool?.oauth_details) {
          checkOAuthAuthStatus(
            disconnectingTool.id,
            disconnectingTool.oauth_details
          );
        }

        // If in edit mode, update the agent's tools
        if (props.isEdit && disconnectingTool?.id) {
          // We don't want to remove the tool from the list, just update its connection status
          // The tool will be refreshed with the updated connection status from the API
          refetchMcpTools();
        }

        // Refresh the data for all added tools to ensure accurate connection status
        refreshAddedToolsData();

        setIsOauthConnected(false);
      },
      onError: (error) => {
        toast.error(
          `Failed to remove OAuth credentials: ${
            error instanceof Error ? error.message : "Unknown error"
          }`
        );
      },
    });

    useEffect(() => {
      if (props.isEdit) {
        const addedTools = localStorage.getItem("addedTools");
        const parsedAddedTools = JSON.parse(addedTools || "[]");
        if (!!addedTools && parsedAddedTools.length > 0) {
          setAddedToolIds(new Set(parsedAddedTools));
          form.setValue("mcp_server_ids", parsedAddedTools, {
            shouldDirty: true,
          });
          setTools(parsedAddedTools);
        }
        localStorage.removeItem("addedTools");
      }
    }, []);

    // Check OAuth authentication when tools are added
    useEffect(() => {
      addedTools.forEach((tool) => {
        if (tool.oauth_details && !oauthAuthStatus[tool.id]) {
          checkOAuthAuthStatus(tool.id, tool.oauth_details);
        }
      });
    }, [addedTools, checkOAuthAuthStatus, oauthAuthStatus]);

    useImperativeHandle(ref, () => ({
      saveToStore: () => {
        setData(form.getValues());
      },
    }));

    const handleOAuthLogin = (tool: any) => {
      if (!tool.integrations?.length) {
        toast.error("Integration id not found for this tool.");
        return;
      }

      const redirect_url = window.location.href;
      const user_id = user?.id;
      localStorage.setItem(
        "employeeToolsFormData",
        JSON.stringify({
          ...data,
          originForCreateAndEdit: originForCreateAndEdit,
          formStep: formStep,
          ...form.getValues(),
        })
      );
      const oauthParams = new URLSearchParams({
        user_id: user_id || "",
        redirect_url: redirect_url || "", // Redirect back to current page after OAuth
      });
      const authUrl = `${
        process.env.NEXT_PUBLIC_API_URL
      }/integrations/${tool.integrations[0]}/oauth/authorize?${oauthParams.toString()}`;
      window.location.href = authUrl;
    };

    useEffect(() => {
      if (pendingSaveStep === 3) {
        setData(form.getValues());
        setPendingSaveStep(null);
      }
    }, [pendingSaveStep, setData, setPendingSaveStep]); // Removed 'form' from dependencies

    const handlePopularToolClick = (tool: any) => {
      if (isPopularToolAdded(tool?.id)) {
        handleAddTool(tool?.id);
        return;
      }
      setSelectedPopularTool(tool);
      addPopularToolInAllTools(tool?.id);
      // setPopularToolDialogOpen(true);
    };

    const isPopularToolAdded = (toolId: string) => {
      return allTools.some((t) => t.id === toolId);
    };

    const addPopularToolInAllTools = async (toolId: string) => {
      setIsPopularToolLoading(true);
      try {
        const tool = await mcpApi.useMarketplaceTool(toolId, "MCP");
        if (tool.success) {
          queryClient.invalidateQueries({ queryKey: ["mcps"] });
          handleAddTool(toolId);
          toast.success("Tool added successfully");
          setPopularToolDialogOpen(false);
        }
      } catch (error) {
      } finally {
        setIsPopularToolLoading(false);
      }
    };

    if (props.isLoading) {
      return <LoadingSpinner message="Loading tools data..." />;
    }

    if (isUpdatingAgent && !isModalOpen) {
      return <LoadingSpinner message="Updating agent..." />;
    }

    return (
      <div className="flex flex-col h-full p-[18px] md:p-[25px] gap-[17px]">
        {/* Top Header */}
        <div className="flex flex-col justify-end items-start md:flex-row md:justify-end md:items-center mt-[50px] md:mt-0">
          {/* <Button
            variant="ghost"
            className="text-brand-primary hover:text-brand-primary/90 hover:bg-purple-50 p-0 font-medium"
            onClick={() => setFormStep(formStep - 1)}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button> */}

          <div className="flex gap-2">
            <Button
              variant="outline"
              className="px-6 w-auto"
              onClick={() => {
                if (originForCreateAndEdit == Employee.CARD) {
                  router.push(employeesRoute);
                } else {
                  if (props?.isEdit) {
                    // Store agentData in localStorage cache
                    const cacheKey = LOCAL_STORAGE_KEYS.AGENT_DETAILS_CACHE;
                    const cache = JSON.parse(
                      localStorage.getItem(cacheKey) || "{}"
                    );
                    if (props?.agentData?.id) {
                      cache[props.agentData.id] = props.agentData;
                      localStorage.setItem(cacheKey, JSON.stringify(cache));
                    }
                    localStorage.setItem(
                      LOCAL_STORAGE_KEYS.IS_NEW_CHAT,
                      "true"
                    );
                    router.push(`${employeeChatRoute}/${props?.agentData?.id}`);
                  } else {
                    router.push(dashboardRoute);
                  }
                }
              }}
            >
              Close
            </Button>
            {!props?.isEdit && (
              <Button
                variant="primary"
                className="px-6 w-auto"
                onClick={handlePublishClick}
                disabled={isCreatingAgent || isUpdatingAgent}
              >
                {isCreatingAgent || isUpdatingAgent ? (
                  <>
                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    {props?.isEdit ? "Saving..." : "Publishing..."}
                  </>
                ) : props?.isEdit ? (
                  "Save Changes"
                ) : (
                  "Publish Changes"
                )}
              </Button>
            )}
          </div>
        </div>

        <div className="flex-1 overflow-y-auto ">
          <div className="max-w-4xl  min-w-full">
            {/* Main White Container */}
            <div className="bg-white rounded-lg border border-gray-200 px-[12px] md:px-[32px] py-[24px] shadow-sm">
              {/* Agent Info Card */}
              <div className="flex flex-row gap-2 items-start mb-8 justify-between">
                <div className="flex-shrink-1 xl:flex-shrink-0">
                  <h1>Tools</h1>
                  <p className="text-text-secondary text-sm">
                    Plug in tools/MCP servers for quicker task execution through
                    your digital employees
                  </p>
                </div>

                {/* Action Buttons - visible in top right when user added tool */}
                {!!addedTools.length && (
                  <div className="flex gap-4">
                    <Button
                      variant="tertiary"
                      onClick={() => {
                        track(AnalyticsEvents.ADDED_TOOL, {
                          ...data,
                          ...form.getValues(),
                        });
                        setIsModalOpen(true);
                      }}
                      className="w-[140px]"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Select Tools
                    </Button>
                    {/* <Button
                      onClick={() =>
                        window.open(
                          "https://ruh-marketplace.rapidinnovation.dev/",
                          "_blanck"
                        )
                      }
                      variant="outline"
                      className="px-6"
                    >
                      <Package2 className="w-4 h-4 mr-2" />
                      Explore from marketplace
                    </Button> */}
                  </div>
                )}
              </div>

              {/* Tools Section */}
              <div className="space-y-6">
                {/* Connected Tools List */}
                {addedTools.length > 0 ? (
                  <div className="space-y-6">
                    <h3 className="text-[14px] mb-3 ">
                      Selected Tools ({addedTools.length})
                    </h3>
                    <div className="space-y-3">
                      {addedTools.map((tool: any) => (
                        <ToolCard
                          key={tool.id}
                          tool={tool}
                          oauthStatus={oauthAuthStatus[tool.id]}
                          envKeyValues={
                            tool.integrations && tool.integrations.length > 0
                              ? envKeyValues[tool.integrations[0]] || {}
                              : {}
                          }
                          isGettingEnvs={isGettingMcpsEnvsById}
                          isAddingEnvKeys={isAddingEnvKeys}
                          isRemovingOAuth={
                            isRemovingAuthCredential &&
                            disconnectingTool?.id === tool.id
                          }
                          apiKeyConfig={apiKeyConfig}
                          toolConnectionStatus={toolConnectionStatus}
                          onEnvKeyChange={(key, value) => {
                            if (
                              tool.integrations &&
                              tool.integrations.length > 0
                            ) {
                              setEnvKeyValues((prev) => ({
                                ...prev,
                                [tool.integrations[0]]: {
                                  ...(prev[tool.integrations[0]] || {}),
                                  [key]: value,
                                },
                              }));
                            }
                          }}
                          onAddEnvKeys={() => {
                            if (
                              !tool.integrations ||
                              tool.integrations.length === 0
                            )
                              return;

                            const integrationId = tool.integrations[0];
                            const configs = apiKeyConfig[integrationId];

                            if (!configs || !configs.length) return;

                            const allEnvKeyValues = configs
                              .map((config: any) => ({
                                key: config.name,
                                value:
                                  envKeyValues[integrationId]?.[config.name] ||
                                  "",
                              }))
                              .filter((item: any) => item.value.trim());

                            if (allEnvKeyValues.length > 0) {
                              addMcpEnvKeys({
                                mcpId: integrationId,
                                envKeyValues: allEnvKeyValues,
                              });
                            }
                          }}
                          onOAuthLogin={() => handleOAuthLogin(tool)}
                          onOAuthDisconnect={() => {
                            removeAuthCredentialDetail(tool);
                            setDisconnectingTool(tool);
                          }}
                          onRemoveTool={() => handleRemoveTool(tool.id)}
                        />
                      ))}
                    </div>
                  </div>
                ) : (
                  /* Empty State with Large White Box */
                  <div className="flex flex-col items-center justify-center py-16">
                    {/* Large White Box with Tool Icons Grid */}
                    <Image
                      src={"/assets/dashboard/create-agent.svg"}
                      width={216}
                      height={120}
                      alt={"create-agent-img"}
                    />

                    {/* Try Popular Tools Section */}
                    <div className="w-full max-w-2xl">
                      <div className="flex gap-4 justify-center py-6">
                        <Button
                          variant={"primary"}
                          onClick={() => {
                            track(AnalyticsEvents.ADDED_TOOL, {
                              ...data,
                              ...form.getValues(),
                            });
                            setIsModalOpen(true);
                          }}
                          className="!bg-brand-primary hover:!bg-brand-primary/90 text-white px-6 w-[270px]"
                        >
                          <Plus className="w-4 h-4 mr-2" />
                          Select Tools
                        </Button>
                        {/* <Button
                          onClick={() =>
                            window.open(
                              "https://ruh-marketplace.rapidinnovation.dev/",
                              "_blanck"
                            )
                          }
                          variant="outline"
                          className="px-6"
                        >
                          <Package2 className="w-4 h-4 mr-2" />
                          Explore from marketplace
                        </Button> */}
                      </div>
                      <h3 className="text-[16px] font-satoshi-bold text-text-secondary text-center mb-6">
                        Try popular tools
                      </h3>
                      {isLoadingPopularTools ? (
                        <div className="flex items-center justify-center h-32">
                          <div className="flex flex-col items-center gap-3">
                            <Loader2 className="h-8 w-8 animate-spin text-brand-primary" />
                            <p className="text-sm text-gray-600">
                              Loading popular tools...
                            </p>
                          </div>
                        </div>
                      ) : (
                        <>
                          <div className="grid grid-cols-3 gap-2 max-w-lg mx-auto">
                            {popularTools?.length > 0 &&
                              popularTools.map((tool: any) => (
                                <div
                                  key={tool.id}
                                  className={`flex items-center gap-2 p-2 rounded-md cursor-pointer transition-all duration-200 ${
                                    addedToolIds.has(tool.id)
                                      ? "bg-green-100 border border-green-300"
                                      : "bg-gray-100 hover:bg-gray-200"
                                  }`}
                                  onClick={() => {
                                    handlePopularToolClick(tool);
                                  }}
                                  role="button"
                                  tabIndex={0}
                                  aria-label={`Open dialog for ${tool.name}`}
                                >
                                  <div className="w-4 h-4 flex items-center justify-center flex-shrink-0">
                                    {tool.logo ? (
                                      <Image
                                        src={tool.logo}
                                        alt={tool.name}
                                        width={16}
                                        height={16}
                                        className="w-4 h-4"
                                      />
                                    ) : (
                                      <div className="w-4 h-4 bg-blue-600 rounded-sm flex items-center justify-center">
                                        <PackageIcon className="w-2.5 h-2.5 text-white" />
                                      </div>
                                    )}
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <span className="text-xs font-medium text-gray-800 block truncate">
                                      {sanitizeString(tool.name)}
                                    </span>
                                  </div>
                                  {addedToolIds.has(tool.id) && (
                                    <Check className="w-3 h-3 text-green-600 flex-shrink-0" />
                                  )}
                                </div>
                              ))}
                          </div>
                          {popularTools?.length === 0 && (
                            <p className="text-sm font-satoshi-regular text-text-secondary flex items-center justify-center text-center">
                              {" "}
                              No popular tools found{" "}
                            </p>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* All Tools Modal */}
        <ItemSelectionDialog
          onSubmitAgent={updateAgentTool}
          isUpdatingAgent={isUpdatingAgentTool}
          isEdit={props.isEdit}
          isOpen={isModalOpen}
          onOpenChange={setIsModalOpen}
          title="All Tools"
          subtitle="Your tools and tool templates from the community"
          searchPlaceholder="Search"
          items={filteredTools as SelectableItem[]}
          isLoading={isLoading}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          addedItemIds={addedToolIds}
          marketplaceUrl={marketplaceMcpsUrl}
          // Show refresh icon and handler
          showRefresh={true}
          onRefresh={async () => {
            await refetchMcpTools();
            toast.success("Tool list refreshed!");
          }}
          // Load more functionality
          onLoadMore={handleLoadMore}
          hasMoreItems={hasMoreItems}
          isLoadingMore={isLoadingMore}
          onItemToggle={(toolId) => {
            if (addedToolIds.has(toolId)) {
              handleRemoveTool(toolId);
            } else {
              handleAddTool(toolId);
            }
          }}
          onSelect={(selectedIds) => {
            // Convert Set to Array and update the tools
            const selectedIdsArray = Array.from(selectedIds) as string[];
            setAddedToolIds(new Set(selectedIdsArray));
            setTools(selectedIdsArray);
            // Mark form as dirty
            form.setValue("mcp_server_ids", selectedIdsArray, {
              shouldDirty: true,
            });
          }}
          emptyStateIcon={<Package2 className="h-12 w-12 mb-2" />}
          emptyStateText="No tools found"
          renderItemIcon={(tool) =>
            tool.logo ? (
              <Image src={tool.logo} alt={tool.name} width={24} height={24} />
            ) : (
              <PackageIcon className="w-5 h-5 text-blue-600" />
            )
          }
          renderItemSubtext={
            (tool) => ``
            // `by ${sanitizeString(tool.department || "")}`
          }
        />

        {!props.isEdit && isPublishModalOpen && (
          <PublishAgentModal
            open={isPublishModalOpen}
            onOpenChange={closePublishModal}
            onPublish={handleModalPublish}
            onDiscard={handleModalDiscard}
          />
        )}

        {/* Popular Tool Dialog */}
        <Dialog
          open={popularToolDialogOpen}
          onOpenChange={setPopularToolDialogOpen}
        >
          <DialogContent className="min-w-[240px] bg-white border border-border-default rounded-lg p-6">
            <DialogHeader>
              <DialogTitle className="text-[18px] font-satoshi-bold text-text-primary">
                {sanitizeString(selectedPopularTool?.name)}
              </DialogTitle>
              <DialogDescription className="text-sm text-text-secondary">
                {selectedPopularTool?.description}
              </DialogDescription>
            </DialogHeader>

            <DialogFooter>
              <Button
                variant={
                  isPopularToolAdded(selectedPopularTool?.id)
                    ? "secondary"
                    : "primary"
                }
                className="w-full"
                onClick={() => {
                  !isPopularToolAdded(selectedPopularTool?.id) &&
                    !isPopularToolLoading &&
                    addPopularToolInAllTools(selectedPopularTool?.id);
                }}
              >
                {isPopularToolAdded(selectedPopularTool?.id)
                  ? "Tool Already Added"
                  : isPopularToolLoading
                    ? "Adding..."
                    : "Add to account"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    );
  }
);

export { CreateEmployeeToolsFormPage };
