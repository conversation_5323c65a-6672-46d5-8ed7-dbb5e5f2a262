"use client";

import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { SecondaryButton } from "@/components/shared/SecondaryButton";
import {
  Di<PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useWorkflowStore } from "@/hooks/use-workflow";
import { sanitizeString } from "@/services/helper";
import { useEmployeeManagementStore } from "@/hooks/useEmployeeManagementStore";
import { useState } from "react";
import { Textarea } from "@/components/ui/textarea";

interface WorkflowStartingFormProps {
  onStartWorkflow: (selectedWorkflowId: string) => void;
  isWorkflowStreaming: boolean;
}

export const WorkflowForm = ({
  onStartWorkflow,
  isWorkflowStreaming,
}: WorkflowStartingFormProps) => {
  const {
    currentWorkflowName,
    workflowValues,
    openWorkflowStartingForm,
    setOpenWorkflowStartingForm,
    updateWorkflowFieldValue,
    getCurrentWorkflow,
    setWorkflowPayload,
  } = useWorkflowStore();

  const { employee } = useEmployeeManagementStore();
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleStartWorkflow = () => {
    const validationErrors: Record<string, string> = {};
    workflowValues.forEach((field) => {
      if (
        field.type !== "array" &&
        (!field.value ||
          (typeof field.value === "string" && field.value.trim() === ""))
      ) {
        validationErrors[field.field] = `${sanitizeString(
          field.field
        )} is required`;
      }
    });

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    setErrors({});

    const selectedWorkflowId = employee.selectedId
      ? getCurrentWorkflow(employee.selectedId)
      : null;

    if (!selectedWorkflowId) {
      setOpenWorkflowStartingForm(false);
      return;
    }

    const userDependentFields = workflowValues
      .filter((item) => item.type !== "array")
      .map((item) => item.field);

    const userPayloadTemplate = workflowValues.reduce(
      (acc, item) => {
        acc[item.field] = {
          transition_id: item.transition_id,
          value: item.value,
        };
        return acc;
      },
      {} as Record<string, any>
    );

    const workflowPayload = {
      workflow_id: selectedWorkflowId,
      payload: {
        user_dependent_fields: userDependentFields,
        user_payload_template: userPayloadTemplate,
      },
      approval: false,
      auto_approve: false,
    };

    setWorkflowPayload(workflowPayload);

    onStartWorkflow(selectedWorkflowId);
  };

  return (
    <Dialog
      open={openWorkflowStartingForm}
      onOpenChange={setOpenWorkflowStartingForm}
    >
      <DialogContent className="h-[90vh] p-10 md:max-w-[700px] flex flex-col gap-10 font-primary">
        <DialogHeader>
          <DialogTitle className="text-left">
            Please fill out the following fields for{" "}
            {sanitizeString(currentWorkflowName ?? "")} workflow
          </DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-8 flex-grow overflow-y-auto">
          {workflowValues
            .filter((value) => value.type !== "array")
            .map((value, index) => (
              <div key={index} className="flex flex-col gap-2">
                <Label>{sanitizeString(value.field)}</Label>
                {value.type === "string" && (
                  <Input
                    type="text"
                    placeholder={sanitizeString(value.field)}
                    onChange={(e) => {
                      updateWorkflowFieldValue(value.field, e.target.value);
                      if (errors[value.field]) {
                        setErrors((prev) => {
                          const newErrors = { ...prev };
                          delete newErrors[value.field];
                          return newErrors;
                        });
                      }
                    }}
                  />
                )}
                {value.type === "number" && (
                  <Input
                    type="number"
                    placeholder={sanitizeString(value.field)}
                    onChange={(e) => {
                      updateWorkflowFieldValue(value.field, e.target.value);
                      if (errors[value.field]) {
                        setErrors((prev) => {
                          const newErrors = { ...prev };
                          delete newErrors[value.field];
                          return newErrors;
                        });
                      }
                    }}
                  />
                )}
                {value.type === "multiline" && (
                  <Textarea
                    className="h-[100px] resize-none"
                    rows={5}
                    placeholder={sanitizeString(value.field)}
                    onChange={(e) => {
                      updateWorkflowFieldValue(value.field, e.target.value);
                      if (errors[value.field]) {
                        setErrors((prev) => {
                          const newErrors = { ...prev };
                          delete newErrors[value.field];
                          return newErrors;
                        });
                      }
                    }}
                  />
                )}
                {value.type === "enum" && (
                  <Select
                    onValueChange={(newValue) => {
                      updateWorkflowFieldValue(value.field, newValue);
                      if (errors[value.field]) {
                        setErrors((prev) => {
                          const newErrors = { ...prev };
                          delete newErrors[value.field];
                          return newErrors;
                        });
                      }
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={sanitizeString(value.field)} />
                    </SelectTrigger>
                    <SelectContent>
                      {value.enum?.map((option) => (
                        <SelectItem key={option} value={option}>
                          {sanitizeString(option)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}

                {errors[value.field] && (
                  <p className="text-red-500 text-sm">{errors[value.field]}</p>
                )}
              </div>
            ))}
        </div>
        <DialogFooter className="flex-row">
          <SecondaryButton
            className="w-30"
            onClick={() => setOpenWorkflowStartingForm(false)}
          >
            Cancel
          </SecondaryButton>
          <PrimaryButton
            className="w-30"
            onClick={handleStartWorkflow}
            disabled={isWorkflowStreaming}
          >
            Start Workflow
          </PrimaryButton>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
