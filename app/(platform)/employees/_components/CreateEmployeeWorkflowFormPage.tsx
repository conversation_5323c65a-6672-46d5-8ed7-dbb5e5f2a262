"use client";

import React, { useImperative<PERSON><PERSON>le, forwardRef } from "react";
import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { createEmployeeProfileSchema } from "@/lib/schemas/createEmployee";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useState, useEffect } from "react";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import {
  ArrowLeft,
  Plus,
  Grid3X3,
  Loader2,
  Check,
  MoreVertical,
  Trash2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { ItemSelectionDialog } from "@/components/shared/ItemSelectionDialog";
import { Input } from "@/components/ui/input";
import { workflowApi } from "@/app/api/workflow";
import { WorkflowInDB } from "@/shared/interfaces";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Image from "next/image";
import { useEmployeeEditStore } from "@/hooks/useEmployeeEditStore";
import { agentApi } from "@/app/api/agent";
import { toast } from "sonner";
import { LoadingSpinner } from "@/components/shared/LoadingSpinner";
import { PublishAgentModal } from "@/components/modals/PublishAgentModal";
import { validateProfileRequiredFields } from "./CreateEmployeeProfileFormPage";
import { useRouter } from "next/navigation";
import {
  agentRoute,
  dashboardRoute,
  employeeChatRoute,
  employeesRoute,
  redirectionAfterCreateEmployee,
} from "@/shared/routes";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogDescription,
} from "@/components/ui/dialog";
import { mcpApi } from "@/app/api/mcp";
import { sanitizeString } from "@/services/helper";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  LOCAL_STORAGE_KEYS,
  marketplaceWorkflowsUrl,
  workflowBuilderUrl,
} from "@/shared/constants";
import { AnalyticsEvents, Employee } from "@/shared/enums";
import { useMixpanelTrack } from "@/hooks/useMixPanelTrack";

const createWorkflowSchema = createEmployeeProfileSchema.pick({
  workflow_ids: true,
});

type CreateWorkflowSchema = z.infer<typeof createWorkflowSchema>;

interface CreateEmployeeWorkflowFormPageProps {
  agentData?: any;
  isEdit?: boolean;
  isLoading?: boolean;
  onSubmitAgent: (formData: any, provider?: string, model?: string) => void;
  isCreatingAgent?: boolean;
  isUpdatingAgent?: boolean;
}

const CreateEmployeeWorkflowFormPage = forwardRef(
  (
    {
      onSubmitAgent,
      isCreatingAgent,
      isUpdatingAgent,
      ...props
    }: CreateEmployeeWorkflowFormPageProps,
    ref
  ) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [hasMoreItems, setHasMoreItems] = useState(false);
    const [isLoadingMore, setIsLoadingMore] = useState(false);
    const [allFetchedData, setAllFetchedData] = useState<any[]>([]);
    const [popularWorkflowDialogOpen, setPopularWorkflowDialogOpen] =
      useState(false);
    const [selectedPopularWorkflow, setSelectedPopularWorkflow] = useState<
      any | null
    >(null);
    const [isPopularWorkflowLoading, setIsPopularWorkflowLoading] =
      useState(false);
    const [addedWorkflows, setAddedWorkflows] = useState<any[]>([]);
    const router = useRouter();
    const {
      setData,
      formStep,
      setFormStep,
      setCheckRequiredField,
      data,
      isPublishModalOpen,
      openPublishModal,
      closePublishModal,
      pendingSaveStep,
      setPendingSaveStep,
      originForCreateAndEdit,
    } = useEmployeeCreateStore();
    const [addedWorkflowIds, setAddedWorkflowIds] = useState<Set<string>>(
      new Set(data.workflow_ids || [])
    );
    const isMobile = useIsMobile();
    const { closeModal } = useEmployeeEditStore();
    const queryClient = useQueryClient();
    const track = useMixpanelTrack();
    const { agentData: agentDataProp } = props;

    const form = useForm<CreateWorkflowSchema>({
      resolver: zodResolver(createWorkflowSchema),
      defaultValues: {
        workflow_ids: data.workflow_ids || [],
      },
    });

    useImperativeHandle(ref, () => ({
      saveToStore: () => {
        const currentFormData = form.getValues();
        const updatedData = {
          ...currentFormData,
          workflow_ids: Array.from(addedWorkflowIds),
        };
        setData(updatedData);
      },
    }));

    // Initialize addedWorkflowIds from store data or edit data
    useEffect(() => {
      const initialWorkflowIds = data.workflow_ids || [];

      setAddedWorkflowIds(new Set(initialWorkflowIds));
      form.setValue("workflow_ids", initialWorkflowIds);
    }, [props.isEdit, data?.workflow_ids, data.workflow_ids, form]);

    // Update form values when addedWorkflowIds changes
    useEffect(() => {
      const workflowArray = Array.from(addedWorkflowIds);
      form.setValue("workflow_ids", workflowArray);
    }, [addedWorkflowIds, form]);

    // Debounce search term to prevent excessive filtering
    useEffect(() => {
      const timer = setTimeout(() => {
        setDebouncedSearchTerm(searchTerm);
      }, 500); // 500ms delay

      return () => clearTimeout(timer);
    }, [searchTerm]);

    // Reset pagination when search term changes
    useEffect(() => {
      setCurrentPage(1);
      setAllFetchedData([]);
    }, [debouncedSearchTerm]);

    // Fetch all workflows from API
    const {
      data: workflowData,
      isLoading: isInitialLoading,
      refetch: refetchWorkflows,
    } = useQuery({
      queryKey: ["workflows", currentPage, debouncedSearchTerm],
      queryFn: () =>
        workflowApi.getWorkflowsByUser(currentPage, 10, debouncedSearchTerm),
      staleTime: 30 * 60 * 100,
    });

    // Fetch workflows by IDs for the added workflows
    const {
      data: workflowsByIdsData,
      isLoading: isLoadingWorkflowsByIds,
      refetch: refetchWorkflowsByIds,
    } = useQuery({
      queryKey: ["workflowsByIds", Array.from(addedWorkflowIds)],
      queryFn: () =>
        workflowApi
          .getWorkflowsByIds(Array.from(addedWorkflowIds))
          .then((response) => {
            if (response.success && response.workflows) {
              setAddedWorkflows(response.workflows);
            }
          }),
      staleTime: 30 * 60 * 100,
      enabled: addedWorkflowIds.size > 0, // Only run query if there are workflow IDs
    });

    // Only show loading state for initial load (page 1), not for subsequent pages
    const isLoadingWorkflows = currentPage === 1 ? isInitialLoading : false;

    // Handle successful data fetching
    useEffect(() => {
      if (workflowData) {
        // When we get new data, append it to our allFetchedData if it's not the first page
        if (currentPage > 1) {
          setAllFetchedData((prev) => [...prev, ...(workflowData.data || [])]);
        } else {
          // If it's the first page, just set the data directly
          setAllFetchedData(workflowData.data || []);
        }

        // Update loading state and check if there are more items
        setIsLoadingMore(false);
        setHasMoreItems(workflowData.metadata?.hasNextPage || false);
      }
    }, [workflowData, currentPage]);

    // Handle load more functionality
    const handleLoadMore = () => {
      if (hasMoreItems && !isLoadingMore) {
        setIsLoadingMore(true);
        // Increment the page number to trigger the next query
        setCurrentPage((prev) => prev + 1);
      }
    };

    // Fetch workflows from API
    const { data: popularWorkflows, isLoading: isLoadingPopularWorkflows } =
      useQuery({
        queryKey: ["marketplaceWorkflows"],
        queryFn: () => workflowApi.getMarketplaceWorkflows(1, 10),
        staleTime: 30 * 60 * 100,
      });

    //update data api
    const { mutate: updateAgentWorkflow, isPending: isUpdatingAgentWorkflow } =
      useMutation({
        mutationFn: ({ data }: { data: any }) =>
          agentApi.updateAgentCombined(props?.agentData?.id, data),
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ["agents"] });
          queryClient.invalidateQueries({
            queryKey: ["agent", props?.agentData?.id],
          });
          toast.success("Workflows updated successfully", {
            position: "top-center",
          });
          setIsModalOpen(false);
        },
        onError: (error) => {
          toast.error(error.message || "Failed to update workflows", {
            position: "top-center",
          });
        },
      });

    const allWorkflows: WorkflowInDB[] = React.useMemo(() => {
      if (!workflowData?.data && allFetchedData.length === 0) return [];

      // Use combined data from all pages if we're past page 1
      const workflowArray =
        currentPage === 1
          ? Array.isArray(workflowData?.data)
            ? workflowData?.data
            : workflowData?.data || []
          : allFetchedData;

      return workflowArray;
    }, [workflowData, allFetchedData, currentPage]);

    // Since we're using the API for search, we don't need to filter the workflows client-side
    const filteredWorkflows = allWorkflows;

    const handleAddWorkflow = (workflowId: string) => {
      if (props.isEdit) {
        const newIds = new Set(addedWorkflowIds).add(workflowId);
        updateAgentWorkflow({ data: { workflow_ids: Array.from(newIds) } });
        return;
      }
      setAddedWorkflowIds((prevIds) => {
        const newIds = new Set(prevIds).add(workflowId);
        form.setValue("workflow_ids", Array.from(newIds), {
          shouldDirty: true,
        });
        // Refetch workflows by IDs when a new workflow is added
        setTimeout(() => refetchWorkflowsByIds(), 0);
        return newIds;
      });
    };

    const handleRemoveWorkflow = (workflowId: string) => {
      if (props.isEdit) {
        const newIds = new Set(addedWorkflowIds);
        newIds.delete(workflowId);
        updateAgentWorkflow({ data: { workflow_ids: Array.from(newIds) } });
        return;
      }
      setAddedWorkflowIds((prevIds) => {
        const newIds = new Set(prevIds);
        newIds.delete(workflowId);
        form.setValue("workflow_ids", Array.from(newIds), {
          shouldDirty: true,
        });
        // Refetch workflows by IDs when a workflow is removed
        setTimeout(() => refetchWorkflowsByIds(), 0);
        return newIds;
      });
    };

    const onSubmit = (formData: any, provider?: string, model?: string) => {
      onSubmitAgent({ ...data, ...formData }, provider, model);
    };

    const handlePublishClick = async () => {
      // First, check if we're not in edit mode and validate profile required fields
      // if (!props.isEdit) {
      const profileErrors = validateProfileRequiredFields(data);
      if (profileErrors.length > 0) {
        // Redirect to profile step and show validation errors
        setFormStep(1);
        setCheckRequiredField(true);
        profileErrors.forEach((error) => {
          toast.error(error.message, {
            duration: 4000,
            position: "top-center",
          });
        });
        return;
      }
      // }
      const isValid = await form.trigger();
      if (!isValid) {
        const errors = form.formState.errors;
        Object.values(errors).forEach((error) => {
          if (error?.message) {
            toast.error(error.message, {
              duration: 4000,
              position: "top-center",
            });
          }
        });
        return;
      }
      if (props.isEdit) {
        // In edit mode, directly call the API without modal
        onSubmit(form.getValues());
      } else {
        openPublishModal();
      }
    };

    const handleModalPublish = (provider: string, model: string) => {
      closePublishModal();
      onSubmit(form.getValues(), provider, model);
    };

    const handleModalDiscard = () => {
      closePublishModal();
    };

    useEffect(() => {
      if (pendingSaveStep === 4) {
        const currentFormData = form.getValues();
        const updatedData = {
          ...currentFormData,
          workflow_ids: Array.from(addedWorkflowIds),
        };
        setData(updatedData);
        setPendingSaveStep(null);
      }
    }, [pendingSaveStep, setData, setPendingSaveStep, form, addedWorkflowIds]);

    const isPopularWorkflowAdded = (workflow: any) => {
      return allWorkflows.some(
        (w: any) =>
          w?.workflow_url === workflow?.workflow_url ||
          w?.builder_url === workflow?.builder_url
      );
    };

    const handlePopularWorkflowClick = (workflow: any) => {
      addPopularWorkflowInAllWorkflows(workflow?.id);
      // setSelectedPopularWorkflow(workflow);
      // setPopularWorkflowDialogOpen(true);
    };

    const addPopularWorkflowInAllWorkflows = async (workflowId: string) => {
      setIsPopularWorkflowLoading(true);
      try {
        const result = await mcpApi.useMarketplaceTool(workflowId, "WORKFLOW");
        // if (result.success) {
        queryClient.invalidateQueries({ queryKey: ["workflows"] });
        handleAddWorkflow(result?.workflow_id);
        toast.success("Workflow added successfully");
        setPopularWorkflowDialogOpen(false);
        // }
      } catch (error) {
        toast.error("Failed to add workflow");
      } finally {
        setIsPopularWorkflowLoading(false);
      }
    };

    if (props.isLoading) {
      return <LoadingSpinner message="Loading workflow data..." />;
    }
    if (isUpdatingAgentWorkflow && !isModalOpen) {
      return <LoadingSpinner message="Updating workflows..." />;
    }
    return (
      <div className="flex flex-col h-full p-[18px] md:p-[25px] gap-[17px]">
        {/* Top Header */}
        <div className="flex flex-col justify-end items-start md:flex-row md:justify-end md:items-center mt-[50px] md:mt-0">
          {/* <Button
            variant="ghost"
            className="text-brand-primary hover:text-brand-primary/90 hover:bg-purple-50 p-0"
            onClick={() => setFormStep(formStep - 1)}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back
          </Button> */}

          <div className="flex gap-2">
            <Button
              variant="outline"
              className="px-6 w-auto"
              onClick={() => {
                if (originForCreateAndEdit == Employee.CARD) {
                  router.push(employeesRoute);
                } else {
                  if (props?.isEdit) {
                    // Store agentData in localStorage cache
                    const cacheKey = LOCAL_STORAGE_KEYS.AGENT_DETAILS_CACHE;
                    const cache = JSON.parse(
                      localStorage.getItem(cacheKey) || "{}"
                    );
                    if (props?.agentData?.id) {
                      cache[props.agentData.id] = props.agentData;
                      localStorage.setItem(cacheKey, JSON.stringify(cache));
                    }
                    localStorage.setItem(
                      LOCAL_STORAGE_KEYS.IS_NEW_CHAT,
                      "true"
                    );
                    router.push(`${employeeChatRoute}/${props?.agentData?.id}`);
                  } else {
                    router.push(dashboardRoute);
                  }
                }
              }}
            >
              Close
            </Button>
            {!props?.isEdit && (
              <Button
                variant="primary"
                className="px-6 w-auto"
                onClick={handlePublishClick}
                disabled={isCreatingAgent || isUpdatingAgent}
              >
                {isCreatingAgent || isUpdatingAgent ? (
                  <>
                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    {props?.isEdit ? "Saving..." : "Publishing..."}
                  </>
                ) : props?.isEdit ? (
                  "Save Changes"
                ) : (
                  "Publish Changes"
                )}
              </Button>
            )}
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          <div className="max-w-4xl min-w-full">
            {/* Single White Box containing all components */}
            <div className="bg-white rounded-lg px-[12px] md:px-[32px] py-[24px] border border-gray-200">
              {/* Agent Info Section */}
              <div className="flex flex-row gap-2 items-start mb-8 justify-between">
                <div className="flex-shrink-1 xl:flex-shrink-0">
                  <h1>Workflows</h1>
                  <p className="text-text-secondary text-sm">
                    Power your AI employees' capabilities by adding pre-built
                    workflows that they can execute.
                  </p>
                </div>

                {/* Action Buttons - Only visible when workflows are added */}
                {addedWorkflows.length > 0 && (
                  <div className="flex gap-4">
                    <Button
                      variant={"tertiary"}
                      onClick={() => {
                        track(AnalyticsEvents.ADDED_WORKFLOW, {
                          ...data,
                          ...form.getValues(),
                        });
                        setIsModalOpen(true);
                      }}
                      className="w-[160px]"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Select Workflow
                    </Button>
                    {/* <Button variant="outline" className="px-6">
                    <Grid3X3 className="w-4 h-4 mr-2" />
                    Explore from marketplace
                  </Button> */}
                  </div>
                )}
              </div>

              {/* Workflows Section */}
              <div className="space-y-6">
                {/* Connected Workflows List */}
                {addedWorkflows.length > 0 ? (
                  <div className="space-y-6">
                    <h3 className="text-[14px] mb-3">
                      Selected Workflows ({addedWorkflowIds.size})
                    </h3>
                    <div className="space-y-3">
                      {addedWorkflows.map((workflow) => (
                        <div
                          key={workflow.id}
                          className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg bg-gray-50"
                        >
                          <div className="w-12 h-12 bg-white rounded-lg flex items-center justify-center border flex-shrink-0">
                            <Grid3X3 className="w-6 h-6 text-gray-500" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h4 className="text-[14px] font-satoshi-bold">
                              {sanitizeString(workflow.name)}
                            </h4>
                            <p className="text-sm text-gray-600">
                              {workflow.description ||
                                "Build custom automations and integrations with other apps."}
                            </p>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="text-gray-400 hover:text-gray-600"
                              >
                                <MoreVertical className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-48">
                              <DropdownMenuItem
                                onClick={() =>
                                  handleRemoveWorkflow(workflow.id)
                                }
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                Remove workflow
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  /* Empty State with Large White Box */
                  <div className="flex flex-col items-center justify-center py-16">
                    {/* Large White Box with Workflow Icons Grid */}
                    <Image
                      src={"/assets/dashboard/create-agent.svg"}
                      width={216}
                      height={120}
                      alt={"create-agent-img"}
                    />

                    {/* Action Buttons - Centered in empty state */}
                    <div className="flex gap-4 my-6">
                      <Button
                        variant={"primary"}
                        onClick={() => {
                          track(AnalyticsEvents.ADDED_WORKFLOW, {
                            ...data,
                            ...form.getValues(),
                          });
                          setIsModalOpen(true);
                        }}
                        className="w-[270px] px-6"
                      >
                        <Plus className="w-4 h-4 mr-2" />
                        Select Workflow
                      </Button>
                    </div>

                    {/* Try Popular Workflows Section */}
                    <div className="w-full max-w-2xl">
                      <h3 className="text-[16px] font-satoshi-bold text-text-secondary text-center mb-6">
                        Try popular workflows
                      </h3>
                      {isLoadingPopularWorkflows ? (
                        <div className="flex items-center justify-center h-32">
                          <div className="flex flex-col items-center gap-3">
                            <Loader2 className="h-8 w-8 animate-spin text-brand-primary" />
                            <p className="text-sm text-gray-600">
                              Loading popular workflows...
                            </p>
                          </div>
                        </div>
                      ) : (
                        <>
                          {popularWorkflows?.length > 0 ? (
                            <div className="grid grid-cols-3 gap-2 max-w-lg mx-auto">
                              {popularWorkflows.map((workflow: any) => (
                                <div
                                  key={workflow.id}
                                  className={`flex items-center gap-2 p-2 rounded-md cursor-pointer transition-all duration-200 ${
                                    addedWorkflowIds.has(workflow.id)
                                      ? "bg-green-100 border border-green-300"
                                      : "bg-gray-100 hover:bg-gray-200"
                                  }`}
                                  onClick={() =>
                                    handlePopularWorkflowClick(workflow)
                                  }
                                  role="button"
                                  tabIndex={0}
                                  aria-label={`Open dialog for ${workflow.name}`}
                                >
                                  <div className="w-4 h-4 flex items-center justify-center flex-shrink-0">
                                    <div className="w-4 h-4 bg-blue-600 rounded-sm flex items-center justify-center">
                                      <Grid3X3 className="w-2.5 h-2.5 text-white" />
                                    </div>
                                  </div>
                                  <div className="flex-1 min-w-0">
                                    <span className="text-xs font-medium text-gray-800 block truncate">
                                      {sanitizeString(workflow.name)}
                                    </span>
                                  </div>
                                  {addedWorkflowIds.has(workflow.id) && (
                                    <Check className="w-3 h-3 text-green-600 flex-shrink-0" />
                                  )}
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-sm flex items-center font-satoshi-regular text-text-secondary justify-center text-center">
                              No workflows found
                            </p>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* All Workflows Modal */}
        <ItemSelectionDialog
          onSubmitAgent={updateAgentWorkflow}
          isUpdatingAgent={isUpdatingAgentWorkflow}
          isEdit={props.isEdit}
          dialogWidth={isMobile ? `min-w-[300px]` : "min-w-[599px]"}
          type="workflow"
          isOpen={isModalOpen}
          onOpenChange={setIsModalOpen}
          title="All Workflows"
          subtitle="Your workflows and workflow templates from the community"
          searchPlaceholder="Search workflows here"
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          items={filteredWorkflows}
          addedItemIds={addedWorkflowIds}
          marketplaceUrl={marketplaceWorkflowsUrl}
          workflowBuilderUrl={workflowBuilderUrl}
          // Show refresh icon and handler
          showRefresh={true}
          onRefresh={async () => {
            await refetchWorkflows();
            toast.success("Workflow list refreshed!");
          }}
          // Load more functionality
          onLoadMore={handleLoadMore}
          hasMoreItems={hasMoreItems}
          isLoadingMore={isLoadingMore}
          onItemToggle={(workflowId) => {
            if (addedWorkflowIds.has(workflowId)) {
              handleRemoveWorkflow(workflowId);
            } else {
              handleAddWorkflow(workflowId);
            }
          }}
          onSelect={(selectedIds) => {
            // Convert Set to Array and update the workflows
            const selectedIdsArray = Array.from(selectedIds) as string[];
            setAddedWorkflowIds(new Set(selectedIdsArray));
            // Mark form as dirty
            form.setValue("workflow_ids", selectedIdsArray, {
              shouldDirty: true,
            });
          }}
          isLoading={isLoadingWorkflows}
          renderItemIcon={(workflow) => (
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
              <Grid3X3 className="w-5 h-5 text-blue-600" />
            </div>
          )}
          renderItemSubtext={(workflow) => ""}
          emptyStateIcon={<Grid3X3 className="h-12 w-12 mb-2" />}
          emptyStateText="No workflows found"
          gridCols={2}
        />

        {!props.isEdit && isPublishModalOpen && (
          <PublishAgentModal
            open={isPublishModalOpen}
            onOpenChange={closePublishModal}
            onPublish={handleModalPublish}
            onDiscard={handleModalDiscard}
          />
        )}

        {/* Popular Workflow Dialog */}
        <Dialog
          open={popularWorkflowDialogOpen}
          onOpenChange={setPopularWorkflowDialogOpen}
        >
          <DialogContent className="min-w-[240px] bg-white border border-border-default rounded-lg p-6">
            <DialogHeader>
              <DialogTitle className="text-[18px] font-satoshi-bold text-text-primary">
                {sanitizeString(selectedPopularWorkflow?.name)}
              </DialogTitle>
              <DialogDescription className="text-sm text-text-secondary">
                {selectedPopularWorkflow?.description}
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant={
                  isPopularWorkflowAdded(selectedPopularWorkflow)
                    ? "secondary"
                    : "primary"
                }
                className="w-full"
                disabled={
                  isPopularWorkflowAdded(selectedPopularWorkflow) ||
                  isPopularWorkflowLoading
                }
                onClick={() => {
                  if (
                    !isPopularWorkflowAdded(selectedPopularWorkflow) &&
                    !isPopularWorkflowLoading
                  ) {
                    addPopularWorkflowInAllWorkflows(
                      selectedPopularWorkflow?.id
                    );
                  }
                }}
              >
                {isPopularWorkflowAdded(selectedPopularWorkflow)
                  ? "Workflow Already Added"
                  : isPopularWorkflowLoading
                    ? "Adding..."
                    : "Add to account"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    );
  }
);

export { CreateEmployeeWorkflowFormPage };
