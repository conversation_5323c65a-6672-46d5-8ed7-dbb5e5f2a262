"use client";
import { useCallback, useEffect } from "react";
import { employeeTourConfig } from "../../../lib/utils/tourConfig";
import { isTourCompleted, markTourCompleted } from "@/lib/utils/tourUtils";
import { LOCAL_STORAGE_KEYS } from "@/shared/constants";
import { useTourContext } from "@/lib/providers/TourProvider";

export default function AgentsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { startTour } = useTourContext();

  useEffect(() => {
    if (!isTourCompleted(LOCAL_STORAGE_KEYS.RUH_EMPLOYEE_TOUR_COMPLETED)) {
      handleStartTour();
    }
  }, []);

  const handleStartTour = useCallback(() => {
    startTour(employeeTourConfig, {
      onComplete: () => {
        markTourCompleted(LOCAL_STORAGE_KEYS.RUH_EMPLOYEE_TOUR_COMPLETED);
      },
      onSkip: () => {
        markTourCompleted(LOCAL_STORAGE_KEYS.RUH_EMPLOYEE_TOUR_COMPLETED);
      },
    });
  }, [startTour]);

  return <div className="w-full flex flex-col h-full">{children}</div>;
}
