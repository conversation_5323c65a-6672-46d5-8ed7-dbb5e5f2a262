"use client";
import React from "react";
import PaymentHistoryTable from "../_components/PaymentHistoryTable";
import { useSidebarStore } from "@/hooks/use-sidebar";

const PaymentHistory = () => {
  const { isSecondaryBarOpen } = useSidebarStore();

  return (
    <div
      className={`px-6 py-6 ${isSecondaryBarOpen ? "md:max-w-[calc(100vw-319px)]" : "md:max-w-[calc(100vw-75px)] xl:w-full"}`}
    >
      <div className="flex items-center justify-between mb-6 mt-[50px] md:mt-0">
        <div>
          <h2 className="text-xl font-semibold text-brand-primary-font">
            Payment History
          </h2>
          <p className="text-muted-foreground text-sm w-[70%]">
            View your transaction history, including subscription payments and
            credit purchases.
          </p>
        </div>
      </div>

      <PaymentHistoryTable />
    </div>
  );
};

export default PaymentHistory;
