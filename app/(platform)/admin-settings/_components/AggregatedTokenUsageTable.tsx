import React from "react";
import CustomTable from "@/components/shared/CustomTable";
import Image from "next/image";
import { useRouter } from "next/navigation";

interface AggregatedTokenUsageTableProps {
  tokenUsageLogs: any[];
  isLoading: boolean;
}

const AggregatedTokenUsageTable: React.FC<AggregatedTokenUsageTableProps> = ({
  tokenUsageLogs,
  isLoading,
}) => {
  const router = useRouter();
  const tableColumns = [
    {
      key: "user_name",
      label: "User Name",
      render: (row: any) => row.user_name,
    },
    {
      key: "total_input_tokens",
      label: "Total Input Tokens",
      render: (row: any) => row.total_input_tokens,
    },
    {
      key: "total_output_tokens",
      label: "Total Output Tokens",
      render: (row: any) => row.total_output_tokens,
    },
    {
      key: "total_credits",
      label: "Credit Used",
      render: (row: any) => (
        <div className="flex items-center gap-1">
          <Image
            src={`/assets/icons/coin.svg`}
            alt="coin"
            width={20}
            height={20}
            className="w-4 h-4"
          />
          <span className="text-sm">{row.total_credits.toFixed(4)}</span>
        </div>
      ),
    },
  ];

  const handleRowClick = (row: any) => {
    router.push(`/admin-settings/token-usage/${row.user_id}`);
  };

  return (
    <CustomTable
      isLoading={isLoading}
      columns={tableColumns}
      data={tokenUsageLogs}
      pageSize={tokenUsageLogs.length}
      totalPages={1}
      totalRecords={tokenUsageLogs.length}
      isPagination={false}
      onRowClick={handleRowClick}
      rowClickable={true}
    />
  );
};

export default AggregatedTokenUsageTable;