import React from "react";
import moment from "moment";
import CustomTable from "@/components/shared/CustomTable";
import Image from "next/image";

interface TokenUsageTableProps {
  tokenUsageLogs: any[];
  isLoading: boolean;
}

const TokenUsageTable: React.FC<TokenUsageTableProps> = ({
  tokenUsageLogs,
  isLoading,
}) => {
  const tableColumns = [
    {
      key: "date",
      label: "Date",
      render: (row: any) => moment(row.date).format("MMM DD YYYY"),
    },
    {
      key: "input_tokens",
      label: "Input Tokens",
      render: (row: any) => row.input_tokens,
    },
    {
      key: "output_tokens",
      label: "Output Tokens",
      render: (row: any) => row.output_tokens,
    },
    {
      key: "total_credits",
      label: "Credit Used",
      render: (row: any) => (
        <div className="flex items-center gap-1">
          <Image
            src={`/assets/icons/coin.svg`}
            alt="coin"
            width={20}
            height={20}
            className="w-4 h-4"
          />
          <span className="text-sm">{row.total_credits.toFixed(4)}</span>
        </div>
      ),
    },
  ];

  return (
    <CustomTable
      isLoading={isLoading}
      columns={tableColumns}
      data={tokenUsageLogs}
      pageSize={tokenUsageLogs.length}
      totalPages={1}
      totalRecords={tokenUsageLogs.length}
      isPagination={false}
    />
  );
};

export default TokenUsageTable;