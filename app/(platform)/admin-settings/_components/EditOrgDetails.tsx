"use client";

import React, { useState, useRef, useEffect } from "react";
import Image from "next/image";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { toast } from "sonner";
import { Loader2, Pencil, Plus } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { gcsApi, uploadToGCS } from "@/app/api/gcs";

// Zod schema for organization details
const orgDetailsSchema = z.object({
  name: z.string().min(1, "Organization name is required"),
  websiteUrl: z
    .string()
    .url("Please enter a valid URL")
    .optional()
    .or(z.literal("")),
  industry: z.string().min(1, "Industry is required"),
  logo: z.string().optional(),
});

type OrgDetailsFormData = z.infer<typeof orgDetailsSchema>;

interface EditOrgDetailsProps {
  currentData?: {
    name: string;
    websiteUrl: string;
    industry: string;
    logo: string;
  };
  onSave?: (data: OrgDetailsFormData) => Promise<void>;
}

const EditOrgDetails: React.FC<EditOrgDetailsProps> = ({
  currentData = {
    name: "",
    websiteUrl: "",
    industry: "",
    logo: "",
  },
  onSave,
}) => {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const form = useForm<OrgDetailsFormData>({
    resolver: zodResolver(orgDetailsSchema),
    defaultValues: {
      name: currentData.name,
      websiteUrl: currentData.websiteUrl,
      industry: currentData.industry,
      logo: currentData.logo,
    },
  });

  // Update form values when currentData changes
  useEffect(() => {
    form.reset({
      name: currentData.name,
      websiteUrl: currentData.websiteUrl,
      industry: currentData.industry,
      logo: currentData.logo,
    });
    setPreviewUrl(currentData.logo);
  }, [currentData, form]);

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("image/")) {
      toast.error("Please select an image file");
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("File size must be less than 5MB");
      return;
    }

    setIsUploading(true);

    try {
      // Create preview URL
      const objectUrl = URL.createObjectURL(file);
      setPreviewUrl(objectUrl);

      // Get presigned URL
      const fileName = `org-logo-${Date.now()}-${file.name}`;
      const presignedResponse = await gcsApi.getPresignedUrl(
        fileName,
        file.type,
        "organization-logos"
      );

      // Upload file to GCS
      const publicUrl = await uploadToGCS(presignedResponse.url, file);

      // Update form with the public URL
      form.setValue("logo", publicUrl);
      setPreviewUrl(publicUrl);

      // Clean up object URL
      URL.revokeObjectURL(objectUrl);

      toast.success("Logo uploaded successfully");
    } catch (error) {
      console.error("Error uploading logo:", error);
      toast.error("Failed to upload logo");
      // Reset preview to original logo
      setPreviewUrl(currentData.logo);
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleRemoveLogo = () => {
    form.setValue("logo", "");
    setPreviewUrl("");
    toast.success("Logo removed successfully");
  };

  const onSubmit = async (data: OrgDetailsFormData) => {
    setIsLoading(true);
    try {
      if (onSave) {
        await onSave(data);
      } else {
        // Default behavior - just show success message
        await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate API call
        toast.success("Organization details updated successfully");
      }
      setOpen(false);
    } catch (error) {
      toast.error("Failed to update organization details");
      console.error("Error updating organization details:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    form.reset(); // Reset to original values
    setPreviewUrl(currentData.logo); // Reset preview to original logo
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="tertiary" className="flex items-center gap-2">
          <Pencil className="w-4 h-4" />
          Edit Details
        </Button>
      </DialogTrigger>
      <DialogContent className="md:max-w-[500px]  overflow-auto md:max-h-none">
        <DialogHeader className="items-start md:items-center">
          <DialogTitle className="text-brand-primary-font">
            Edit Organization Details
          </DialogTitle>
          <DialogDescription className="text-left">
            Update your organization information. Changes will be reflected
            across your workspace.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Logo Upload Section */}
            <div className="space-y-4">
              <FormLabel className="text-sm font-bold">Logo</FormLabel>
              <div className="flex items-center gap-4">
                <div className="relative">
                  <div className="w-24 h-24 rounded-full border-2 border-dashed border-brand-primary flex items-center justify-center bg-brand-card">
                    {previewUrl || form.watch("logo") ? (
                      <Image
                        src={previewUrl || form.watch("logo") || ""}
                        alt="Organization Logo"
                        width={80}
                        height={80}
                        className="w-20 h-20 object-contain rounded-full"
                      />
                    ) : (
                      <Plus className="w-8 h-8 text-brand-primary" />
                    )}
                  </div>
                  {isUploading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-full">
                      <Loader2 className="w-6 h-6 text-white animate-spin" />
                    </div>
                  )}
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                </div>

                {/* Upload/Update/Remove Buttons */}
                <div className="flex flex-col gap-2">
                  {previewUrl || form.watch("logo") ? (
                    <>
                      <Button
                        type="button"
                        onClick={handleFileSelect}
                        disabled={isUploading}
                        className="bg-brand-primary text-white hover:bg-brand-primary/90"
                      >
                        Update
                      </Button>
                      <Button
                        type="button"
                        onClick={handleRemoveLogo}
                        disabled={isUploading}
                        variant="outline"
                        className="border-brand-primary text-brand-primary hover:bg-brand-primary/10"
                      >
                        Remove
                      </Button>
                    </>
                  ) : (
                    <Button
                      type="button"
                      onClick={handleFileSelect}
                      disabled={isUploading}
                      className="bg-brand-primary text-white hover:bg-brand-primary/90"
                    >
                      Upload
                    </Button>
                  )}
                </div>
              </div>

              <p className="text-brand-secondary-font text-xs">
                Upload organization logo (optional)
              </p>
            </div>

            {/* Form Fields */}
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-bold">
                      Organization Name
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter organization name"
                        className="text-sm font-normal"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="websiteUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-bold">
                      Website URL
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="https://www.example.com"
                        className="text-sm font-normal"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="industry"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-bold">
                      Industry
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="text-sm font-normal">
                          <SelectValue placeholder="Select an industry" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="Sales">Sales</SelectItem>
                        <SelectItem value="Marketing">Marketing</SelectItem>
                        <SelectItem value="IT & Support">
                          IT & Support
                        </SelectItem>
                        <SelectItem value="Finance">Finance</SelectItem>
                        <SelectItem value="Healthcare">Healthcare</SelectItem>
                        <SelectItem value="Education">Education</SelectItem>
                        <SelectItem value="Manufacturing">
                          Manufacturing
                        </SelectItem>
                        <SelectItem value="Retail">Retail</SelectItem>
                        <SelectItem value="Other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <DialogFooter className="gap-2 flex-row">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <PrimaryButton
                type="submit"
                isLoading={isLoading}
                disabled={isLoading || isUploading}
                className="w-fit"
              >
                Save Changes
              </PrimaryButton>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default EditOrgDetails;
