import React from "react";
import moment from "moment";
import CustomTable from "@/components/shared/CustomTable";
import Image from "next/image";

interface UsageBreakdownTableProps {
  usageLogs: any[];
  isLoading: boolean;
}

const UsageBreakdownTable: React.FC<UsageBreakdownTableProps> = ({
  usageLogs,
  isLoading,
}) => {
  const tableColumns = [
    {
      key: "date",
      label: "Date",
      render: (row: any) => moment(row.date).format("MMM DD YYYY"),
    },
    {
      key: "input_tokens",
      label: "Input Tokens",
      render: (row: any) => row.input_tokens?.toLocaleString() || 0,
    },
    {
      key: "output_tokens",
      label: "Output Tokens",
      render: (row: any) => row.output_tokens?.toLocaleString() || 0,
    },
    {
      key: "voice_input_tokens",
      label: "Voice Input Tokens",
      render: (row: any) => row.voice_input_tokens?.toLocaleString() || 0,
    },
    {
      key: "voice_output_tokens",
      label: "Voice Output Tokens",
      render: (row: any) => row.voice_output_tokens?.toLocaleString() || 0,
    },
    {
      key: "total_credits",
      label: "Chat Credits",
      render: (row: any) => (
        <div className="flex items-center gap-1">
          <Image
            src={`/assets/icons/coin.svg`}
            alt="coin"
            width={20}
            height={20}
            className="w-4 h-4"
          />
          <span className="text-sm">{(row.total_credits || 0).toFixed(4)}</span>
        </div>
      ),
    },
    {
      key: "total_voice_credits",
      label: "Voice Credits",
      render: (row: any) => (
        <div className="flex items-center gap-1">
          <Image
            src={`/assets/icons/coin.svg`}
            alt="coin"
            width={20}
            height={20}
            className="w-4 h-4"
          />
          <span className="text-sm">{(row.total_voice_credits || 0).toFixed(4)}</span>
        </div>
      ),
    },
  ];

  return (
    <CustomTable
      isLoading={isLoading}
      columns={tableColumns}
      data={usageLogs}
      pageSize={usageLogs.length}
      totalPages={1}
      totalRecords={usageLogs.length}
      isPagination={false}
    />
  );
};

export default UsageBreakdownTable;
