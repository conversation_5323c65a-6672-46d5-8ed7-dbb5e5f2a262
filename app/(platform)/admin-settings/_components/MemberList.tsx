"use client";

import React, { useState } from "react";
import Link from "next/link";
import { ChevronDown, ChevronRight } from "lucide-react";
import MemberListPagination from "./MemberListPagination";
import { useQuery } from "@tanstack/react-query";
import { ColumnDef } from "@tanstack/react-table";

import { Badge } from "@/components/ui/badge";
import ChangeRoleType from "./ChangeRoleType";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
// CustomLoader is now handled by the DataTable component
import { DataTable } from "@/components/shared/DataTable";

import { useOrgStore } from "@/hooks/use-organization";
import { organizationApi } from "@/app/api/organization";
import NoUser from "@/public/assets/Icons-components/NoUser";

// TypeScript interfaces for the API structure
interface PaginatedResponse {
  users: Member[];
  totalCount: number;
  page: number;
  pageSize: number;
}

interface Department {
  name: string;
  role: string;
  permission: string;
}

interface Member {
  id: string;
  name: string;
  email: string;
  departments: Department[];
}

interface InvitedMember {
  invitee_id: string;
  invitee_name: string;
  invitee_email: string;
  organisationId: string;
  organisationName: string;
  department: string; // Flat property, not array
  role: string; // Flat property, not array
  permission: string; // Flat property, not array
  status: string;
  joinedAt: string | null;
  createdAt: string;
  expiresAt: string;
}

const MemberList = () => {
  const { currentOrganization } = useOrgStore();
  const [activeTab, setActiveTab] = useState("active");
  const [openRoleTypeModal, setOpenRoleTypeModal] = useState(false);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [currentPage, setCurrentPage] = useState(1);

  // Separate React Query hooks for each tab
  const {
    data: activeUsersData,
    isLoading: activeLoading,
    error: activeError, // eslint-disable-line @typescript-eslint/no-unused-vars
  } = useQuery<PaginatedResponse>({
    queryKey: ["activeOrgUsers", currentPage],
    queryFn: () => organizationApi.getAllActiveOrgUsers(currentPage, 10),
    enabled: activeTab === "active" && !!currentOrganization,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  const activeUsers = activeUsersData?.users || [];
  const totalPages = Math.ceil((activeUsersData?.totalCount || 0) / 10);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const {
    data: invitedUsers = [],
    isLoading: invitedLoading,
    error: invitedError, // eslint-disable-line @typescript-eslint/no-unused-vars
  } = useQuery({
    queryKey: ["invitedOrgUsers"],
    queryFn: () => organizationApi.getAllInvitedOrgUsers("PENDING"),
    enabled: activeTab === "invited" && !!currentOrganization,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  // Clear expanded rows when switching tabs
  const handleTabSwitch = (tab: string) => {
    setActiveTab(tab);
    setExpandedRows(new Set()); // Clear expanded state when switching tabs
    setCurrentPage(1); // Reset to first page when switching tabs
  };

  // Helper functions for active members
  const getPrimaryDepartment = (
    departments: Department[]
  ): Department | null => {
    if (!departments || departments.length === 0) return null;
    // Return department with highest role (ADMIN > MEMBER)
    const adminDept = departments.find((dept) => dept.role === "ADMIN");
    return adminDept || departments[0];
  };

  const getPrimaryRole = (departments: Department[]): string => {
    if (!departments || departments.length === 0) return "";
    // Return highest role across all departments
    const hasAdmin = departments.some((dept) => dept.role === "ADMIN");
    return hasAdmin ? "ADMIN" : "MEMBER";
  };

  const getDepartmentDisplayText = (departments: Department[]): string => {
    if (!departments || departments.length === 0) return "No Department";
    if (departments.length === 1) return departments[0].name;
    const primaryDept = getPrimaryDepartment(departments);
    const remainingCount = departments.length - 1;
    return `${primaryDept?.name} + ${remainingCount} more`;
  };

  const toggleRowExpansion = (userId: string) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(userId)) {
      newExpandedRows.delete(userId);
    } else {
      newExpandedRows.add(userId);
    }
    setExpandedRows(newExpandedRows);
  };

  const handleRoleChange = () => {};

  // Column definitions for active members
  const activeColumns: ColumnDef<Member>[] = [
    {
      accessorKey: "name",
      header: "Name",
      cell: ({ row }) => {
        const member = row.original;
        return (
          <div className="flex items-center">
            <div className="flex-shrink-0 h-8 w-8 sm:h-10 sm:w-10">
              <div className="h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-brand-primary flex items-center justify-center">
                <span className="text-xs sm:text-sm font-medium text-white">
                  {(member.name || member?.email || "").charAt(0).toUpperCase()}
                </span>
              </div>
            </div>
            <div className="ml-3 sm:ml-4">
              <div className="text-xs sm:text-sm font-medium text-foreground">
                {member?.name || ""}
              </div>
              <div className="text-xs sm:text-sm text-muted-foreground">
                {member?.email || ""}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "departments",
      header: "Departments",
      cell: ({ row }) => {
        const member = row.original;
        const isExpanded = expandedRows.has(member.id);
        return (
          <div className="flex items-center gap-1 sm:gap-2">
            <span
              className="text-xs sm:text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 cursor-pointer underline hover:no-underline transition-colors duration-200 flex items-center gap-1"
              onClick={() => toggleRowExpansion(member.id)}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === "Enter" || e.key === " ") {
                  e.preventDefault();
                  toggleRowExpansion(member.id);
                }
              }}
              aria-label={`${
                isExpanded ? "Collapse" : "Expand"
              } departments for ${member.name || member.email}`}
            >
              {isExpanded ? (
                <ChevronDown className="h-3 w-3 mr-1" />
              ) : (
                <ChevronRight className="h-3 w-3 mr-1" />
              )}
              {member.departments && member.departments.length > 1
                ? getDepartmentDisplayText(member.departments)
                : member.departments?.[0]?.name || "No Department"}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "role",
      header: "Primary Role",
      cell: ({ row }) => {
        const member = row.original;
        const primaryRole = getPrimaryRole(member.departments);
        return (
          <div className="text-xs sm:text-sm text-foreground">
            {primaryRole}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: () => (
        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300">
          Active
        </span>
      ),
    },
  ];

  // Column definitions for invited members
  const invitedColumns: ColumnDef<InvitedMember>[] = [
    {
      accessorKey: "invitee_name",
      header: "Name",
      cell: ({ row }) => {
        const member = row.original;
        return (
          <div className="flex items-center space-x-2 sm:space-x-3">
            <div className="flex-shrink-0 h-8 w-8 sm:h-10 sm:w-10">
              <div className="h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-brand-primary flex items-center justify-center">
                <span className="text-xs sm:text-sm font-medium text-white">
                  {(member.invitee_name || member.invitee_email)
                    .charAt(0)
                    .toUpperCase()}
                </span>
              </div>
            </div>
            <div>
              <div className="text-xs sm:text-sm font-medium text-gray-900 dark:text-gray-100">
                {member.invitee_name || ""}
              </div>
              <div className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                {member.invitee_email}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "department",
      header: "Department",
      cell: ({ row }) => (
        <div className="text-xs sm:text-sm text-foreground">
          {row.original.department || "No Department"}
        </div>
      ),
    },
    {
      accessorKey: "role",
      header: "Role",
      cell: ({ row }) => (
        <div className="text-xs sm:text-sm text-foreground">
          {row.original.role || "No Role"}
        </div>
      ),
    },
    {
      accessorKey: "permission",
      header: "Permission",
      cell: ({ row }) => (
        <Badge variant="outline" className="text-xs">
          {row.original.permission || "No Permission"}
        </Badge>
      ),
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => (
        <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300">
          {row.original?.status === "PENDING" ? "Invite Sent" : "Pending"}
        </span>
      ),
    },
  ];

  // Render expanded row content for departments
  const renderExpandedRow = (member: Member) => {
    return (
      <div className="bg-muted/30 rounded-md overflow-hidden mx-4 my-2">
        {member.departments &&
          member.departments.map((dept, deptIndex) => (
            <div
              key={`${member.id}-dept-${deptIndex}`}
              className="grid grid-cols-5 py-3 px-4 border-b last:border-b-0"
            >
              <div className="pl-8 sm:pl-14 text-xs sm:text-sm text-muted-foreground">
                Department Details
              </div>
              <div className="text-xs sm:text-sm font-medium text-foreground">
                {dept.name}
              </div>
              <div className="text-xs sm:text-sm text-foreground">
                {dept.role}
              </div>
              <div>
                <Badge variant="outline" className="text-xs">
                  {dept.permission}
                </Badge>
              </div>
              <div></div>
            </div>
          ))}
      </div>
    );
  };

  return (
    <div className="w-full lg:w-full overflow-x-auto lg:overflow-auto">
      <div className="px-0 sm:px-2">
        <div className="flex items-center justify-between mb-4 sm:mb-6 border-b border-border">
          <div className="flex space-x-4">
            <button
              className={`py-2 px-1 text-xs sm:text-sm font-medium ${
                activeTab === "active"
                  ? "border-b-2 border-brand-primary text-brand-primary"
                  : "text-primary-font hover:text-brand-secondary"
              }`}
              onClick={() => handleTabSwitch("active")}
            >
              Active
            </button>
            {currentOrganization?.isAdmin && (
              <button
                className={`py-2 px-1 text-xs sm:text-sm font-medium ${
                  activeTab === "invited"
                    ? "border-b-2 border-brand-primary text-brand-primary"
                    : "text-primary-font hover:text-brand-secondary"
                }`}
                onClick={() => handleTabSwitch("invited")}
              >
                Invited
              </button>
            )}
          </div>
        </div>

        {/* Active Members Table */}
        {activeTab === "active" && (
          <>
            {!activeLoading && activeUsersData?.totalCount && (
              <div className="text-sm text-muted-foreground mb-4">
                Total Active Members: {activeUsersData?.totalCount}
              </div>
            )}
            <DataTable
              columns={activeColumns}
              data={activeUsers}
              isLoading={activeLoading}
              loadingText="Loading Active Members..."
              emptyText="You don't have any members"
              emptyIcon={<NoUser width={120} height={120} />}
              emptyAction={
                <Link href="/admin-settings/invite-member">
                  <PrimaryButton>Invite Members</PrimaryButton>
                </Link>
              }
              expandedRows={expandedRows}
              renderExpandedRow={renderExpandedRow}
              getRowId={(row: Member) => row.id}
            />
            {totalPages > 1 && (
              <MemberListPagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            )}
          </>
        )}

        {/* Invited Members Table */}
        {activeTab === "invited" && (
          <DataTable
            columns={invitedColumns}
            data={invitedUsers}
            isLoading={invitedLoading}
            loadingText="Loading Invited Members..."
            emptyText="You don't have any invited members"
            emptyIcon={<NoUser width={120} height={120} />}
            emptyAction={
              <Link href="/admin-settings/invite-member">
                <PrimaryButton>Invite Members</PrimaryButton>
              </Link>
            }
          />
        )}
      </div>

      <ChangeRoleType
        open={openRoleTypeModal}
        onClose={() => setOpenRoleTypeModal(false)}
        onSave={handleRoleChange}
      />
    </div>
  );
};

export default MemberList;
