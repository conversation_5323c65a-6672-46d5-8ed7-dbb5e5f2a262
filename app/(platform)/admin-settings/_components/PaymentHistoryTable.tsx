'use client'
import React from 'react'
import { useQuery } from '@tanstack/react-query'
import { format } from 'date-fns'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from '@/components/ui/button'
import CustomLoader from '@/components/shared/CustomLoader'
import { useOrgStore } from '@/hooks/use-organization'
import { subscriptionApi } from '@/app/api/subscription'

type Transaction = {
  id: string;
  organisation_id: string;
  transaction_type: string;
  status: string;
  amount: number;
  currency: string;
  description: string;
  stripe_charge_id: string;
  stripe_invoice_id: string | null;
  invoice_url: string;
  subscription_id: string;
  created_at: string;
};

const PaymentHistoryTable = () => {
  const { currentOrganization } = useOrgStore();

  const {
    data: paymentHistory,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["paymentHistory", currentOrganization?.id],
    queryFn: () => subscriptionApi.getPaymentHistory(currentOrganization!.id),
    enabled: !!currentOrganization,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });

  const handleInvoiceClick = (url: string) => {
    window.open(url, '_blank')
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-40">
        <CustomLoader loadingText="Loading payment history..." />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8 text-red-500">
        Error loading payment history. Please try again.
      </div>
    );
  }

  const transactions = paymentHistory?.transactions || [];

  return (
    <div className="shadow overflow-hidden border-b border-border sm:rounded-lg">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Invoice</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.length > 0 ? (
            transactions.map((transaction: Transaction) => (
              <TableRow key={transaction.id}>
                <TableCell>{format(new Date(transaction.created_at), 'PPP')}</TableCell>
                <TableCell>{transaction.description}</TableCell>
                <TableCell>{new Intl.NumberFormat('en-US', { style: 'currency', currency: transaction.currency.toUpperCase() }).format(transaction.amount)}</TableCell>
                <TableCell>{transaction.status}</TableCell>
                <TableCell>
                  <Button variant="link" onClick={() => handleInvoiceClick(transaction.invoice_url)}>
                    View Invoice
                  </Button>
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={5} className="text-center py-12">
                No payment history found.
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
}

export default PaymentHistoryTable