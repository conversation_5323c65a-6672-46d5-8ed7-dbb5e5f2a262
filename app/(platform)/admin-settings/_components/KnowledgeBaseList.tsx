"use client";

import React from "react";
import Link from "next/link";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ListFilter } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { PrimaryButton } from "@/components/shared/PrimaryButton";

interface FileData {
  id: number;
  name: string;
  fileSize: string;
  areas: string[];
  dateCreated: string;
  uploadedBy: string;
}

const FILES_DATA: FileData[] = [
  {
    id: 1,
    name: "Meeting_Notes_2025-05-09.docx",
    fileSize: "242 mb",
    areas: ["Design", "Marketing"],
    dateCreated: "May 13, 2025",
    uploadedBy: "Arunima Ray",
  },
  {
    id: 2,
    name: "Project_Plan_V2.pdf",
    fileSize: "267 mb",
    areas: ["Design", "Marketing"],
    dateCreated: "May 13, 2025",
    uploadedBy: "Arunima Ray",
  },
  {
    id: 3,
    name: "Invoice_#1234_March2025.xlsx",
    fileSize: "267 mb",
    areas: ["Design", "Marketing"],
    dateCreated: "May 13, 2025",
    uploadedBy: "Arunima Ray",
  },
  {
    id: 4,
    name: "Survey_results_Q1_2025.csv",
    fileSize: "267 mb",
    areas: ["Design", "Marketing"],
    dateCreated: "May 13, 2025",
    uploadedBy: "Arunima Ray",
  },
  {
    id: 5,
    name: "app_config.json",
    fileSize: "267 mb",
    areas: ["Design", "Marketing"],
    dateCreated: "May 13, 2025",
    uploadedBy: "Arunima Ray",
  },
];

const KnowledgeBaseList = () => {
  const handleAction = (action: string, fileId: number): void => {};

  return (
    <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="flex flex-col sm:flex-row items-center justify-between mb-6">
        <div className="mb-4 sm:mb-0">
          <h1 className="text-2xl font-semibold text-primary-font">
            {FILES_DATA.length} {`files in Rapid's Knowledge Base`}
          </h1>
          <p className="text-primary-font text-sm">
            Collaborate, build, and deploy AI agents securely in one workspace.
          </p>
        </div>

        <Link href="/admin-settings/knowledge-source">
          <PrimaryButton className="w-50 h-11 mt-6">
            + Add Knowledge Base
          </PrimaryButton>
        </Link>
      </div>

      <Tabs defaultValue="files" className="w-full mb-6">
        <div className="flex items-center">
          <Button variant="outline" size="sm">
            <ListFilter className="w-5 h-5" />
            Filter
          </Button>
        </div>

        <TabsContent value="files">
          <div className="shadow overflow-hidden border-b border-border sm:rounded-lg mt-4">
            <table className="min-w-full divide-y divide-border bg-card text-foreground">
              <thead className="bg-muted text-muted-foreground">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  >
                    Name
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  >
                    File Size
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  >
                    Areas
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  >
                    Date Created
                  </th>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  >
                    Uploaded By
                  </th>
                  <th scope="col" className="relative px-6 py-3">
                    <span className="sr-only">Actions</span>
                  </th>
                </tr>
              </thead>
              <tbody className="bg-card divide-y divide-border">
                {FILES_DATA.map((file: FileData) => (
                  <tr key={file.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-foreground">
                      {file.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                      {file.fileSize}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-wrap gap-1">
                        {file.areas.map((area: string, index: number) => (
                          <span
                            key={index}
                            className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-muted text-muted-foreground"
                          >
                            {area}
                          </span>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                      {file.dateCreated}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                      {file.uploadedBy}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            className="h-8 w-8 p-0 data-[state=open]:bg-muted"
                          >
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => handleAction("view", file.id)}
                          >
                            View
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleAction("edit", file.id)}
                          >
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={() => handleAction("delete", file.id)}
                            className="text-red-600 focus:bg-red-100 dark:focus:bg-red-900"
                          >
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {FILES_DATA.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                No files found.
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="integrations">
          <div className="py-8 text-center text-muted-foreground">
            Integrations content goes here.
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default KnowledgeBaseList;
