"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface MemberListPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const MemberListPagination = ({
  currentPage,
  totalPages,
  onPageChange,
}: MemberListPaginationProps) => {
  const renderPageNumbers = () => {
    const pages = [];
    const showEllipsisStart = currentPage > 2;
    const showEllipsisEnd = currentPage < totalPages - 1;

    // Always show first page
    pages.push(
      <Button
        key={1}
        variant={currentPage === 1 ? "default" : "outline"}
        className="h-8 w-8"
        onClick={() => onPageChange(1)}
      >
        1
      </Button>
    );

    // Show ellipsis after first page
    if (showEllipsisStart) {
      pages.push(
        <span key="ellipsis-start" className="px-2">
          ...
        </span>
      );
    }

    // Show current page and surrounding pages
    for (
      let i = Math.max(2, currentPage - 1);
      i <= Math.min(totalPages - 1, currentPage + 1);
      i++
    ) {
      if (i === 1 || i === totalPages) continue;
      pages.push(
        <Button
          key={i}
          variant={currentPage === i ? "default" : "outline"}
          className="h-8 w-8"
          onClick={() => onPageChange(i)}
        >
          {i}
        </Button>
      );
    }

    // Show ellipsis before last page
    if (showEllipsisEnd) {
      pages.push(
        <span key="ellipsis-end" className="px-2">
          ...
        </span>
      );
    }

    // Always show last page
    if (totalPages > 1) {
      pages.push(
        <Button
          key={totalPages}
          variant={currentPage === totalPages ? "default" : "outline"}
          className="h-8 w-8"
          onClick={() => onPageChange(totalPages)}
        >
          {totalPages}
        </Button>
      );
    }

    return pages;
  };

  return (
    <div className="flex items-center justify-end space-x-2 py-4">
      <Button
        variant="outline"
        className="h-8 w-8 p-0"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>
      {renderPageNumbers()}
      <Button
        variant="outline"
        className="h-8 w-8 p-0"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default MemberListPagination;
