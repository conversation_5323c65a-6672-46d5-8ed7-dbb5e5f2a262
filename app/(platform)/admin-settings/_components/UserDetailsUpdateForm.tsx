"use client";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  // FormDescription,
} from "@/components/ui/form";
import { useUserStore } from "@/hooks/use-user";
import { UserFormData, userFormSchema } from "@/lib/schemas/user";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { useState } from "react";
import { Loader2 } from "lucide-react";
import { useFileUpload } from "@/hooks/useFileUpload";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { toast } from "sonner";
import { userApi } from "@/app/api/user";

export const UserDetailsUpdateForm = () => {
  const { user, setUser } = useUserStore();
  const [isLoading, setIsLoading] = useState(false);

  const {
    fileInputRef,
    isUploading,
    handleFileChange,
    triggerFileSelect,
    acceptValue,
  } = useFileUpload({
    fileType: "image",
    filePath: "user-profile-images",
    onSuccess: (url) => {
      form.setValue("profileImage", url);
      // Automatically update the profile image when uploaded
      handleUpdateUserDetails({
        profile_image: url,
      });
    },
    onError: (error) => {
      console.error("error=>", error);
      toast.error("Failed to upload profile image");
    },
    maxSizeMB: 5,
  });

  const form = useForm<UserFormData>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      fullName: user?.fullName ?? "",
      profileImage: user?.profileImage ?? "",
      phoneNumber: user?.phoneNumber ?? "",
    },
    values: {
      fullName: user?.fullName ?? "",
      profileImage: user?.profileImage ?? "",
      phoneNumber: user?.phoneNumber ?? "",
    },
  });

  const { isDirty } = form.formState;

  const handleUpdateUserDetails = async (data: {
    full_name?: string | null;
    phone_number?: string | null;
    profile_image?: string | null;
  }) => {
    try {
      const response = await userApi.updateUserDetails(data);

      // Update the user store with the new data
      setUser({
        ...user,
        fullName: response.fullName,
        phoneNumber: response.phoneNumber,
        profileImage: response.profileImage,
      });

      toast.success("Profile updated successfully");
    } catch (error) {
      toast.error("Failed to update profile");
      console.error("Error updating user details:", error);
    }
  };

  async function onSubmit(data: UserFormData) {
    setIsLoading(true);
    try {
      await handleUpdateUserDetails({
        full_name: data.fullName,
        phone_number: data.phoneNumber,
        profile_image: data.profileImage,
      });
    } catch (error) {
      console.error("Error in form submission:", error);
    } finally {
      setIsLoading(false);
    }
  }

  // Function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-row gap-10 max-w-3xl pl-20 font-primary"
      >
        {/* Left side - Avatar */}
        <div className="flex flex-col items-center gap-2">
          <div className="relative">
            <div
              onClick={triggerFileSelect}
              className="cursor-pointer hover:opacity-90 transition-opacity"
            >
              <Avatar className="h-24 w-24">
                <AvatarImage src={form.watch("profileImage")} />
                <AvatarFallback className="text-2xl bg-brand-card border border-brand-stroke">
                  {getInitials(form.watch("fullName") || "User")}
                </AvatarFallback>
              </Avatar>
              {isUploading && (
                <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-full">
                  <Loader2 className="w-8 h-8 text-white animate-spin" />
                </div>
              )}
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept={acceptValue}
              onChange={handleFileChange}
              className="hidden"
            />
          </div>
          <p className="text-brand-primary-font text-xs font-bold text-center">
            Employee Avatar
          </p>
        </div>

        {/* Right side - Form fields */}
        <div className="flex flex-col gap-6 w-full">
          <FormField
            control={form.control}
            name="fullName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormItem>
            <FormLabel>Email</FormLabel>
            <FormControl>
              <Input
                value={user?.email || ""}
                disabled
                className="bg-muted cursor-not-allowed"
              />
            </FormControl>
          </FormItem>
          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <PrimaryButton
            type="submit"
            isLoading={isLoading}
            className="font-secondary"
            disabled={!isDirty}
          >
            Update
          </PrimaryButton>
        </div>
      </form>
    </Form>
  );
};
