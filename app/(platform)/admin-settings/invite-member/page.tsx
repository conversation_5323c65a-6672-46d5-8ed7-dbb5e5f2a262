"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { toast } from "sonner";
import { ArrowLeft } from "lucide-react";

import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PrimaryButton } from "@/components/shared/PrimaryButton";

import api from "@/services/axios";
import { useQuery } from "@tanstack/react-query";
import { useOrgStore } from "@/hooks/use-organization";
import { departmentApi } from "@/app/api/department";
import { DepartmentList } from "@/shared/interfaces";
import { Button } from "@/components/ui/button";
import { capitalizeFirstLetter } from "@/lib/utils";

const formSchema = z.object({
  sendInvitesTo: z
    .string()
    .email("Please enter a valid email address.")
    .min(1, {
      message: "Please enter at least one recipient.",
    }),
  department: z.string().min(1, {
    message: "Please specify the department.",
  }),
  inviteAs: z.string().min(1, {
    message: "Please specify a role.",
  }),
  permission: z.string().min(1, {
    message: "Please specify a permission.",
  }),
});

const InviteMemberForm = () => {
  const { currentOrganization } = useOrgStore();
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  // Fetch departments using useQuery
  const { data: departments = [], isLoading: isDepartmentsLoading } = useQuery<
    DepartmentList[]
  >({
    queryKey: ["departments", currentOrganization?.id],
    queryFn: () => departmentApi.getAllDepartment(),
    enabled: !!currentOrganization?.id,
  });

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      sendInvitesTo: "",
      department: "",
      inviteAs: "",
      permission: "",
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      setIsLoading(true);

      if (currentOrganization) {
        const data = {
          organisationId: currentOrganization?.id,
          email: values.sendInvitesTo,
          department: values.department,
          role: values.inviteAs,
          permission: values.permission,
        };

        await api.post("/organisations/invite", data);
        toast.success("Invitation sent successfully!!");
        form.reset({
          sendInvitesTo: "",
          department: "",
          inviteAs: "",
          permission: "",
        });
        router.back();
      }
    } catch (error) {
      toast.error("Failed to send invite");
      console.error("error=>", error);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="h-auto p-4 py-18 md:py-0 mb-[50px] md:mb-0">
      <Button onClick={() => router.back()} variant="ghost">
        <ArrowLeft className="w-4 h-4" />
        Back
      </Button>

      <div className="mx-auto max-w-170">
        <div className="p-6">
          <h1 className="text-2xl font-semibold text-primary-font mb-2">
            # Invite people to{" "}
            {capitalizeFirstLetter(currentOrganization?.name || "")}
          </h1>
          <p className="text-muted-foreground text-sm mb-6">
            Add your teammates to your workspace to help manage tasks,
            contribute to projects, and collaborate across departments.
          </p>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="sendInvitesTo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-brand-primary-font text-sm font-bold">
                      Send invites to
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="<EMAIL>"
                        type="email"
                        {...field}
                        className="text-brand-primary-font text-sm"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-brand-primary-font text-sm font-bold">
                      Department
                    </FormLabel>
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger>
                          <span className="text-brand-primary-font text-sm">
                            {field.value || "Select department"}
                          </span>
                        </SelectTrigger>
                        <SelectContent>
                          {isDepartmentsLoading ? (
                            <SelectItem value="loading" disabled>
                              Loading departments...
                            </SelectItem>
                          ) : departments.length > 0 ? (
                            departments.map((department) => (
                              <SelectItem
                                key={department.id}
                                value={department.name}
                              >
                                {department.name}
                              </SelectItem>
                            ))
                          ) : (
                            <SelectItem value="no-departments" disabled>
                              No departments available
                            </SelectItem>
                          )}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="inviteAs"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-brand-primary-font text-sm font-bold">
                      Invite as
                    </FormLabel>
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger>
                          <span className="text-brand-primary-font text-sm">
                            {field.value || "Select role"}
                          </span>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="member">Member</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="permission"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-brand-primary-font text-sm font-bold">
                      Permission
                    </FormLabel>
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger>
                          <span className="text-brand-primary-font text-sm">
                            {field.value || "Select permission"}
                          </span>
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Executer">Executer</SelectItem>
                          <SelectItem value="Editor">Editor</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end space-x-4 mt-6">
                <PrimaryButton
                  type="submit"
                  className="w-fit px-10"
                  isLoading={isLoading}
                  disabled={isLoading || isDepartmentsLoading}
                >
                  Send
                </PrimaryButton>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default InviteMemberForm;
