"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";

import { <PERSON>Lef<PERSON>, Lock } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import api from "@/services/axios";
import { useRouter } from "next/navigation";

const areaSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(80, "Must not be 80 characters or less"),

  description: z
    .string()
    .max(80, "Must not be 200 characters or less")
    .optional(),
  isPrivate: z.boolean().optional(),
});

const CreateDepartment = () => {
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const form = useForm<z.infer<typeof areaSchema>>({
    resolver: zodResolver(areaSchema),
    defaultValues: {
      name: "",
      description: "",
      isPrivate: false,
    },
  });

  const onSubmit = async (data: z.infer<typeof areaSchema>) => {
    try {
      setLoading(true);
      const finalData = {
        name: data.name,
        description: data.description,
        parent_department_id: null,
        visibility: data.isPrivate ? "PRIVATE" : "PUBLIC",
      };
      await api.post("/organisations/departments", finalData);
      toast.success("Department created successfully!!");
      form.reset();
      router.back();
    } catch (error) {
      toast.error("Failed to create department!!");
      console.error("error=>", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="h-auto p-4 py-18 md:py-0 mb-[50px] md:mb-0">
      {/* modify back button router back */}

      <Button onClick={() => router.back()} variant="ghost">
        <ArrowLeft className="w-4 h-4" />
        Back
      </Button>

      <div className="mx-auto max-w-170">
        <div className="p-6">
          <h2 className="text-xl font-bold text-brand-primary-font mb-2">
            Set Up a New Department
          </h2>
          <p className="text-muted-foreground text-sm mb-6">
            Departments allow you to manage your AI employees, team members, and
            knowledge in a focused space. Name your department, describe its
            purpose, and control who gets access.
          </p>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Name */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="flex flex-col gap-2">
                    <FormLabel className="text-brand-primary-font text-sm font-bold">
                      Name
                    </FormLabel>
                    <FormControl>
                      <Input
                        prefix="#"
                        placeholder="e.g. marketing"
                        {...field}
                        className="text-brand-primary-font text-sm"
                      />
                    </FormControl>
                    <p className="text-text-secondary text-sm ">
                      Names must be lowercase, must be without spaces or
                      periods, and can't be longer than 80 characters.
                    </p>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Instructions */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-brand-primary-font text-sm font-bold">
                      Description
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add any description here..."
                        {...field}
                        className="text-brand-primary-font text-sm bg-white"
                      />
                    </FormControl>
                    <p className="text-text-secondary text-sm mt-1">
                      Description must not be more than 80 characters.
                    </p>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Make Private */}
              <FormField
                control={form.control}
                name="isPrivate"
                render={({ field }) => (
                  <FormItem className="flex items-center justify-between">
                    <div>
                      <FormLabel className=" flex gap-1.5 text-brand-primary-font text-sm font-bold">
                        <Lock className="w-4 h-4" /> Make department private
                      </FormLabel>
                      <p className="text-text-secondary text-sm mt-1">
                        This area can be joined by invite only.
                      </p>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {/* Actions */}
              <div className="flex justify-end gap-4 pt-4">
                <Button
                  onClick={() => router.back()}
                  type="button"
                  variant="outline"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-brand-primary text-white hover:bg-brand-secondary"
                  disabled={loading}
                >
                  {loading ? "Creating..." : "Create"}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default CreateDepartment;
