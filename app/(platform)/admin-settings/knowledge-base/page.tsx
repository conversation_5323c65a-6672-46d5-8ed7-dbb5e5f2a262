"use client";

import { useState } from "react";
import Link from "next/link";
import { PlusCircle, Database, Circle, Info } from "lucide-react";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { Card, CardContent } from "@/components/ui/card";
import Image from "next/image";

import { useQuery, useQueryClient } from "@tanstack/react-query";
import { knowledgeBaseApi } from "@/app/api/knowledgeBase";
import CustomLoader from "@/components/shared/CustomLoader";
import KnowledgeBaseConnector from "./_components/KnowledgeBaseConnector";
import GDriveForm from "./_components/GDriveFrom";
import { useOrgStore } from "@/hooks/use-organization";

const KnowledgeBasePage = () => {
  const queryClient = useQueryClient();
  const [showConnector, setShowConnector] = useState(false);
  const [showGDriveForm, setShowGDriveForm] = useState(false);

  // Use React Query for data fetching
  const {
    data: knowledgeBaseResponse,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ["knowledgeBase"],
    queryFn: () => knowledgeBaseApi.getKnowledgeBaseList(),
    staleTime: 5 * 60 * 1000, // Consider data fresh for 5 minutes
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes
  });

  // Extract data from the response
  const knowledgeBaseData = knowledgeBaseResponse?.sources || [];
  const isInitialMapping = knowledgeBaseResponse?.isInitialMapping || false;
  const { currentOrganization } = useOrgStore();

  const updateKnowledgeBase = async () => {
    // Invalidate and refetch the query
    await queryClient.invalidateQueries({
      queryKey: ["knowledgeBase"],
    });
  };

  const handleConnectorSubmit = (connector: string) => {
    if (connector === "google-drive") {
      setShowConnector(false);
      setShowGDriveForm(true);
    }
  };

  // Handle error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Database className="h-10 w-10 text-muted-foreground" />
        <h2 className="mt-4 text-2xl font-semibold text-red-600">
          Error loading knowledge base
        </h2>
        <p className="text-muted-foreground text-sm mt-2">
          {error instanceof Error ? error.message : "Something went wrong"}
        </p>
        <PrimaryButton className="w-fit mt-6" onClick={() => refetch()}>
          Try Again
        </PrimaryButton>
      </div>
    );
  }

  return (
    <>
      <div className="p-6 md:py-6 py-18 mb-[50px] md:mb-0 max-h-[100vh] overflow-auto">
        {/* Always show header with title at top */}
        <div className="flex md:items-start md:justify-between mb-6 flex-col lg:flex-row items-start justify-items-start gap-2 md:gap-2">
          <div>
            <h1 className="text-xl font-semibold text-brand-primary-font">
              Knowledge Base
            </h1>
            <p className="text-muted-foreground text-sm">
              Collaborate, build, and deploy AI agents securely in one
              workspace.
            </p>
          </div>
          {/* Show Create Knowledge Base button in right corner only when data is present and not loading */}
          {/* {!isLoading && knowledgeBaseData.length > 0 && ( */}
          {currentOrganization?.isAdmin && (
            <PrimaryButton
              className="w-fit"
              onClick={() => setShowConnector(true)}
            >
              <PlusCircle className="mr-1 h-4 w-4" />
              Create knowledge base
            </PrimaryButton>
          )}
          {/* )} */}
        </div>

        {/* Show custom loader when data is loading */}
        {isLoading ? (
          <div className="flex items-center justify-center min-h-[60vh]">
            <CustomLoader loadingText="Loading knowledge base..." />
          </div>
        ) : (
          <>
            {/* When knowledgeBaseData length > 0, show cards */}
            {knowledgeBaseData.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                {knowledgeBaseData.map((item: any, index: number) => (
                  <Link
                    href={`/admin-settings/knowledge-base/${item?.id}`}
                    key={item.id || index}
                  >
                    <Card className="group hover:shadow-lg hover:shadow-brand-primary/10 transition-all duration-300 cursor-pointer border border-brand-stroke rounded-xl bg-brand-card hover:border-brand-primary/30 transform hover:scale-[1.02] py-2">
                      <CardContent className="p-3">
                        {/* Header with Icon and Title */}
                        <div className="flex items-center justify-between mb-2 mr-4">
                          {/* Icon on top left */}
                          <div className="flex items-center justify-center w-15 h-15 rounded-full bg-brand-background border border-brand-stroke group-hover:border-brand-primary/50 transition-colors">
                            {item.type === "GOOGLE_DRIVE" ? (
                              <Image
                                src="/assets/icons/GoogleDriveIcon.svg"
                                alt="Google Drive"
                                width={40}
                                height={40}
                              />
                            ) : (
                              <Circle className="w-5 h-5 text-brand-secondary-font" />
                            )}
                          </div>

                          {/* Title in center */}
                          <div className="flex-1 text-center">
                            <h3 className="text-lg font-semibold text-brand-primary-font group-hover:text-brand-primary transition-colors">
                              {item.name}
                            </h3>
                          </div>

                          {/* Empty space for balance */}
                          <div className="w-10"></div>
                        </div>

                        {/* Created Date - semi bold */}

                        {item.createdAt && (
                          <>
                            <div className="text-center mb-3">
                              <p className="text-sm font-semibold text-brand-secondary-font">
                                Created:{" "}
                                {new Date(item.createdAt).toLocaleDateString(
                                  "en-GB",
                                  {
                                    day: "2-digit",
                                    month: "2-digit",
                                    year: "numeric",
                                  }
                                )}
                              </p>
                            </div>
                          </>
                        )}

                        {/* Conditional Content */}
                        {!isInitialMapping && (
                          <div className="flex items-center gap-2 text-sm text-brand-secondary-font  px-3 py-2">
                            <Info className="w-4 h-4 text-brand-primary flex-shrink-0" />
                            <span className="font-medium">
                              Map Your Data To Department
                            </span>
                          </div>
                        )}

                        {/* Button at bottom */}
                        {!isInitialMapping ? (
                          <PrimaryButton className="w-full py-2 text-sm font-medium bg-brand-primary hover:bg-brand-primary/90 transition-all duration-200 shadow-md hover:shadow-lg mt-4">
                            Manage Access
                          </PrimaryButton>
                        ) : (
                          <PrimaryButton
                            className="w-full py-2 text-sm font-medium bg-brand-primary hover:bg-brand-primary/90 transition-all duration-200 shadow-md hover:shadow-lg"
                            onClick={() => {}}
                          >
                            <Info className="w-4 h-4 mr-2" />
                            View Details
                          </PrimaryButton>
                        )}
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            ) : (
              /* When no knowledge base data is present, show Create Knowledge Base in the middle */

              <div className="flex flex-col gap-8 items-center justify-center mt-20 text-center">
                <Image
                  src="/assets/images/no-files.svg"
                  alt="No invited members"
                  className="text-black"
                  width={180}
                  height={180}
                />
                <div className="flex flex-col gap-4 items-center justify-center">
                  <div className="flex flex-col gap-1">
                    <h3 className="text-xl font-bold text-brand-primary-font">
                      You don&apos;t have any files
                    </h3>
                    <p className="text-muted-foreground">
                      Please add some files to your account
                    </p>
                  </div>
                  <PrimaryButton
                    className="w-fit"
                    onClick={() => setShowConnector(true)}
                  >
                    <PlusCircle className="mr-1 h-4 w-4" />
                    Create Knowledge Base
                  </PrimaryButton>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Connector Selection Modal */}
      <KnowledgeBaseConnector
        open={showConnector}
        onOpenChange={setShowConnector}
        onContinue={(connector: string) => handleConnectorSubmit(connector)}
      />

      {/* Google Drive Form Modal */}
      <GDriveForm
        open={showGDriveForm}
        onBack={() => {
          setShowGDriveForm(false);
          setShowConnector(true);
        }}
        onClose={() => setShowGDriveForm(false)}
        updateKnowledgeBase={updateKnowledgeBase}
      />
    </>
  );
};

export default KnowledgeBasePage;
