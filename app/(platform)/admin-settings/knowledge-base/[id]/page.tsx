"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { toast } from "sonner";

import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import CustomLoader from "@/components/shared/CustomLoader";
import {
  FolderOpen,
  CheckCircle2,
  ChevronDown,
  ChevronUp,
  RefreshCw,
  Lock,
  Globe,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { knowledgeBaseApi } from "@/app/api/knowledgeBase";
import { departmentApi } from "@/app/api/department";
import { useUserStore } from "@/hooks/use-user";
import { useOrgStore } from "@/hooks/use-organization";
import { useQuery } from "@tanstack/react-query";
import { DepartmentList } from "@/shared/interfaces";

interface FolderData {
  id: string;
  name: string;
  existingDepartments?: string[]; // Departments that already have access
}

interface Department extends DepartmentList {
  icon: React.ReactNode;
  color: string;
}

interface FolderPermissions {
  [folderId: string]: string[];
}

const KnowledgeBaseDetails = () => {
  const router = useRouter();
  const { currentOrganization } = useOrgStore();
  const { user } = useUserStore();
  const [folderPermissions, setFolderPermissions] = useState<FolderPermissions>(
    {}
  );
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(
    new Set()
  );
  const [submittingFolders, setSubmittingFolders] = useState<Set<string>>(
    new Set()
  );
  const [isSyncing, setIsSyncing] = useState(false);

  // Use React Query for data fetching folders
  const {
    data: folderData = [],
    isLoading: foldersLoading,
    error: foldersError,
    refetch,
  } = useQuery({
    queryKey: ["knowledgeBaseFolders", currentOrganization?.id],
    queryFn: async () => {
      const orgId = currentOrganization?.id;
      if (!orgId) throw new Error("Organization ID is required");

      // Fetch folders and departments in parallel
      const [foldersResponse, departmentsResponse] = await Promise.all([
        knowledgeBaseApi.getKnowledgeBaseFolders(orgId),
        departmentApi.getAllDepartment(),
      ]);

      const departmentIds = departmentsResponse.map((d: any) => d.id);

      // Get folder access mapping
      const accessResponse = await knowledgeBaseApi.getFolderAccessDepartment(
        orgId,
        departmentIds
      );

      // Create a map of folderId -> Set of departmentIds
      const folderAccessMap = new Map<string, Set<string>>();

      for (const entry of accessResponse) {
        const { departmentId, folders } = entry;

        for (const folder of folders) {
          if (!folderAccessMap.has(folder.id)) {
            folderAccessMap.set(folder.id, new Set());
          }
          folderAccessMap.get(folder.id)!.add(departmentId);
        }
      }

      // Transform folders to include existingDepartments
      const transformedData: FolderData[] = foldersResponse.map(
        (folder: any) => ({
          id: folder.id,
          name: folder.name,
          existingDepartments: Array.from(folderAccessMap.get(folder.id) || []),
        })
      );

      return transformedData;
    },
    enabled: !!currentOrganization?.id,
  });
  // Use React Query for fetching departments
  const {
    data: departmentsData = [],
    isLoading: departmentsLoading,
    error: departmentsError,
  } = useQuery({
    queryKey: ["departments", currentOrganization?.id],
    queryFn: async () => {
      const orgId = currentOrganization?.id;
      if (!orgId) {
        throw new Error("Organization ID is required");
      }

      // Call the department API
      const departmentsResponse = await departmentApi.getAllDepartment();

      // Transform API response
      const transformedDepartments: Department[] = departmentsResponse.map(
        (dept: DepartmentList) => ({
          ...dept,
          icon: null,
          color: "bg-gray-100 text-gray-700 border-gray-200",
        })
      );

      return transformedDepartments;
    },
    enabled: !!currentOrganization?.id, // Only run query when organization ID is available
  });

  const isLoading = foldersLoading || departmentsLoading;
  const error = foldersError || departmentsError;

  // Initialize permissions when folder data changes
  useEffect(() => {
    if (folderData.length > 0) {
      const initialPermissions: FolderPermissions = {};
      folderData.forEach((folder) => {
        initialPermissions[folder.id] = [...(folder.existingDepartments || [])];
      });
      setFolderPermissions(initialPermissions);
    }
  }, [folderData]);

  const handleDepartmentToggle = (folderId: string, departmentId: string) => {
    setFolderPermissions((prev) => ({
      ...prev,
      [folderId]: prev[folderId].includes(departmentId)
        ? prev[folderId].filter((id) => id !== departmentId)
        : [...prev[folderId], departmentId],
    }));
  };

  const toggleFolderExpansion = (folderId: string) => {
    setExpandedFolders((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(folderId)) {
        newSet.delete(folderId);
      } else {
        newSet.add(folderId);
      }
      return newSet;
    });
  };

  const handleFolderContinue = async (folderId: string) => {
    const selectedDepartments = folderPermissions[folderId] || [];

    if (selectedDepartments.length === 0) {
      toast.error("Please select at least one department for this folder");
      return;
    }

    try {
      setSubmittingFolders((prev) => new Set([...prev, folderId]));

      // Prepare payload for batch grant access API
      const payload = {
        departmentData: selectedDepartments.map((departmentId) => ({
          departmentId,
          fileIds: [], // Optional field - empty array for now
          folderIds: [folderId], // Include the current folder ID
        })),
      };

      // Call the batch grant access API
      const result = await knowledgeBaseApi.batchGrantAccess(payload);

      if (result.success) {
        // Show success message
        toast.success(
          result.message ||
            `Access permissions updated successfully for ${
              folderData.find((f) => f.id === folderId)?.name
            }!`
        );

        // If there were any failed departments, show a warning
        if (
          result.failedDepartmentIds &&
          result.failedDepartmentIds.length > 0
        ) {
          toast.warning(
            `Some departments failed to get access: ${result.failedDepartmentIds.join(
              ", "
            )}`
          );
        }

        // Note: You can implement cache updates with React Query's queryClient.setQueryData
        // For now, we'll refetch the data to get updated information
        await refetch();
      } else {
        toast.error(result.message || "Failed to update permissions");
      }
    } catch (error) {
      console.error("Error updating permissions:", error);
      toast.error("Failed to update permissions. Please try again.");
    } finally {
      setSubmittingFolders((prev) => {
        const newSet = new Set(prev);
        newSet.delete(folderId);
        return newSet;
      });
    }
  };

  const handleSyncFolders = async () => {
    if (!currentOrganization?.id && user?.id) {
      return;
    }

    try {
      setIsSyncing(true);

      // Call the sync API
      const result = await knowledgeBaseApi.syncKnowledgeBaseFolder(
        user?.id || "",
        currentOrganization?.id || "",
        true
      );
      toast.success("Folders synced successfully!");
    } catch (error) {
      console.error("Error syncing folders:", error);
      toast.error("Failed to sync folders. Please try again.");
    } finally {
      setIsSyncing(false);
    }
  };

  const getDepartmentById = (id: string) =>
    departmentsData.find((dept) => dept.id === id);

  // Handle error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <FolderOpen className="h-10 w-10 text-muted-foreground" />
        <h2 className="mt-4 text-2xl font-semibold text-red-600">
          Error loading folders
        </h2>
        <p className="text-muted-foreground text-sm mt-2">
          {error instanceof Error ? error.message : "Something went wrong"}
        </p>
        <PrimaryButton className="w-fit mt-6" onClick={() => refetch()}>
          Try Again
        </PrimaryButton>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <CustomLoader loadingText="Loading knowledge base details..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-brand-background p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex lg:items-center gap-4 mb-8 flex-col lg:flex-row mt-[50px] md:mt-0">
          <Link
            href="/admin-settings/knowledge-base"
            onClick={() => router.back()}
            className="text-sm text-brand-primary font-semibold mr-20"
          >
            &larr; Back
          </Link>
          <div className="flex-1">
            <h1 className="text-3xl font-bold text-brand-primary-font">
              Folder Access Management
            </h1>
            <p className="text-brand-secondary-font mt-1">
              Manage department access permissions for each folder
            </p>
          </div>
          {/* Sync Button */}
          <PrimaryButton
            onClick={handleSyncFolders}
            disabled={isSyncing || folderData.length === 0}
            className="w-fit"
          >
            {isSyncing ? (
              <div className="flex items-center gap-2">
                <RefreshCw className="w-4 h-4 animate-spin" />
                Syncing...
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <RefreshCw className="w-4 h-4" />
                Sync Folders
              </div>
            )}
          </PrimaryButton>
        </div>

        {/* Folders with Individual Department Selection */}
        <div className="space-y-4">
          {folderData.map((folder) => {
            const isExpanded = expandedFolders.has(folder.id);
            const isSubmitting = submittingFolders.has(folder.id);
            const selectedDepartments = folderPermissions[folder.id] || [];
            const existingDepartments = folder.existingDepartments || [];
            const hasChanges =
              JSON.stringify(selectedDepartments.sort()) !==
              JSON.stringify(existingDepartments.sort());

            return (
              <Card
                key={folder.id}
                className="bg-brand-card border-brand-stroke"
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-10 h-10 bg-brand-background border border-brand-stroke rounded-lg">
                        <FolderOpen className="w-5 h-5 text-brand-primary" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-brand-primary-font">
                          {folder.name}
                        </h3>
                        <p className="text-sm text-brand-secondary-font hidden lg:block">
                          ID: {folder.id}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3 flex-col-reverse lg:flex-row">
                      {existingDepartments.length > 0 && (
                        <Badge
                          variant="outline"
                          className="text-brand-primary border-brand-primary"
                        >
                          {existingDepartments.length} departments have access
                        </Badge>
                      )}
                      <button
                        onClick={() => toggleFolderExpansion(folder.id)}
                        className="flex items-center gap-1 text-brand-primary hover:text-brand-secondary transition-colors"
                      >
                        <span className="text-sm">
                          {isExpanded ? "Collapse" : "Manage Access"}
                        </span>
                        {isExpanded ? (
                          <ChevronUp className="w-4 h-4" />
                        ) : (
                          <ChevronDown className="w-4 h-4" />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Show existing departments */}
                  {existingDepartments.length > 0 && (
                    <div className="mt-3">
                      <p className="text-sm text-brand-secondary-font mb-2">
                        Current Access:
                      </p>
                      <div className="flex flex-wrap gap-2">
                        {existingDepartments.map((deptId) => {
                          const dept = getDepartmentById(deptId);
                          return dept ? (
                            <div
                              key={deptId}
                              className="flex items-center gap-2 px-3 py-1 rounded-full text-xs bg-gray-100 text-gray-700 border-gray-200"
                            >
                              <span>{dept.name}</span>
                            </div>
                          ) : null;
                        })}
                      </div>
                    </div>
                  )}
                </CardHeader>

                {isExpanded && (
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium text-brand-primary-font mb-3">
                          Select Departments:
                        </h4>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {departmentsData.map((department) => {
                            const isSelected = selectedDepartments.includes(
                              department.id
                            );

                            return (
                              <div
                                key={department.id}
                                className={`flex items-center gap-3 p-3 border rounded-lg cursor-pointer transition-all hover:bg-brand-card-hover ${
                                  isSelected
                                    ? "border-brand-primary bg-brand-clicked"
                                    : "border-brand-stroke bg-brand-background"
                                }`}
                                onClick={() =>
                                  handleDepartmentToggle(
                                    folder.id,
                                    department.id
                                  )
                                }
                              >
                                <Checkbox
                                  checked={isSelected}
                                  onChange={() =>
                                    handleDepartmentToggle(
                                      folder.id,
                                      department.id
                                    )
                                  }
                                  className="flex-shrink-0"
                                />
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center gap-2">
                                    <div className="flex items-center">
                                      {department.visibility === "PUBLIC" ? (
                                        <div title="Public Department">
                                          <Globe className="w-4 h-4 text-green-600" />
                                        </div>
                                      ) : (
                                        <div title="Private Department">
                                          <Lock className="w-4 h-4 text-red-600" />
                                        </div>
                                      )}
                                    </div>
                                    <h5 className="font-medium text-brand-primary-font text-sm truncate">
                                      {department.name}
                                    </h5>
                                  </div>
                                  <p className="text-xs text-brand-secondary-font truncate">
                                    {department.description}
                                  </p>
                                </div>
                                {isSelected && (
                                  <CheckCircle2 className="w-4 h-4 text-brand-primary flex-shrink-0" />
                                )}
                              </div>
                            );
                          })}
                        </div>
                      </div>

                      {/* Individual Continue Button */}
                      <div className="flex items-center justify-between pt-4 border-t border-brand-stroke">
                        <div className="text-sm text-brand-secondary-font">
                          {selectedDepartments.length > 0 ? (
                            <>
                              {selectedDepartments.length} departments selected
                              {hasChanges && (
                                <span className="text-brand-primary ml-2">
                                  • Changes pending
                                </span>
                              )}
                            </>
                          ) : (
                            "No departments selected"
                          )}
                        </div>
                        <PrimaryButton
                          onClick={() => handleFolderContinue(folder.id)}
                          disabled={
                            selectedDepartments.length === 0 || isSubmitting
                          }
                          className="w-fit"
                        >
                          {isSubmitting ? (
                            <div className="flex items-center gap-2">
                              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                              Updating...
                            </div>
                          ) : (
                            "Continue"
                          )}
                        </PrimaryButton>
                      </div>
                    </div>
                  </CardContent>
                )}
              </Card>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default KnowledgeBaseDetails;
