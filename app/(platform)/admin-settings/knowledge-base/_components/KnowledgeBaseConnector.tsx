"use client";

import React from "react";
import Image from "next/image";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Label } from "@/components/ui/label";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import * as z from "zod";

const dataSources = [
  { name: "<PERSON>ra", disabled: true, icon: "/assets/icons/Jira.svg" },
  { name: "Confluence", disabled: true, icon: "/assets/icons/Confluence.svg" },
  { name: "Slack", disabled: true, icon: "/assets/icons/SlackIcon.svg" },
  {
    name: "Google Calendar",
    disabled: true,
    icon: "/assets/icons/GoogleCalendar.svg",
  },
  { name: "Gmail", disabled: true, icon: "/assets/icons/GmailIcon.svg" },
  { name: "Notion", disabled: true, icon: "/assets/icons/Notion.svg" },
];

const formSchema = z.object({
  connector: z.string().min(1, "Select a data source"),
});

type FormValues = z.infer<typeof formSchema>;

const KnowledgeBaseConnector = ({
  open,
  onOpenChange,
  onContinue,
}: {
  open: boolean;
  onOpenChange: (value: boolean) => void;
  onContinue: (connector: string) => void;
}) => {
  const { handleSubmit, watch, setValue } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      connector: "",
    },
  });

  const selectedSource = watch("connector");
  const isFormValid = !!selectedSource;

  const onSubmit = (data: FormValues) => {
    onContinue(data.connector);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="md:max-w-xl rounded-2xl bg-card max-h-[600px] overflow-auto md:max-h-none">
        <DialogHeader className="mb-4">
          <DialogTitle className="text-xl font-semibold text-brand-primary-font">
            Import data from third party
          </DialogTitle>
          <DialogDescription className="text-sm text-brand-secondary-font">
            Import data from external sources as knowledge.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {/* Google Drive Connector */}
            <div
              onClick={() => setValue("connector", "google-drive")}
              className={`flex items-center p-3 border rounded-lg space-x-3 transition cursor-pointer bg-card hover:bg-brand-card-hover ${
                selectedSource === "google-drive"
                  ? "ring-2 ring-brand-primary"
                  : ""
              }`}
            >
              <Image
                src="/assets/icons/GoogleDriveIcon.svg"
                alt="Google Drive"
                width={20}
                height={20}
              />
              <span className="font-medium text-sm text-brand-primary-font">
                Google Drive
              </span>
            </div>

            {/* Disabled Connectors */}
            {dataSources.map((item, i) => (
              <div
                key={i}
                className="flex items-center p-3 border rounded-lg space-x-3 opacity-40 cursor-not-allowed bg-card"
              >
                <Image src={item.icon} alt={item.name} width={24} height={24} />
                <Label
                  htmlFor={item.name}
                  className="font-medium text-sm text-brand-primary-font cursor-not-allowed"
                >
                  {item.name}
                </Label>
              </div>
            ))}
          </div>

          {/* Continue Button */}
          <PrimaryButton
            type="submit"
            className="mt-6 w-full"
            disabled={!isFormValid}
          >
            Continue
          </PrimaryButton>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default KnowledgeBaseConnector;
