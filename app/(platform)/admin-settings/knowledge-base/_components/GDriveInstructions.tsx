"use client";

import React from "react";
import Link from "next/link";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ArrowLeft, BookOpen } from "lucide-react";

const GDriveInstructions = ({
  open,
  onBack,
}: {
  open: boolean;
  onBack: () => void;
}) => {
  return (
    <Dialog open={open} onOpenChange={onBack}>
      <DialogContent className="sm:max-w-4xl rounded-2xl bg-card max-h-[90vh] overflow-y-auto">
        <DialogHeader className="mb-6">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-brand-clicked">
              <BookOpen className="w-5 h-5 text-brand-primary" />
            </div>
            <div>
              <DialogTitle className="text-xl font-satoshi-bold text-brand-primary-font">
                Google Drive Setup Instructions
              </DialogTitle>
              <DialogDescription className="text-sm text-brand-secondary-font mt-1">
                Follow these steps to create a service account and connect your
                Google Drive
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-8">
          {/* Part 1 */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-brand-primary text-brand-white-text flex items-center justify-center font-satoshi-bold text-sm">
                1
              </div>
              <h3 className="text-lg font-satoshi-bold text-brand-primary-font">
                Create Google Cloud Project & Enable API
              </h3>
            </div>
            <div className="ml-11 space-y-3">
              <div className="p-4 rounded-lg bg-color-light border border-brand-stroke">
                <ul className="space-y-2 text-sm text-brand-primary-font">
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-primary mt-2 flex-shrink-0"></span>
                    <span>
                      As the Admin of your Ruh AI workspace, sign in to{" "}
                      <Link
                        href="https://console.cloud.google.com/"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-brand-primary underline underline-offset-2"
                      >
                        <strong>Google Cloud Console</strong>
                      </Link>{" "}
                      using the same email ID you&apos;ll use for Ruh AI.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-primary mt-2 flex-shrink-0"></span>
                    <span>
                      In the top bar, click the{" "}
                      <strong>Project dropdown</strong> (or press Ctrl + O) to
                      open the Project Picker.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-primary mt-2 flex-shrink-0"></span>
                    <span>
                      Create a new project, or select an existing one.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-primary mt-2 flex-shrink-0"></span>
                    <span>
                      Open the navigation menu, go to{" "}
                      <strong>APIs & Services → Dashboard</strong>.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-primary mt-2 flex-shrink-0"></span>
                    <span>
                      Click{" "}
                      <strong>&quot;Enable APIs and Services&quot;</strong> at
                      the top.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-primary mt-2 flex-shrink-0"></span>
                    <span>
                      In the API Library, search for{" "}
                      <strong>Google Drive API</strong> and click Enable.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Part 2 */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-brand-secondary text-brand-white-text flex items-center justify-center font-satoshi-bold text-sm">
                2
              </div>
              <h3 className="text-lg font-satoshi-bold text-brand-primary-font">
                Create a Service Account
              </h3>
            </div>
            <div className="ml-11 space-y-3">
              <div className="p-4 rounded-lg bg-color-light-secondary border border-brand-stroke">
                <ul className="space-y-2 text-sm text-brand-primary-font">
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-secondary mt-2 flex-shrink-0"></span>
                    <span>
                      In the navigation menu, hover over{" "}
                      <strong>IAM & Admin</strong>, then click{" "}
                      <strong>Service Accounts</strong>.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-secondary mt-2 flex-shrink-0"></span>
                    <span>
                      Click <strong>&quot;Create Service Account&quot;</strong>.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-secondary mt-2 flex-shrink-0"></span>
                    <span>
                      Enter a name (e.g.,{" "}
                      <code className="px-2 py-1 bg-brand-card-hover rounded text-xs font-satoshi-medium">
                        ruh-ai-knowledge-access
                      </code>
                      ) and click <strong>Create and Continue</strong>.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-secondary mt-2 flex-shrink-0"></span>
                    <span>
                      Click on <strong>&apos;Done&apos;</strong> below.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Part 3 */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-brand-tertiary text-brand-white-text flex items-center justify-center font-satoshi-bold text-sm">
                3
              </div>
              <h3 className="text-lg font-satoshi-bold text-brand-primary-font">
                Generate Service Account Key
              </h3>
            </div>
            <div className="ml-11 space-y-3">
              <div className="p-4 rounded-lg bg-color-light-tertiary border border-brand-stroke">
                <ul className="space-y-2 text-sm text-brand-primary-font">
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-tertiary mt-2 flex-shrink-0"></span>
                    <span>Click on the newly created Service Account.</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-tertiary mt-2 flex-shrink-0"></span>
                    <span>
                      Go to the <strong>Keys</strong> tab.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-tertiary mt-2 flex-shrink-0"></span>
                    <span>
                      Click{" "}
                      <strong>
                        &quot;Add Key&quot; → &quot;Create new key&quot;
                      </strong>
                      .
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-tertiary mt-2 flex-shrink-0"></span>
                    <span>
                      Keep <strong>JSON</strong> selected and click Create.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-tertiary mt-2 flex-shrink-0"></span>
                    <span>
                      A{" "}
                      <code className="px-2 py-1 bg-brand-card-hover rounded text-xs font-satoshi-medium">
                        .json
                      </code>{" "}
                      key file will be downloaded automatically.{" "}
                      <strong>Keep this file safe</strong> — you&apos;ll need it
                      in Ruh AI.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Part 4 */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-brand-primary text-brand-white-text flex items-center justify-center font-satoshi-bold text-sm">
                4
              </div>
              <h3 className="text-lg font-satoshi-bold text-brand-primary-font">
                Give Folder Access on Google Drive
              </h3>
            </div>
            <div className="ml-11 space-y-3">
              <div className="p-4 rounded-lg bg-color-light border border-brand-stroke">
                <ul className="space-y-2 text-sm text-brand-primary-font">
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-primary mt-2 flex-shrink-0"></span>
                    <span>
                      Open <strong>Google Drive</strong> using the same email.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-primary mt-2 flex-shrink-0"></span>
                    <span>
                      Create a new folder (or use an existing one) to act as
                      your Knowledge Base.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-primary mt-2 flex-shrink-0"></span>
                    <span>
                      Right-click the folder and select <strong>Share</strong>.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-primary mt-2 flex-shrink-0"></span>
                    <span>
                      In the share dialog, add the email for the service account
                      you just created and give it{" "}
                      <strong>Read/Editor access</strong>. At the very least,
                      Read access will be required.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-primary mt-2 flex-shrink-0"></span>
                    <span>
                      It will look something like{" "}
                      <code className="px-2 py-1 bg-brand-card-hover rounded text-xs font-satoshi-medium">
                        <EMAIL>
                      </code>
                      .
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Part 5 */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 rounded-full bg-brand-secondary text-brand-white-text flex items-center justify-center font-satoshi-bold text-sm">
                5
              </div>
              <h3 className="text-lg font-satoshi-bold text-brand-primary-font">
                Connect in Ruh AI
              </h3>
            </div>
            <div className="ml-11 space-y-3">
              <div className="p-4 rounded-lg bg-color-light-secondary border border-brand-stroke">
                <ul className="space-y-2 text-sm text-brand-primary-font">
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-secondary mt-2 flex-shrink-0"></span>
                    <span>
                      In Ruh AI&apos;s <strong>Admin Panel → Knowledge</strong>,
                      click on <strong>&apos;Create&apos;</strong> and select{" "}
                      <strong>Google Drive</strong> as the option.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-secondary mt-2 flex-shrink-0"></span>
                    <span>
                      In the Google Drive knowledge modal, enter the name of the
                      Google Drive folder.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-secondary mt-2 flex-shrink-0"></span>
                    <span>
                      Upload the <strong>Service Account JSON key file</strong>{" "}
                      you downloaded earlier.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-secondary mt-2 flex-shrink-0"></span>
                    <span>
                      (Optional) Provide the <strong>Folder ID</strong> for more
                      precise access.
                    </span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-secondary mt-2 flex-shrink-0"></span>
                    <span>Tick all the required checkboxes.</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <span className="w-1.5 h-1.5 rounded-full bg-brand-secondary mt-2 flex-shrink-0"></span>
                    <span>
                      Click <strong>Create</strong>.
                    </span>
                  </li>
                </ul>
              </div>
            </div>
          </div>

          {/* Important Note */}
          <div className="p-4 rounded-lg bg-background-accent border border-brand-primary/20">
            <div className="flex items-start gap-3">
              <div className="w-6 h-6 rounded-full bg-brand-primary/10 flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-brand-primary text-xs font-satoshi-bold">
                  !
                </span>
              </div>
              <div>
                <h4 className="font-satoshi-bold text-brand-primary-font text-sm mb-1">
                  Important Note
                </h4>
                <p className="text-sm text-brand-secondary-font">
                  Make sure to keep your service account JSON key file secure
                  and never share it publicly. This file contains sensitive
                  credentials that provide access to your Google Drive.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Back Button */}
        <div className="flex justify-start mt-8 pt-6 border-t border-brand-stroke">
          <Button
            type="button"
            variant="outline"
            onClick={onBack}
            className="flex items-center gap-2 font-satoshi-medium"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Form
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default GDriveInstructions;
