"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Tabs,
  TabsContent /* <PERSON><PERSON>List, TabsTrigger */,
} from "@/components/ui/tabs";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import { LoaderIcon } from "lucide-react";
import { HelpCircle, Upload, X } from "lucide-react";
import api from "@/services/axios";
import { useOrgStore } from "@/hooks/use-organization";
import GDriveInstructions from "./GDriveInstructions";

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  folderId: z
    .string()
    .optional()
    .refine(
      (val) => !val || /^[a-zA-Z0-9_-]{10,}$/.test(val), // basic validation for GDrive folder ID
      {
        message: "Invalid Google Drive Folder ID format",
      }
    ),
  jsonFile: z
    .any()
    .refine((file) => file instanceof File && file.name.endsWith(".json"), {
      message: "Valid JSON file is required",
    }),
  checklist1: z
    .boolean()
    .refine((val) => val === true, { message: "Please confirm point 1" }),
  checklist2: z
    .boolean()
    .refine((val) => val === true, { message: "Please confirm point 2" }),
  checklist3: z
    .boolean()
    .refine((val) => val === true, { message: "Please confirm point 3" }),
});

type FormValues = z.infer<typeof formSchema>;

const GDriveForm = ({
  open,
  onBack,
  onClose,
  updateKnowledgeBase,
}: {
  open: boolean;
  onBack: () => void;
  onClose: () => void;
  updateKnowledgeBase: () => Promise<void>;
}) => {
  const { currentOrganization } = useOrgStore();
  const [showInstructions, setShowInstructions] = useState(false);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("service-account");
  const [agreeToOthTerms, setAgreeToOthTerms] = useState(false);
  const [oAuthLoading, setOAuthLoading] = useState(false);
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: {
      name: "",
      folderId: "",
      checklist1: false,
      checklist2: false,
      checklist3: false,
    },
  });

  const onSubmit = async (data: FormValues) => {
    // Add logic to send data.jsonFile and folderId to backend
    try {
      if (currentOrganization) {
        setLoading(true);
        const text = await data?.jsonFile.text();
        const formData = {
          organisationId: currentOrganization?.id,
          type: "GOOGLE_DRIVE",
          name: data.name,
          key: text,
        };

        const result = await api.post(`/organisations/sources`, formData);

        if (result) {
          toast.success("Source Added Successfully");
          await updateKnowledgeBase();
          onClose();
        }
      }
    } catch (error: any) {
      const errorMsg =
        error.response?.data?.detail ||
        error.response?.data?.message ||
        "Failed to add source";
      toast.error(errorMsg, {
        duration: 4000,
        position: "top-center",
      });
      console.error("error=>", error);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleOAuth = async () => {
    try {
      setOAuthLoading(true);
      const response = await api.post("/google-drive/oauth/initiate", {});
      if (response?.data?.success && response?.data?.oauth_url) {
        window.location.href = response.data.oauth_url;
      } else {
        toast.error("Failed to initiate Google authentication");
      }
    } catch (error: any) {
      const errorMsg =
        error?.response?.data?.detail ||
        error?.response?.data?.message ||
        "Failed to initiate Google authentication";
      toast.error(errorMsg, {
        duration: 4000,
        position: "top-center",
      });
      console.error("Error:", error);
    } finally {
      setOAuthLoading(false);
    }
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-xl rounded-2xl bg-card h-[600px] overflow-y-auto">
          <DialogHeader className="mb-4">
            <div className="flex items-start justify-start flex-col">
              <DialogTitle className="text-xl font-semibold text-brand-primary-font">
                Google Drive
              </DialogTitle>
              <DialogDescription className="text-sm text-brand-secondary-font text-left">
                We will be accessing the content of the folder you specify
                below.
              </DialogDescription>
            </div>
          </DialogHeader>

          <Tabs
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full"
          >
            {/* <TabsList className="grid w-full grid-cols-2 mb-6 bg-brand-background rounded-lg">
              <TabsTrigger
                value="google-auth"
                className="data-[state=active]:bg-brand-primary data-[state=active]:text-white rounded-md"
              >
                Using Google OAuth
              </TabsTrigger>
              <TabsTrigger
                value="service-account"
                className="data-[state=active]:bg-brand-primary data-[state=active]:text-white rounded-md"
              >
                Using Service Account
              </TabsTrigger>
            </TabsList> */}

            <TabsContent value="service-account">
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
                {/* Setup Guide Link */}
                <div className="flex justify-start mb-4">
                  <button
                    type="button"
                    onClick={() => setShowInstructions(true)}
                    className="text-brand-primary hover:text-brand-primary/80 underline text-sm inline-flex items-center gap-1"
                  >
                    <HelpCircle className="w-4 h-4" />
                    Need help? View the service account setup guide
                  </button>
                </div>

                {/* Name */}
                <div>
                  <Label
                    htmlFor="name"
                    className="text-brand-primary-font text-sm font-bold"
                  >
                    Name<span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="name"
                    placeholder="Enter a name"
                    {...register("name")}
                    className="text-brand-primary-font text-sm mt-2"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-500 mt-1">
                      {errors.name.message}
                    </p>
                  )}
                </div>

                {/* JSON File Upload */}
                <div>
                  <Label
                    htmlFor="jsonFile"
                    className="text-brand-primary-font text-sm font-bold"
                  >
                    Upload Service Account Key (JSON)
                    <span className="text-red-500">*</span>
                  </Label>

                  <div className="mt-2 space-y-3">
                    <input
                      id="jsonFile"
                      type="file"
                      accept=".json"
                      onChange={(e) => {
                        if (e.target.files?.[0]) {
                          setValue("jsonFile", e.target.files[0], {
                            shouldValidate: true,
                          });
                        }
                      }}
                      className="hidden"
                    />

                    {/* File Display Container - Only show when file is selected */}
                    {watch("jsonFile") && (
                      <div className="flex items-center justify-between px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg">
                        <span className="text-sm text-brand-primary-font">
                          {watch("jsonFile")?.name}
                        </span>
                        <button
                          type="button"
                          onClick={() => {
                            setValue("jsonFile", undefined, {
                              shouldValidate: true,
                            });
                            // Reset the file input
                            const fileInput = document.getElementById(
                              "jsonFile"
                            ) as HTMLInputElement;
                            if (fileInput) {
                              fileInput.value = "";
                            }
                          }}
                          className="flex items-center justify-center w-5 h-5 text-gray-500 hover:text-gray-700 transition-colors"
                          aria-label="Remove file"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </div>
                    )}

                    {/* Upload Button - Always visible */}
                    <div className="flex flex-col items-start gap-2">
                      <label
                        htmlFor="jsonFile"
                        className="inline-flex items-center justify-center gap-2 px-4 py-2 bg-white text-brand-primary border border-brand-primary rounded-lg cursor-pointer text-sm font-medium transition-all duration-200 hover:font-semibold hover:border-2"
                      >
                        <Upload className="w-4 h-4" />
                        Upload
                      </label>

                      {/* "No file chosen" text - Only show when no file is selected */}
                      {!watch("jsonFile") && (
                        <span className="text-sm text-gray-500">
                          No file chosen
                        </span>
                      )}
                    </div>
                  </div>

                  {errors.jsonFile && (
                    <p className="text-sm text-red-500 mt-1">
                      {errors.jsonFile.message?.toString()}
                    </p>
                  )}
                </div>

                {/* Folder ID */}
                <div>
                  <Label
                    htmlFor="folderId"
                    className="text-brand-primary-font text-sm font-bold"
                  >
                    Folder ID
                  </Label>
                  <Input
                    id="folderId"
                    placeholder="Enter Google Drive Folder ID"
                    {...register("folderId")}
                    className="text-brand-primary-font text-sm mt-2"
                  />
                  {errors.folderId && (
                    <p className="text-sm text-red-500 mt-1">
                      {errors.folderId.message}
                    </p>
                  )}
                </div>

                <div className="space-y-4">
                  <h3 className="text-sm font-bold text-brand-primary-font">
                    Terms & Conditions <span className="text-red-500">*</span>
                  </h3>

                  <div className="space-y-3">
                    <div className="flex items-start gap-3">
                      <Checkbox
                        id="checklist1"
                        checked={watch("checklist1")}
                        onCheckedChange={(val) =>
                          setValue("checklist1", val === true, {
                            shouldValidate: true,
                          })
                        }
                        className="mt-0.5"
                      />
                      <Label
                        htmlFor="checklist1"
                        className="text-sm text-brand-secondary-font leading-relaxed"
                      >
                        Confirm that the service account has been provided with
                        read access to the designated folder ID.
                      </Label>
                    </div>
                    {errors.checklist1 && (
                      <p className="text-sm text-red-500 ml-6">
                        {errors.checklist1.message}
                      </p>
                    )}

                    <div className="flex items-start gap-3">
                      <Checkbox
                        id="checklist2"
                        checked={watch("checklist2")}
                        onCheckedChange={(val) =>
                          setValue("checklist2", val === true, {
                            shouldValidate: true,
                          })
                        }
                        className="mt-0.5"
                      />
                      <Label
                        htmlFor="checklist2"
                        className="text-sm text-brand-secondary-font leading-relaxed"
                      >
                        Ensure that the Google Drive API is activated in the
                        Google Cloud Console.
                      </Label>
                    </div>
                    {errors.checklist2 && (
                      <p className="text-sm text-red-500 ml-6">
                        {errors.checklist2.message}
                      </p>
                    )}

                    <div className="flex items-start gap-3">
                      <Checkbox
                        id="checklist3"
                        checked={watch("checklist3")}
                        onCheckedChange={(val) =>
                          setValue("checklist3", val === true, {
                            shouldValidate: true,
                          })
                        }
                        className="mt-0.5"
                      />
                      <Label
                        htmlFor="checklist3"
                        className="text-sm text-brand-secondary-font leading-relaxed"
                      >
                        Please confirm that you understand the conditions
                        mentioned above.
                      </Label>
                    </div>
                    {errors.checklist3 && (
                      <p className="text-sm text-red-500 ml-6">
                        {errors.checklist3.message}
                      </p>
                    )}
                  </div>
                </div>

                {/* Buttons */}
                <div className="flex justify-between mt-6">
                  <Button type="button" variant="outline" onClick={onBack}>
                    Back
                  </Button>
                  <PrimaryButton
                    type="submit"
                    className="w-fit"
                    disabled={!isValid}
                  >
                    {loading ? (
                      <LoaderIcon className="animate-spin" />
                    ) : (
                      "Import Data"
                    )}
                  </PrimaryButton>
                </div>
              </form>
            </TabsContent>

            <TabsContent
              value="google-auth"
              className="flex flex-col min-h-[400px] space-y-6 p-4"
            >
              <div className="space-y-6">
                <div>
                  <h3 className="text-sm font-bold text-brand-primary-font mb-2">
                    Instructions
                  </h3>
                  <ul className="list-disc pl-5 space-y-2 text-sm text-brand-secondary-font">
                    <li>
                      You will be redirected to Google Sign-in page to
                      authenticate.
                    </li>
                    <li>
                      Select the specific folders or files you want to use as
                      knowledge base.
                    </li>
                    <li>
                      The selected content will be indexed for your AI
                      assistant.
                    </li>
                  </ul>
                </div>

                <div className="flex items-start gap-3">
                  <Checkbox
                    id="agreeTerms"
                    checked={agreeToOthTerms}
                    onCheckedChange={(val) => setAgreeToOthTerms(val === true)}
                    className="mt-0.5"
                  />
                  <Label
                    htmlFor="agreeTerms"
                    className="text-sm text-brand-secondary-font leading-relaxed"
                  >
                    I understand that the selected Google Drive content will be
                    processed and used to enhance the AI assistant&apos;s
                    knowledge base.
                  </Label>
                </div>
              </div>

              <div className="flex justify-center">
                <PrimaryButton
                  type="button"
                  className="w-fit"
                  onClick={() => handleGoogleOAuth()}
                  disabled={!agreeToOthTerms || oAuthLoading}
                  isLoading={oAuthLoading}
                >
                  Add Source
                </PrimaryButton>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      <GDriveInstructions
        open={showInstructions}
        onBack={() => setShowInstructions(false)}
      />
    </>
  );
};

export default GDriveForm;
