"use client";

import React from "react";
import Link from "next/link";
import { PrimaryButton } from "@/components/shared/PrimaryButton";

import MemberList from "../_components/MemberList";
import { useSidebarStore } from "@/hooks/use-sidebar";
import { useOrgStore } from "@/hooks/use-organization";

const ManageMembers = () => {
  const { isSecondaryBarOpen } = useSidebarStore();
  const { currentOrganization } = useOrgStore();
  return (
    <div
      className={`p-6 md:py-6 py-18 mb-[50px] md:mb-0 ${isSecondaryBarOpen ? "md:max-w-[calc(100vw-319px)]" : "md:max-w-[calc(100vw-75px)] xl:w-full"} xl:max-w-full max-h-[100vh] overflow-auto`}
    >
      <div className="flex md:items-start md:justify-between mb-6 flex-col xl:flex-row items-start justify-items-start gap-2 md:gap-2">
        <div>
          <h2 className="text-xl font-semibold text-brand-primary-font">
            Manage Members
          </h2>
          <p className="text-muted-foreground text-sm w-[100%] md:w-[70%]">
            Invite external collaborators and team members into your workspace.
            Assign roles, manage access, and work together seamlessly to build,
            deploy, and refine your AI-powered workflows.
          </p>
        </div>

        {currentOrganization?.isAdmin && (
          <Link href="/admin-settings/invite-member">
            <PrimaryButton>Invite Members</PrimaryButton>
          </Link>
        )}
      </div>

      <MemberList />
    </div>
  );
};

export default ManageMembers;
