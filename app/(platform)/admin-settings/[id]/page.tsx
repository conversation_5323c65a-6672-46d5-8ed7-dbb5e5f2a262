"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import { useQuery } from "@tanstack/react-query";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Users, Bot, BookText } from "lucide-react";
import Members from "./_components/Members";
import Agents from "./_components/Agents";
import KnowledgeBase from "./_components/KnowledgeBase";

import { useOrgStore } from "@/hooks/use-organization";
import { departmentApi } from "@/app/api/department";
import { knowledgeBaseApi } from "@/app/api/knowledgeBase";
import { organizationApi } from "@/app/api/organization";
import { useIsMobile } from "@/hooks/use-mobile";
import { useSidebarStore } from "@/hooks/use-sidebar";

const DepartmentDetails = () => {
  const { currentOrganization } = useOrgStore();
  const params = useParams();
  const departmentId = params?.id as string;
  const [tab, setTab] = useState("members");
  const [page, setPage] = useState(1);
  const pageSize = 10;
  const router = useRouter();
  const isMobile = useIsMobile();
  const { isSecondaryBarOpen } = useSidebarStore();

  // Fetch department members using useQuery
  const { data: orgInfo = {}, isLoading: membersLoading } = useQuery({
    queryKey: ["departmentMembers", departmentId, page, pageSize],
    queryFn: () =>
      departmentApi.getDepartmentMembers(departmentId, page, pageSize),
    enabled: !!departmentId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch department folders using useQuery
  const {
    data: foldersData = [],
    isLoading: foldersLoading,
    error: foldersError,
  } = useQuery({
    queryKey: ["departmentFolders", currentOrganization?.id, departmentId],
    queryFn: () => {
      if (!currentOrganization?.id)
        throw new Error("Organization ID not found");
      return knowledgeBaseApi.getFolderAccessDepartment(
        currentOrganization.id,
        [departmentId]
      );
    },
    enabled: !!(currentOrganization && currentOrganization?.id && departmentId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Extract folders for the current department
  const departmentFolders =
    foldersData?.find((dept: any) => dept.departmentId === departmentId)
      ?.folders || [];

  // Fetch department agents using useQuery
  const {
    data: agentsData,
    isLoading: agentsLoading,
    error: agentsError,
  } = useQuery({
    queryKey: ["departmentAgents", currentOrganization?.id, departmentId],
    queryFn: () => {
      if (!currentOrganization?.id)
        throw new Error("Organization ID not found");
      return organizationApi.getDepartmentAgents(departmentId);
    },
    enabled: !!(currentOrganization && currentOrganization?.id && departmentId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  return (
    <div
      className={`md:px-6 md:py-6 p-4 max-h-[100vh] overflow-auto mt-[50px] md:mt-0 ${isSecondaryBarOpen ? "md:max-w-[calc(100vw-319px)]" : "md:max-w-[calc(100vw-75px)] xl:w-full"}`}
    >
      <Link
        href="/admin-settings/knowledge-base"
        onClick={() => router.back()}
        className="text-sm text-brand-primary font-semibold mr-20"
      >
        &larr; Back
      </Link>

      <h2 className="text-xl font-bold text-brand-primary-font mb-2 mt-4">
        {`# ${orgInfo?.dept_name || ""}`}
      </h2>
      <p className="text-muted-foreground text-sm mb-6">
        {`${orgInfo?.dept_desc || ""}`}
      </p>

      <Tabs value={tab} onValueChange={setTab} className="w-full">
        <TabsList className="bg-transparent p-0 border-b border-border w-auto sm:w-min md:w-min overflow-auto scrollbar-hide justify-start">
          <TabsTrigger
            value="members"
            className="data-[state=active]:border-b-2 data-[state=active]:text-brand-primary border-brand-primary rounded-none text-sm "
          >
            <Users className="w-4 h-4 mr-1" /> Members
          </TabsTrigger>
          <TabsTrigger
            value="agents"
            className="data-[state=active]:border-b-2 data-[state=active]:text-brand-primary border-brand-primary rounded-none text-sm px-4"
          >
            <Bot className="w-4 h-4 mr-1" /> AI Employees
          </TabsTrigger>
          <TabsTrigger
            value="knowledge"
            className="data-[state=active]:border-b-2 data-[state=active]:text-brand-primary border-brand-primary rounded-none text-sm px-4"
          >
            <BookText className="w-4 h-4 mr-1" /> Knowledge Base
          </TabsTrigger>
        </TabsList>

        <TabsContent value="members">
          <Members
            dataLoading={membersLoading}
            memberList={orgInfo?.users || []}
            totalCount={orgInfo?.totalCount || 0}
            currentPage={page}
            pageSize={pageSize}
            onPageChange={setPage}
          />
        </TabsContent>
        <TabsContent value="agents">
          <Agents
            agents={agentsData?.agents || []}
            loading={agentsLoading}
            error={agentsError}
          />
        </TabsContent>
        <TabsContent value="knowledge">
          <KnowledgeBase
            folders={departmentFolders}
            loading={foldersLoading}
            error={foldersError}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DepartmentDetails;
