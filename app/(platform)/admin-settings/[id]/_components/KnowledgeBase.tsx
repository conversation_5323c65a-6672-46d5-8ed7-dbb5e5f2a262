"use client";

import { <PERSON>older, Trash2, BrainCircuit } from "lucide-react";
import { Button } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/shared/LoadingSpinner";

interface FolderData {
  id: string;
  name: string;
}

interface KnowledgeBaseProps {
  folders: FolderData[];
  loading: boolean;
  error: Error | null;
}

const KnowledgeBase = ({ folders, loading, error }: KnowledgeBaseProps) => {
  const handleDeleteFolder = async (folderId: string) => {};

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-error mb-2">Failed to load knowledge base</div>
        <div className="text-sm text-muted-foreground">{error.message}</div>
      </div>
    );
  }

  return (
    <div className="mt-6">
      {folders.length === 0 ? (
        <div className="text-center py-12">
          <BrainCircuit className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-brand-primary-font mb-2">
            No knowledge base found
          </h3>
          <p className="text-sm text-muted-foreground">
            This department doesn&apos;t have any knowledge base folders
            assigned yet.
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {folders.map((folder) => (
            <div
              key={folder.id}
              className="flex items-center justify-between p-4 border border-border rounded-lg bg-background hover:bg-muted/30 transition-colors"
            >
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-color-light rounded-lg flex items-center justify-center">
                  <Folder className="w-5 h-5 text-brand-primary" />
                </div>
                <div>
                  <h3 className="font-semibold text-brand-primary-font">
                    {folder.name}
                  </h3>
                  <p className="text-sm text-muted-foreground hidden lg:block">
                    ID: {folder.id}
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleDeleteFolder(folder.id)}
                className="text-error hover:text-error hover:bg-background-muted border-border-muted"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default KnowledgeBase;
