import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";

interface MemberPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function MemberPagination({
  currentPage,
  totalPages,
  onPageChange,
}: MemberPaginationProps) {
  const renderPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    
    // Always show first page
    pages.push(
      <Button
        key={1}
        variant={currentPage === 1 ? "default" : "outline"}
        size="sm"
        onClick={() => onPageChange(1)}
        className={`min-w-[32px] ${
          currentPage === 1 ? "bg-brand-primary hover:bg-brand-primary/90" : ""
        }`}
      >
        1
      </Button>
    );

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is less than max visible
      for (let i = 2; i <= totalPages; i++) {
        pages.push(
          <Button
            key={i}
            variant={currentPage === i ? "default" : "outline"}
            size="sm"
            onClick={() => onPageChange(i)}
            className={`min-w-[32px] ${
              currentPage === i ? "bg-brand-primary hover:bg-brand-primary/90" : ""
            }`}
          >
            {i}
          </Button>
        );
      }
    } else {
      // Complex pagination with ellipsis
      const startPage = Math.max(2, currentPage - 1);
      const endPage = Math.min(totalPages - 1, currentPage + 1);

      if (currentPage > 3) {
        pages.push(
          <Button
            key="start-ellipsis"
            variant="ghost"
            size="sm"
            disabled
            className="min-w-[32px] cursor-default"
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        );
      }

      // Show pages around current page
      for (let i = startPage; i <= endPage; i++) {
        pages.push(
          <Button
            key={i}
            variant={currentPage === i ? "default" : "outline"}
            size="sm"
            onClick={() => onPageChange(i)}
            className={`min-w-[32px] ${
              currentPage === i ? "bg-brand-primary hover:bg-brand-primary/90" : ""
            }`}
          >
            {i}
          </Button>
        );
      }

      if (currentPage < totalPages - 2) {
        pages.push(
          <Button
            key="end-ellipsis"
            variant="ghost"
            size="sm"
            disabled
            className="min-w-[32px] cursor-default"
          >
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        );
      }

      // Always show last page
      if (totalPages > 1) {
        pages.push(
          <Button
            key={totalPages}
            variant={currentPage === totalPages ? "default" : "outline"}
            size="sm"
            onClick={() => onPageChange(totalPages)}
            className={`min-w-[32px] ${
              currentPage === totalPages ? "bg-brand-primary hover:bg-brand-primary/90" : ""
            }`}
          >
            {totalPages}
          </Button>
        );
      }
    }

    return pages;
  };

  return (
    <div className="flex items-center gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => onPageChange(currentPage - 1)}
        disabled={currentPage <= 1}
        className="h-8 w-8 p-0"
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>
      <div className="flex items-center gap-1">
        {renderPageNumbers()}
      </div>
      <Button
        variant="outline"
        size="sm"
        onClick={() => onPageChange(currentPage + 1)}
        disabled={currentPage >= totalPages}
        className="h-8 w-8 p-0"
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
}