"use client";

import { <PERSON><PERSON> } from "lucide-react";
import { LoadingSpinner } from "@/components/shared/LoadingSpinner";
import { DepartmentAgent } from "@/app/api/organization";

interface AgentsProps {
  agents: DepartmentAgent[];
  loading: boolean;
  error: Error | null;
}

const Agents = ({ agents, loading, error }: AgentsProps) => {
  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-2">Failed to load agents</div>
        <div className="text-sm text-muted-foreground">{error.message}</div>
      </div>
    );
  }

  if (!agents || agents.length === 0) {
    return (
      <div className="text-center py-12">
        <Bot className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-medium text-brand-primary-font mb-2">
          No AI Employee found
        </h3>
        <p className="text-sm text-muted-foreground">
          This department doesn&apos;t have any AI Employee assigned yet.
        </p>
      </div>
    );
  }
  return (
    <div className="mt-6">
      <h3 className="text-sm font-medium text-brand-primary-font mb-1">
        {agents.length} {agents.length === 1 ? "agent" : "agents"}
      </h3>
      <p className="text-sm text-muted-foreground mb-4">
        Deploy intelligent agents to automate tasks, streamline workflows, and
        enhance team productivity.
      </p>

      <div className="grid gap-6 grid-cols-1  lg:grid-cols-2 xl:grid-cols-3">
        {agents.map((agent) => (
          <div
            key={agent.id}
            className="rounded-xl border border-border bg-card p-4 shadow-sm hover:bg-brand-card-hover transition-colors relative"
          >
            <div className="flex items-start gap-4">
              <div className="w-10 h-10 rounded-full bg-brand-primary flex items-center justify-center">
                <Bot className="w-5 h-5 text-white" />
              </div>
              <div className="mt-2 font-semibold text-brand-primary-font text-sm">
                {agent?.name || "N/A"}
              </div>
            </div>

            <div className="flex gap-2 mt-4 text-xs font-medium flex-wrap">
              <span className="text-green-700 bg-green-100 px-2 py-1 rounded-full">
                {agent?.visibility}
              </span>
              <span className="text-blue-700 bg-blue-100 px-2 py-1 rounded-full">
                {agent?.creator_role}
              </span>
            </div>

            <div className="text-muted-foreground text-xs mt-4 line-clamp-2">
              {agent?.description}
            </div>

            {/* Additional agent info */}
            <div className="mt-3 pt-3 border-t border-border">
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>Owner: {agent.owner_name}</span>
                <span
                  className={`px-2 py-1 rounded-full text-xs ${
                    agent.status === "active"
                      ? "bg-green-100 text-green-700"
                      : "bg-gray-100 text-gray-700"
                  }`}
                >
                  {agent?.status}
                </span>
              </div>
              <div className="mt-2 text-xs text-muted-foreground">
                Created: {new Date(agent.created_at).toLocaleDateString()}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Agents;
