"use client";

import Image from "next/image";
import CustomLoader from "@/components/shared/CustomLoader";
import { MemberPagination } from "./MemberPagination";

interface Props {
  dataLoading: boolean;
  memberList: any[];
  totalCount: number;
  currentPage: number;
  pageSize: number;
  onPageChange: (page: number) => void;
}
const Members = ({
  dataLoading,
  memberList,
  totalCount,
  currentPage,
  pageSize,
  onPageChange,
}: Props) => {
  return (
    <div className="mt-6">
      <h3 className="text-sm font-bold text-brand-primary-font mb-1">
        {totalCount > 0 &&
          `${totalCount} ${totalCount === 1 ? "member" : "members"} in this department`}
      </h3>
      <p className="text-sm text-muted-foreground mb-4">
        Manage team access, assign roles, and monitor member activity in this
        workspace.
      </p>

      <div className="w-full overflow-x-auto rounded-lg border border-border">
        <table className="min-w-full text-sm">
          <thead className="bg-muted/50 border-b border-border">
            <tr className="text-left">
              <th className="px-4 py-3 font-medium text-brand-secondary-font">
                Name
              </th>
              <th className="px-4 py-3 font-medium text-brand-secondary-font">
                Access To
              </th>
              <th className="px-4 py-3 font-medium text-brand-secondary-font">
                Role
              </th>
              <th className="px-4 py-3 font-medium text-brand-secondary-font">
                Status
              </th>
            </tr>
          </thead>
          <tbody>
            {dataLoading ? (
              <tr>
                <td colSpan={5} className="py-10 text-center">
                  <CustomLoader loadingText="Loading members..." />
                </td>
              </tr>
            ) : memberList.length > 0 ? (
              memberList.map((member) => (
                <tr
                  key={member.id}
                  className="border-b border-border hover:bg-muted/30"
                >
                  <td className="px-4 py-3 flex items-center gap-3">
                    <div className="flex-shrink-0 h-8 w-8 sm:h-10 sm:w-10">
                      <div className="h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-brand-primary flex items-center justify-center">
                        <span className="text-xs sm:text-sm font-medium text-white">
                          {(member.name || member?.email || "")
                            .charAt(0)
                            .toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div>
                      <div className="font-medium text-brand-primary-font">
                        {member.name}
                      </div>
                      <div className="text-muted-foreground text-xs">
                        {member.email}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-sm">
                    <span className="inline-flex items-center bg-blue-100 text-blue-600 text-xs font-medium rounded-full px-2 py-1">
                      {member.departments?.[0]?.permission ?? "N/A"}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-sm">
                    {member.departments?.[0]?.role ?? "N/A"}
                  </td>
                  <td className="px-4 py-3 text-sm">
                    <span className="inline-flex items-center text-green-600 text-xs font-medium">
                      <span className="w-2 h-2 mr-1 rounded-full bg-green-500"></span>
                      Active
                    </span>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={5}
                  className="py-10 text-center text-muted-foreground"
                >
                  <div className="flex flex-col items-center justify-center space-y-4">
                    <Image
                      src="/assets/images/no-user.svg"
                      alt="No active members"
                      width={120}
                      height={120}
                      className="opacity-50"
                    />
                    <div className="space-y-1">
                      <h3 className="text-xl font-bold text-brand-primary-font">
                        No members in this department
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        Add team members to collaborate and assign roles within
                        this department.
                      </p>
                    </div>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      {totalCount > pageSize && (
        <div className="mt-6 flex justify-end border-t border-border pt-4">
          <MemberPagination
            currentPage={currentPage}
            totalPages={Math.ceil(totalCount / pageSize)}
            onPageChange={onPageChange}
          />
        </div>
      )}
    </div>
  );
};

export default Members;
