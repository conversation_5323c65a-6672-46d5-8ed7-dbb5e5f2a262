"use client";
import { subscriptionApi } from "@/app/api/subscription";
import { useOrgStore } from "@/hooks/use-organization";
import { useUserStore } from "@/hooks/use-user";
import React, { useState, useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
} from "recharts";
import { BarChart3, Table } from "lucide-react";
import UsageBreakdownTable from "../_components/UsageBreakdownTable";

const UsageBreakdown = () => {
  const { currentOrganization } = useOrgStore();
  const { user } = useUserStore();

  // Set default to current realistic month (max June for 2025)
  const [selectedMonth, setSelectedMonth] = useState("7"); // June
  const [selectedYear, setSelectedYear] = useState("2025");

  // Chart line visibility toggles
  const [showChatCredits, setShowChatCredits] = useState(true);
  const [showVoiceCredits, setShowVoiceCredits] = useState(true);

  // View mode state (graph or table)
  const [viewMode, setViewMode] = useState<"graph" | "table">("graph");

  const {
    data: usageData,
    isLoading,
    error,
  } = useQuery({
    queryKey: [
      "usageBreakdown",
      currentOrganization?.id,
      user?.id,
      selectedMonth,
      selectedYear,
    ],
    queryFn: () =>
      subscriptionApi.getTokenUsageLogs(
        currentOrganization?.id || "",
        user?.id || "",
        selectedMonth,
        selectedYear
      ),
    enabled: !!currentOrganization?.id && !!user?.id,
  });

  const logs = usageData?.token_usage_logs || [];
  // No client-side filtering needed since API handles it

  // Prepare chart data
  const chartData = useMemo(() => {
    const data = logs
      .map((log: any) => ({
        date: log.date,
        formattedDate: new Date(log.date).toLocaleDateString("en-US", {
          month: "short",
          day: "numeric",
        }),
        chatCredits: Number(log.total_credits) || 0,
        voiceCredits: Number(log.total_voice_credits) || 0,
        inputTokens: log.input_tokens || 0,
        outputTokens: log.output_tokens || 0,
        voiceInputTokens: log.voice_input_tokens || 0,
        voiceOutputTokens: log.voice_output_tokens || 0,
      }))
      .sort(
        (a: any, b: any) =>
          new Date(a.date).getTime() - new Date(b.date).getTime()
      );

    return data;
  }, [logs]);

  if (isLoading) {
    return (
      <div className="px-6 py-6 space-y-6">
        {/* Header Skeleton */}
        <div className="flex items-center justify-between">
          <div>
            <Skeleton className="h-7 w-64 mb-2" />
            <Skeleton className="h-4 w-96" />
          </div>
          <Skeleton className="h-8 w-32" />
        </div>

        {/* Filters Skeleton */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex gap-4">
              <div className="flex flex-col gap-2">
                <Skeleton className="h-4 w-12" />
                <Skeleton className="h-10 w-32" />
              </div>
              <div className="flex flex-col gap-2">
                <Skeleton className="h-4 w-8" />
                <Skeleton className="h-10 w-32" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Content Skeleton */}
        <Card>
          <CardContent className="pt-6">
            <Skeleton className="h-80 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="px-6 py-6 space-y-6">
      {/* Header with View Toggle */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-brand-primary-font">
            Usage Breakdown
          </h2>
          <p className="text-sm text-gray-600">
            Detailed breakdown of your token usage and credits.
          </p>
        </div>

        {/* View Mode Tabs */}
        <div className="flex items-center bg-gray-100 rounded-lg p-1">
          <Button
            variant={viewMode === "graph" ? "default" : "ghost"}
            size="sm"
            onClick={() => setViewMode("graph")}
            className="flex items-center gap-2"
          >
            <BarChart3 className="w-4 h-4" />
            Graph
          </Button>
          <Button
            variant={viewMode === "table" ? "default" : "ghost"}
            size="sm"
            onClick={() => setViewMode("table")}
            className="flex items-center gap-2"
          >
            <Table className="w-4 h-4" />
            Table
          </Button>
        </div>
      </div>

      {error && <p className="text-red-500">Error: {(error as any).message}</p>}

      {/* Common Filters Section */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
          <CardDescription>
            Select the time period for your usage data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex flex-col gap-2">
              <Label htmlFor="month">Month</Label>
              <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Select month" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Months</SelectItem>
                  <SelectItem value="7">July</SelectItem>
                  <SelectItem value="8">August</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="year">Year</Label>
              <Select value={selectedYear} onValueChange={setSelectedYear}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Select year" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Years</SelectItem>
                  <SelectItem value="2025">2025</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Content based on view mode */}
      {viewMode === "graph" ? (
        <Card>
          <CardHeader>
            <CardTitle>Usage Trends</CardTitle>
            <CardDescription>
              Track your token usage and credit consumption over time
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Line Visibility Controls */}
            <div className="flex gap-6">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="chat-credits"
                  checked={showChatCredits}
                  onCheckedChange={(checked) =>
                    setShowChatCredits(checked === true)
                  }
                />
                <Label
                  htmlFor="chat-credits"
                  className="flex items-center gap-2"
                >
                  <div className="w-3 h-3 bg-blue-500 rounded"></div>
                  Chat Credits
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="voice-credits"
                  checked={showVoiceCredits}
                  onCheckedChange={(checked) =>
                    setShowVoiceCredits(checked === true)
                  }
                />
                <Label
                  htmlFor="voice-credits"
                  className="flex items-center gap-2"
                >
                  <div className="w-3 h-3 bg-green-500 rounded"></div>
                  Voice Credits
                </Label>
              </div>
            </div>

            {/* Chart */}
            <div className="h-80">
              <ChartContainer
                config={{
                  chatCredits: {
                    label: "Chat Credits",
                    color: "#3b82f6",
                  },
                  voiceCredits: {
                    label: "Voice Credits",
                    color: "#10b981",
                  },
                }}
                className="h-full w-full"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={chartData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid
                      strokeDasharray="3 3"
                      className="opacity-30"
                    />
                    <XAxis
                      dataKey="formattedDate"
                      tick={{ fontSize: 12 }}
                      tickLine={{ stroke: "#e5e7eb" }}
                    />
                    <YAxis
                      tick={{ fontSize: 12 }}
                      tickLine={{ stroke: "#e5e7eb" }}
                      label={{
                        value: "Credits",
                        angle: -90,
                        position: "insideLeft",
                      }}
                    />
                    <ChartTooltip
                      content={<ChartTooltipContent />}
                      labelFormatter={(value) => `Date: ${value}`}
                    />
                    {showChatCredits && (
                      <Line
                        type="monotone"
                        dataKey="chatCredits"
                        stroke="#3b82f6"
                        strokeWidth={3}
                        name="Chat Credits"
                        dot={{ fill: "#3b82f6", strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: "#3b82f6", strokeWidth: 2 }}
                      />
                    )}
                    {showVoiceCredits && (
                      <Line
                        type="monotone"
                        dataKey="voiceCredits"
                        stroke="#10b981"
                        strokeWidth={3}
                        name="Voice Credits"
                        dot={{ fill: "#10b981", strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, stroke: "#10b981", strokeWidth: 2 }}
                      />
                    )}
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </div>
          </CardContent>
        </Card>
      ) : (
        <UsageBreakdownTable usageLogs={logs} isLoading={isLoading} />
      )}
    </div>
  );
};

export default UsageBreakdown;
