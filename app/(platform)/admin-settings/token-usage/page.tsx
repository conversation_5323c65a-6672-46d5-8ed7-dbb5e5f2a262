"use client";
import { subscriptionApi } from "@/app/api/subscription";
import { useOrgStore } from "@/hooks/use-organization";
import React, { useEffect, useState } from "react";
import AggregatedTokenUsageTable from "../_components/AggregatedTokenUsageTable";
import { useQuery } from "@tanstack/react-query";

type Props = {};

const TokenUsage = (props: Props) => {
  const { currentOrganization } = useOrgStore();

  const {
    data: tokenUsageData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["aggregatedTokenUsage", currentOrganization?.id],
    queryFn: () =>
      subscriptionApi.getAggregatedTokenUsageLogs(
        currentOrganization?.id || ""
      ),
    enabled: !!currentOrganization?.id,
  });

  const logs = tokenUsageData?.user_token_usage || [];

  return (
    <div className="px-6 py-6 mt-[50px] md:mt-0">
      <h2 className="text-xl font-semibold text-brand-primary-font">
        Token Usage
      </h2>
      <p className="text-sm text-gray-600 mb-4">
        Here you can find the aggregated token usage logs for your organization.
        Click on a user to see detailed logs.
      </p>
      {error && <p className="text-red-500">Error: {(error as any).message}</p>}
      <AggregatedTokenUsageTable tokenUsageLogs={logs} isLoading={isLoading} />
    </div>
  );
};

export default TokenUsage;
