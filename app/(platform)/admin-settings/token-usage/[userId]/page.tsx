'use client'
import { subscriptionApi } from '@/app/api/subscription';
import { userApi } from '@/app/api/user';
import { useOrgStore } from '@/hooks/use-organization';
import React from 'react';
import TokenUsageTable from '../../_components/TokenUsageTable';
import { useQuery } from '@tanstack/react-query';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft } from 'lucide-react';

type Props = {};

const UserTokenUsage = (props: Props) => {
  const { currentOrganization } = useOrgStore();
  const params = useParams();
  const router = useRouter();
  const userId = params.userId as string;

  const { data: userData, isLoading: isUserLoading } = useQuery({
    queryKey: ['userDetails', userId],
    queryFn: () => userApi.getUserDetailsById(userId),
    enabled: !!userId,
  });

  const {
    data: tokenUsageData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['tokenUsage', currentOrganization?.id, userId],
    queryFn: () =>
      subscriptionApi.getTokenUsageLogs(currentOrganization?.id || '', userId),
    enabled: !!currentOrganization?.id && !!userId,
  });

  const logs = tokenUsageData?.token_usage_logs || [];

  if (!userId) {
    return <div className="p-4">No user selected.</div>;
  }
  
  if (isUserLoading) {
    return (
      <div className="px-6 py-6">
        <div className="flex items-center mb-4">
          <Button onClick={() => router.back()} variant="ghost" size="icon" className="mr-2">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <Skeleton className="h-7 w-64" />
        </div>
        <Skeleton className="h-4 w-96 mb-4" />
        <TokenUsageTable tokenUsageLogs={[]} isLoading={true} />
      </div>
    );
  }
  
  return (
    <div className="px-6 py-6">
      <div className="flex items-center mb-4">
        <Button onClick={() => router.back()} variant="ghost" size="icon" className="mr-2">
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <h2 className="text-xl font-semibold text-brand-primary-font">Token usage for {userData?.full_name}</h2>
      </div>
      <p className="text-sm text-gray-600 mb-4">
        Here you can find the detailed token usage logs for the selected user.
      </p>
      {error && <p className="text-red-500">Error: {(error as any).message}</p>}
      <TokenUsageTable
        tokenUsageLogs={logs}
        isLoading={isLoading}
      />
    </div>
  );
};

export default UserTokenUsage;