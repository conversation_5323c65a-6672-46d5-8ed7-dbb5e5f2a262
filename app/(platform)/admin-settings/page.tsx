"use client";

import React, { useCallback, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import { Lock, Hash, Users2, Bot } from "lucide-react";

import { useOrgStore } from "@/hooks/use-organization";
import { departmentApi } from "@/app/api/department";
import { DepartmentList } from "@/shared/interfaces";
import { PrimaryButton } from "@/components/shared/PrimaryButton";
import {
  Card,
  CardFooter,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";

import { useQuery } from "@tanstack/react-query";
import CustomLoader from "@/components/shared/CustomLoader";
import { useTourContext } from "@/lib/providers/TourProvider";
import { LOCAL_STORAGE_KEYS } from "@/shared/constants";
import { isTourCompleted, markTourCompleted } from "@/lib/utils/tourUtils";
import { adminSettingsTourConfig } from "../../../lib/utils/tourConfig";

const ManageDepartment = () => {
  const { currentOrganization } = useOrgStore();
  const { startTour } = useTourContext();
  console.log("currentOrganization==", currentOrganization);

  // Fetch departments using useQuery
  const { data: departments = [], isLoading: dataLoading } = useQuery<
    DepartmentList[]
  >({
    queryKey: ["departments", currentOrganization?.id],
    queryFn: () => departmentApi.getAllDepartment(),
    enabled: !!currentOrganization?.id,
  });

  useEffect(() => {
    if (
      !isTourCompleted(LOCAL_STORAGE_KEYS.RUH_ADMIN_SETTINGS_TOUR_COMPLETED)
    ) {
      handleStartTour();
    }
  }, []);

  const handleStartTour = useCallback(() => {
    startTour(adminSettingsTourConfig, {
      onComplete: () => {
        markTourCompleted(LOCAL_STORAGE_KEYS.RUH_ADMIN_SETTINGS_TOUR_COMPLETED);
      },
      onSkip: () => {
        markTourCompleted(LOCAL_STORAGE_KEYS.RUH_ADMIN_SETTINGS_TOUR_COMPLETED);
      },
    });
  }, [startTour]);

  return (
    <div className="px-6 md:py-6 py-18 mb-[50px] md:mb-0 max-h-[100vh] overflow-auto">
      <div className="flex md:items-start md:justify-between mb-6 flex-col lg:flex-row items-start justify-items-start gap-2 md:gap-2">
        <div>
          <h2 className="text-xl font-semibold text-brand-primary-font">
            Manage Departments
          </h2>
          <p className="text-muted-foreground text-sm w-[100%] md:w-[100%] lg:w-[70%]">
            Create dedicated departments to group AI agents, external
            collaborators, and internal knowledge under specific functions to
            streamline workflows.
          </p>
        </div>
        {currentOrganization?.isAdmin && (
          <Link href="/admin-settings/create-department">
            <PrimaryButton>+ Add Department</PrimaryButton>
          </Link>
        )}
      </div>

      {dataLoading ? (
        <CustomLoader loadingText="Departments Loading..." />
      ) : departments.length > 0 ? (
        <div className="grid gap-5 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
          {departments.map((department, i) => (
            <Link
              href={`/admin-settings/${department.id}`}
              key={i}
              className="h-full"
            >
              <Card className="h-full flex flex-col justify-between hover:bg-brand-card-hover transition-colors cursor-pointer gap-6 p-4">
                <CardHeader className="px-0">
                  <CardTitle className=" flex text-brand-primary-font text-bold text-sm gap-1 mb-1">
                    {department.visibility === "PRIVATE" ? (
                      <Lock className="w-4 h-4" />
                    ) : (
                      <Hash className="w-4 h-4" />
                    )}
                    {department?.name}
                  </CardTitle>

                  <CardDescription className="text-sm text-muted-foreground">
                    {department.description}
                  </CardDescription>
                </CardHeader>
                <CardFooter>
                  <div className="flex items-start justify-start gap-2 text-sm text-brand-secondary-font w-full">
                    <div className="flex items-center gap-1">
                      <Bot className="w-4 h-4" /> {department?.agentCount || 0}
                    </div>
                    <div className="flex items-center gap-1">
                      <Users2 className="w-4 h-4" />{" "}
                      {department?.memberCount || 0}
                    </div>
                  </div>
                </CardFooter>
              </Card>
            </Link>
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center mt-20 text-center">
          <Image
            src="/assets/images/no-department.svg"
            alt="No invited members"
            width={120}
            height={120}
          />
          <div className="space-y-1 mt-4">
            <h3 className="text-xl font-bold text-brand-primary-font">
              You don&apos;t have any department
            </h3>
            <p className="text-muted-foreground">
              Please add department to your account
            </p>
          </div>
          {currentOrganization?.isAdmin && (
            <Link href="/admin-settings/create-department" className="mt-4">
              <PrimaryButton>+ Add Department</PrimaryButton>
            </Link>
          )}
        </div>
      )}
    </div>
  );
};

export default ManageDepartment;
