"use client";

import Image from "next/image";
import { toast } from "sonner";

import { useQuery } from "@tanstack/react-query";
import { organizationApi } from "@/app/api/organization";
import EditOrgDetails from "../_components/EditOrgDetails";
import { useState } from "react";
import { useOrgStore } from "@/hooks/use-organization";

const WorkspaceSettings = () => {
  // Fetch organization details
  const { data: currentOrg, refetch: refetchOrganizations } = useQuery({
    queryKey: ["organizationDetails"],
    queryFn: organizationApi.getCurrentOrganizationDetails,
  });

  const [activeTab, setActiveTab] = useState("overview");
  const { currentOrganization } = useOrgStore();

  // Fetch admin data
  const {
    data: adminData,
    isLoading: isAdminLoading,
    error: adminError,
  } = useQuery({
    queryKey: ["organizationAdmins"],
    queryFn: organizationApi.getOrganizationAdmins,
  });

  const handleUpdateOrganization = async (data: {
    name: string;
    industry: string;
    websiteUrl?: string;
    logo?: string;
  }) => {
    if (!currentOrg?.id) return;

    await organizationApi.updateOrganizationDetails(currentOrg.id, data);
    // Refetch organization details after update
    refetchOrganizations();
    toast.success("Details Updated successfully");
  };

  return (
    <div className="p-6 md:py-6 py-18 mb-[50px] md:mb-0">
      {/* Banner */}

      {/* Workspace Info */}
      <div>
        <h2 className="text-xl font-semibold text-brand-primary-font">
          Workspace Overview
        </h2>
        <p className="text-muted-foreground text-sm w-[100%] md:w-[70%]">
          {`Manage your organization's core settings with ease. Update workspace
          details and maintain administrative control to ensure alignment across
          your AI-powered operations.`}
        </p>

        <div className="w-full overflow-hidden rounded-xl mt-4 mb-8">
          <div className="relative w-full h-56">
            <Image
              src="/assets/misc/ad.png"
              alt="Workspace Banner"
              fill
              className="object-cover w-[75%]"
            />
            <div className="absolute inset-0 bg-black/10 flex items-center px-8">
              <div>
                <h3 className="text-white text-xl font-semibold leading-snug">
                  Bring Your Ideas to Life with AI Agents.
                  <br />
                  Automate and manage your workspace effortlessly.
                </h3>
              </div>
            </div>
          </div>
        </div>

        <div className="flex items-center justify-between mb-6 border-b border-border">
          <div className="flex space-x-4">
            <button
              className={`pb-4 px-1 text-sm font-medium ${
                activeTab === "overview"
                  ? "border-b-2 border-brand-primary text-brand-primary"
                  : "text-primary-font hover:text-brand-secondary"
              }`}
              onClick={() => setActiveTab("overview")}
            >
              Overview
            </button>
            <button
              className={`pb-4 px-1 text-sm font-medium ${
                activeTab === "admins_and_owners"
                  ? "border-b-2 border-brand-primary text-brand-primary"
                  : "text-primary-font hover:text-brand-secondary"
              }`}
              onClick={() => setActiveTab("admins_and_owners")}
            >
              Admins & Owners
            </button>
          </div>
        </div>

        <div className="border border-border rounded-xl bg-card">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between border-b border-border p-6 justify-start items-start gap-[10px] md:gap-0">
            <div className="flex items-center gap-4">
              <Image
                src={currentOrg?.logo || "/assets/logos/rapid-logo.svg"}
                alt={currentOrg?.name || "Organization"}
                width={48}
                height={48}
              />
              <div>
                <div className="text-brand-primary-font font-semibold">
                  {currentOrg?.name || "Loading..."}
                </div>
                <div className="text-muted-foreground text-sm">
                  {currentOrg?.websiteUrl || ""}
                </div>
              </div>
            </div>
            {currentOrganization?.isAdmin && (
              <EditOrgDetails
                currentData={{
                  name: currentOrg?.name || "",
                  websiteUrl: currentOrg?.websiteUrl || "",
                  industry: currentOrg?.industry || "",
                  logo: currentOrg?.logo || "",
                }}
                onSave={handleUpdateOrganization}
              />
            )}
          </div>

          {activeTab === "overview" && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6 text-sm">
              <div>
                <div className="text-muted-foreground">Industry</div>
                <div className="text-brand-primary-font font-medium">
                  {currentOrg?.industry || "Loading..."}
                </div>
              </div>
              <div>
                <div className="text-muted-foreground">Date Created</div>
                <div className="text-brand-primary-font font-medium">
                  {currentOrg?.createdAt
                    ? new Date(currentOrg.createdAt).toLocaleDateString(
                        "en-US",
                        {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        }
                      )
                    : "Loading..."}
                </div>
              </div>
            </div>
          )}

          {activeTab === "admins_and_owners" && (
            <div className="divide-y divide-border p-6">
              {isAdminLoading ? (
                <div className="flex items-center justify-center">
                  <div className="text-muted-foreground">
                    Loading admin data...
                  </div>
                </div>
              ) : adminError ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-red-500">Error loading admin data</div>
                </div>
              ) : adminData ? (
                <div className="flex items-center justify-between  overflow-auto scrollbar-hide gap-[10px]">
                  <div className="flex items-center gap-4 min-w-[200px] md:min-w-auto">
                    <div className="flex-shrink-0 h-8 w-8 sm:h-10 sm:w-10">
                      <div className="h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-brand-primary flex items-center justify-center">
                        <span className="text-xs sm:text-sm font-medium text-white">
                          {(adminData?.fullName || "").charAt(0).toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div>
                      <div className="text-brand-primary-font font-medium text-sm">
                        {adminData.fullName}
                      </div>
                      <div className="text-muted-foreground text-xs md:break-all">
                        {adminData.email}
                      </div>
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground min-w-[200px] md:min-w-auto">
                    Joined on{" "}
                    {new Date(adminData.createdAt).toLocaleDateString("en-US", {
                      year: "numeric",
                      month: "long",
                      day: "numeric",
                    })}
                  </div>
                  <div className="text-sm text-brand-primary-font w-28 text-right">
                    Admin
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center py-8">
                  <div className="text-muted-foreground">
                    No admin data available
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default WorkspaceSettings;
