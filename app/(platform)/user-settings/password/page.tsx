"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { CircleHelp, Eye, EyeOff } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { LoadingSpinner } from "@/components/shared/LoadingSpinner";
import { userApi } from "@/app/api/user";
import { UserInfo, UserUpdate } from "@/shared/interfaces";
import { useUserStore } from "@/hooks/use-user";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Too<PERSON><PERSON><PERSON>rigger,
} from "@/components/ui/tooltip";
import * as TooltipPrimitive from "@radix-ui/react-tooltip";

// Profile form schema
const profileSchema = z
  .object({
    current_password: z.string().min(8, "Current password is required"),
    new_password: z
      .string()
      .min(8, "Password must be at least 8 characters")
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
        "Password must contain at least one uppercase letter, one lowercase letter, and one number"
      ),
    confirm_new_password: z.string().min(8, "Please confirm your password"),
  })
  .refine((data) => data.new_password === data.confirm_new_password, {
    message: "Passwords don't match",
    path: ["confirm_new_password"],
  });

type ProfileFormData = z.infer<typeof profileSchema>;

export default function PasswordComponent() {
  const queryClient = useQueryClient();
  const { user, setUser } = useUserStore();

  // Password visibility states
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Fetch user data
  const {
    data: userInfo,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["user", "info"],
    queryFn: userApi.getCurrentUser,
  });

  // Form setup
  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      current_password: "",
      new_password: "",
      confirm_new_password: "",
    },
  });

  // Update profile mutation
  const updatePasswordMutation = useMutation({
    mutationFn: async (data: ProfileFormData) => {
      // Here you would call your API to update the password
      // For now, we'll just simulate the API call
      return await userApi.updatePassword({
        current_password: data.current_password,
        new_password: data.new_password,
        confirm_new_password: data.confirm_new_password,
      });
    },
    onSuccess: () => {
      toast.success("Password updated successfully");
      form.reset();
      queryClient.invalidateQueries({ queryKey: ["user", "info"] });
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const onSubmit = (data: ProfileFormData) => {
    updatePasswordMutation.mutate(data);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="text-center text-color-error">
          Failed to load profile information
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full flex flex-col gap-6">
      <div className="flex flex-col gap-20 md:p-[28px] p-[14px]">
        <div className="w-full flex flex-col gap-[24px]">
          {/* Header Section */}
          <div className="flex flex-col xl:flex-row items-start gap-[20px] xl:items-center justify-between mt-[50px] md:mt-0">
            <div className="space-y-1">
              <h1 className="text-xl font-satoshi-bold text-text-primary">
                Change Password
              </h1>
              <p className="text-sm font-satoshi-regular text-text-secondary">
                Update your password to keep your account secure.
              </p>
            </div>

            <div className="flex items-center gap-4">
              <Button
                onClick={() => {
                  form.reset();
                }}
                type="button"
                variant={"tertiary"}
                className="w-[80px] p-3"
              >
                Cancel
              </Button>
              <Button
                onClick={form.handleSubmit(onSubmit)}
                type="button"
                variant="primary"
                className="min-w-[80px] p-3"
                disabled={
                  !form.formState.isDirty || updatePasswordMutation.isPending
                }
              >
                {updatePasswordMutation.isPending ? "Saving..." : "Save"}
              </Button>
            </div>
          </div>

          {/* Profile Form */}
          <Card className="bg-color-card-color border-border-muted p-[20px] shadow-none bg-white">
            <CardContent className="p-0">
              <Form {...form}>
                <form id="profile-form" onSubmit={form.handleSubmit(onSubmit)}>
                  {/* Full Name Field */}
                  <div>
                    <FormField
                      control={form.control}
                      name="current_password"
                      render={({ field }) => (
                        <FormItem className="flex flex-col xl:flex-row">
                          <div className="space-y-2 md:min-w-[320px]">
                            <FormLabel className="text-sm font-satoshi-bold text-text-primary data-[error=true]:text-text-primary">
                              Current Password
                            </FormLabel>
                            <p className="text-sm font-satoshi-regular text-text-secondary">
                              Enter your current password to verify your
                              identity
                            </p>
                          </div>
                          <div className="flex flex-col gap-2 xl:w-[510px]">
                            <div className="relative">
                              <FormControl>
                                <Input
                                  {...field}
                                  type={
                                    showCurrentPassword ? "text" : "password"
                                  }
                                  placeholder="Enter your current password"
                                  className={`h-12 px-3 pr-10  text-sm rounded-lg transition-colors`}
                                />
                              </FormControl>
                              <button
                                type="button"
                                onClick={() =>
                                  setShowCurrentPassword(!showCurrentPassword)
                                }
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors"
                              >
                                {showCurrentPassword ? (
                                  <Eye className="h-4 w-4" />
                                ) : (
                                  <EyeOff className="h-4 w-4" />
                                )}
                              </button>
                            </div>
                            <FormMessage className="!text-color-error text-sm" />
                          </div>
                        </FormItem>
                      )}
                    />

                    {/* Divider */}
                    <div className="h-px bg-color-border-default w-full" />
                  </div>
                  <hr className="border-color-border-default my-[20px]" />
                  {/* New Password Field */}
                  <div>
                    <FormField
                      control={form.control}
                      name="new_password"
                      render={({ field }) => (
                        <FormItem className="flex flex-col xl:flex-row">
                          <div className="space-y-2 md:min-w-[320px]">
                            <div className="flex flex-row gap-1 items-center">
                              <FormLabel className="text-sm font-satoshi-bold text-text-primary data-[error=true]:text-text-primary">
                                New Password
                              </FormLabel>
                              {/* <CircleHelp className="w-4 h-4 cursor-pointer"/> */}
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <span tabIndex={0}>
                                      <CircleHelp className="w-4 h-4 cursor-pointer text-text-placeholder" />
                                    </span>
                                  </TooltipTrigger>
                                  <TooltipPrimitive.Portal>
                                    <TooltipPrimitive.Content
                                      data-slot="tooltip-content"
                                      sideOffset={8}
                                      className={
                                        "border border-brand-stroke animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance bg-black text-white max-w-[310px] p-3"
                                      }
                                      side="right"
                                    >
                                      <span className="absolute left-0 rotate-270 -translate-x-1/2 top-1/2">
                                        <svg
                                          width="20"
                                          height="10"
                                          viewBox="0 0 20 10"
                                          className="text-black"
                                          fill="currentColor"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <polygon points="10,0 20,10 0,10" />
                                        </svg>
                                      </span>
                                      <span>
                                        Your password must be between 6-15
                                        characters long should contain at least
                                        one uppercase letter, lowercase letter,
                                        number, and a special character.
                                      </span>
                                    </TooltipPrimitive.Content>
                                  </TooltipPrimitive.Portal>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                            <p className="text-sm font-satoshi-regular text-text-secondary">
                              Create a strong password with at least 8
                              characters
                            </p>
                          </div>
                          <div className="flex flex-col gap-2 xl:w-[510px]">
                            <div className="relative">
                              <FormControl>
                                <Input
                                  {...field}
                                  type={showNewPassword ? "text" : "password"}
                                  placeholder="Enter your new password"
                                  className="h-12 px-3 pr-10  text-sm bg-color-background-muted text-text-primary rounded-lg"
                                />
                              </FormControl>
                              <button
                                type="button"
                                onClick={() =>
                                  setShowNewPassword(!showNewPassword)
                                }
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors"
                              >
                                {showNewPassword ? (
                                  <Eye className="h-4 w-4" />
                                ) : (
                                  <EyeOff className="h-4 w-4" />
                                )}
                              </button>
                            </div>

                            <FormMessage className="!text-color-error text-sm" />
                          </div>
                        </FormItem>
                      )}
                    />

                    {/* Divider */}
                    <div className="h-px bg-color-border-default w-full" />
                  </div>
                  <hr className="border-color-border-default my-[20px]" />

                  {/* Confirm Password Field */}
                  <div>
                    <FormField
                      control={form.control}
                      name="confirm_new_password"
                      render={({ field }) => (
                        <FormItem className="flex flex-col xl:flex-row">
                          <div className="space-y-2 md:min-w-[320px]">
                            <FormLabel className="text-sm font-satoshi-bold text-text-primary data-[error=true]:text-text-primary">
                              Confirm Password
                            </FormLabel>
                            <p className="text-sm font-satoshi-regular text-text-secondary">
                              Re-enter your new password to confirm
                            </p>
                          </div>
                          <div className="flex flex-col gap-2 xl:w-[510px]">
                            <div className="relative">
                              <FormControl>
                                <Input
                                  {...field}
                                  type={
                                    showConfirmPassword ? "text" : "password"
                                  }
                                  placeholder="Enter your confirm password"
                                  className="h-12 px-3 pr-10 text-sm bg-color-background-muted text-text-primary rounded-lg"
                                />
                              </FormControl>
                              <button
                                type="button"
                                onClick={() =>
                                  setShowConfirmPassword(!showConfirmPassword)
                                }
                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors"
                              >
                                {showConfirmPassword ? (
                                  <Eye className="h-4 w-4" />
                                ) : (
                                  <EyeOff className="h-4 w-4" />
                                )}
                              </button>
                            </div>
                            <FormMessage className="!text-color-error text-sm" />
                          </div>
                        </FormItem>
                      )}
                    />

                    {/* Divider */}
                    <div className="h-px bg-color-border-default w-full" />
                  </div>
                </form>
              </Form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
