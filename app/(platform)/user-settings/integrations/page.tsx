"use client";
import React from "react";
import IntegrationsAndApiKeys from "../_components/IntegrationsAndApiKeys";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { dashboardRoute } from "@/shared/routes";

function Integrations() {
  const router = useRouter();
  return (
    <div className="w-full h-full flex flex-col gap-6">
      <div className="flex flex-col gap-6 md:p-[28px] p-[14px] max-h-[100vh] overflow-auto">
        <div className="flex flex-row gap-2 justify-between items-center max-sm:flex-col max-sm:items-start">
          <div className="flex flex-col gap-1">
            <h1 className="text-2xl font-bold">API keys & integrations</h1>
            <p className="text-sm text-text-secondary">
              API Dashboard Sidebar Enhancement
            </p>
          </div>
          <Button
            onClick={() => router.push(dashboardRoute)}
            variant="tertiary"
            className="w-[217px]"
          >
            Back to Home
          </Button>
        </div>
        <IntegrationsAndApiKeys />
      </div>
    </div>
  );
}

export default Integrations;
