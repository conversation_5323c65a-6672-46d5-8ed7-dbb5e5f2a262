import React, { useState, useEffect } from "react";
import { Dot, Key, Plus, Search, X, ChevronDown } from "lucide-react";
import { Sheet, SheetContent } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { apiKeyAndIntegration } from "@/app/api/apiKeysAndIntegration";
import { LoadingSpinner } from "@/components/shared/LoadingSpinner";
import { useUserStore } from "@/hooks/use-user";
import { toast } from "sonner";
import { SpinnerIcon } from "@livekit/components-react";
import Image from "next/image";

// Common loader component
const CenteredLoader = ({
  isLoading,
  children,
  message,
}: {
  isLoading: boolean;
  children: React.ReactNode;
  message?: string;
}) => {
  return (
    <div className="relative">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center z-10 bg-white/60">
          <LoadingSpinner message={message} className="min-h-0" />
        </div>
      )}
      {children}
    </div>
  );
};

const RenderKeyIntegration = ({
  data,
  title,
  isLoading,
  setSelectedKeyForIntegration,
  hasMore,
  onLoadMore,
  isLoadingMore,
}: {
  title: string;
  data: any;
  isLoading: boolean;
  setSelectedKeyForIntegration?: (open: any) => void;
  hasMore?: boolean;
  onLoadMore?: () => void;
  isLoadingMore?: boolean;
}) => {
  return (
    <div className="relative">
      <div className="text-[20px] font-satoshi-bold py-3 rounded-t-lg max-sm:text-[18px]">
        {title}
      </div>
      <div className="flex flex-col gap-2 w-full">
        {data?.length ? (
          <>
            {data.map((item: any) => (
              <div
                onClick={() => {
                  if (item?.is_connected) {
                    setSelectedKeyForIntegration &&
                      setSelectedKeyForIntegration(item);
                  }
                }}
                key={item.name || item.id}
                className={`border border-border-color rounded-lg w-full hover:bg-gray-100 flex flex-row gap-4 items-center py-3 px-4 min-h-[88px] max-h-[88px] border-b-1 border-gray-200 ${item?.is_connected ? "cursor-pointer" : ""}`}
              >
                {item?.logo && (
                  <div className="flex h-full">
                    <Image
                      src={item.logo}
                      alt={item.name}
                      width={24}
                      height={24}
                    />
                  </div>
                )}
                <div className="flex justify-between w-full items-center">
                  <div className="text-sm flex flex-col">
                    <p className="text-sm font-satoshi-bold leading-[20px]">
                      {item.name || item?.integration_name}
                    </p>
                    <span className="font-satoshi-regular text-text-secondary max-w-[295px] max-sm:max-w-[150px] line-clamp-2 leading-[22px]">
                      {item?.description || ""}
                    </span>
                  </div>
                  {item?.is_connected &&
                    (item?.connection_type == "oauth" ? (
                      <div className="flex text-sm font-satoshi-regular text-success justify-center items-center">
                        <Dot className="w-12 h-12 text-green-600 animate-pulse" />
                        <span>Connected</span>
                      </div>
                    ) : (
                      <span className="text-sm font-satoshi-regular text-success">
                        Key Provided
                      </span>
                    ))}

                  {!item?.is_connected && (
                    <Button
                      onClick={() => {
                        setSelectedKeyForIntegration &&
                          setSelectedKeyForIntegration(item);
                      }}
                      variant={"tertiary"}
                    >
                      Connect
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </>
        ) : (
          <div className="flex flex-row justify-center items-center gap-[14px] min-h-[60px] border border-border-color rounded-lg w-full">
            <Key className="w-6 h-6 text-text-secondary" />
            <p className="font-satoshi-bold text-text-secondary">
              No integrations found
            </p>
          </div>
        )}
      </div>
      {hasMore && (
        <div className=" flex w-full justify-center">
          <Button
            variant="primary"
            onClick={onLoadMore}
            disabled={isLoadingMore}
            className="flex items-center gap-2 mt-2"
          >
            {isLoadingMore ? (
              <SpinnerIcon className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
            <span>{isLoadingMore ? "Loading..." : "Load More"}</span>
          </Button>
        </div>
      )}
    </div>
  );
};

// Custom debounce hook
const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

function IntegrationsAndApiKeys() {
  const [selectedKeyForIntegration, setSelectedKeyForIntegration] =
    useState<any>(null);
  // State for API key config input values
  const [apiKeyConfigValues, setApiKeyConfigValues] = useState<
    Record<string, string>
  >({});
  // Pagination state
  const [integrationsPage, setIntegrationsPage] = useState(1);
  const [connectedIntegrationsPage, setConnectedIntegrationsPage] = useState(1);
  const PAGE_SIZE = 10; // Set page size to 5 as per the response example

  const queryClient = useQueryClient();
  const [isSaving, setIsSaving] = useState(false);
  const [isOauthSaving, setIsOauthSaving] = useState(false);
  const [isOauthLoading, setIsOauthLoading] = useState(false);
  const [isApiKeyLoading, setIsApiKeyLoading] = useState(false);
  const [hasFetchedApiKey, setHasFetchedApiKey] = useState(false);
  const [initialApiKeyConfigValues, setInitialApiKeyConfigValues] = useState<
    Record<string, string>
  >({});
  const [toolConnectionStatus, setToolConnectionStatus] = useState<
    Record<string, boolean>
  >({});
  const [isOauthConnected, setIsOauthConnected] = useState<boolean>(false);
  const [refreshCredentials, setRefreshCredentials] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const debouncedSearchTerm = useDebounce(searchTerm, 500); // 500ms debounce delay
  const { user } = useUserStore();

  // State to store all loaded integrations
  const [allIntegrations, setAllIntegrations] = useState<any[]>([]);
  const [allConnectedIntegrations, setAllConnectedIntegrations] = useState<
    any[]
  >([]);
  const [hasMore, setHasMore] = useState({
    connectedTools: false,
    allTools: false,
  });
  const { data: integrationAndKeysData, isFetching: isFetching } = useQuery({
    queryKey: ["integrations", debouncedSearchTerm, integrationsPage],
    queryFn: () =>
      apiKeyAndIntegration.getListOfIntegrations(
        integrationsPage,
        PAGE_SIZE,
        true,
        debouncedSearchTerm
      ),
    staleTime: 5 * 60 * 1000,
  });

  const { data: connectedKeysData, isFetching: isFetchingConnectedKeys } =
    useQuery({
      queryKey: [
        "connectedKeys",
        debouncedSearchTerm,
        connectedIntegrationsPage,
      ],
      queryFn: () =>
        apiKeyAndIntegration.getUserConnectedIntegrations(
          connectedIntegrationsPage,
          PAGE_SIZE,
          debouncedSearchTerm
        ),
      staleTime: 5 * 60 * 1000,
    });
  // Update allIntegrations when new data is fetched
  useEffect(() => {
    if (integrationAndKeysData?.integrations) {
      if (
        integrationAndKeysData?.total >
        [...allIntegrations, ...integrationAndKeysData?.integrations]?.length
      ) {
        setHasMore((prev) => ({
          ...prev,
          allTools: true,
        }));
      } else {
        setHasMore((prev) => ({
          ...prev,
          allTools: false,
        }));
      }
      if (integrationsPage === 1) {
        setAllIntegrations(integrationAndKeysData.integrations);
      } else {
        setAllIntegrations((prev) => [
          ...prev,
          ...integrationAndKeysData.integrations,
        ]);
      }
    }
  }, [integrationAndKeysData, integrationsPage]);

  // Update allConnectedIntegrations when new data is fetched
  useEffect(() => {
    if (connectedKeysData?.integrations) {
      if (
        connectedKeysData?.total >
        [...allConnectedIntegrations, ...connectedKeysData.integrations]?.length
      ) {
        setHasMore((prev) => ({
          ...prev,
          connectedTools: true,
        }));
      } else {
        setHasMore((prev) => ({
          ...prev,
          connectedTools: false,
        }));
      }
      if (connectedIntegrationsPage === 1) {
        setAllConnectedIntegrations(connectedKeysData.integrations);
      } else {
        setAllConnectedIntegrations((prev) => [
          ...prev,
          ...connectedKeysData.integrations,
        ]);
      }
    }
  }, [connectedKeysData, connectedIntegrationsPage]);

  // Reset pagination when search term changes
  useEffect(() => {
    setIntegrationsPage(1);
    setConnectedIntegrationsPage(1);
  }, [debouncedSearchTerm]);

  // Load more integrations
  const loadMoreIntegrations = async () => {
    setIntegrationsPage((prev) => prev + 1);
  };

  // Load more connected integrations
  const loadMoreConnectedIntegrations = async () => {
    setConnectedIntegrationsPage((prev) => prev + 1);
  };

  // Reset config values when integration changes
  React.useEffect(() => {
    if (
      selectedKeyForIntegration?.schema_definition ||
      selectedKeyForIntegration?.api_key_config
    ) {
      const initial: Record<string, string> = {};
      (
        selectedKeyForIntegration?.schema_definition ||
        selectedKeyForIntegration?.api_key_config
      ).forEach((item: any) => {
        initial[item.name] = "";
      });
      setApiKeyConfigValues(initial);
    } else {
      setApiKeyConfigValues({});
    }
  }, [selectedKeyForIntegration]);

  // Fetch saved API key credentials when an API key integration is selected
  useEffect(() => {
    const fetchCredentials = async () => {
      if (
        selectedKeyForIntegration?.integration_type === "api_key" ||
        selectedKeyForIntegration?.connection_type === "api_key"
      ) {
        setIsApiKeyLoading(true);
        setHasFetchedApiKey(false);
        try {
          const data = await apiKeyAndIntegration.getApiKeyCredentials(
            selectedKeyForIntegration.id ||
              selectedKeyForIntegration?.integration_id
          );
          // If data is an object with keys, set them as initial values
          if (data && typeof data === "object" && data.credentials) {
            setApiKeyConfigValues(data.credentials);
            setInitialApiKeyConfigValues(data.credentials);
            const integrationId =
              selectedKeyForIntegration.id ||
              selectedKeyForIntegration.integration_id;
            setToolConnectionStatus((prev) => ({
              ...prev,
              [integrationId]: data?.is_connected,
            }));
          } else {
            // No credentials saved, reset to empty
            const initial: Record<string, string> = {};
            (
              selectedKeyForIntegration?.schema_definition ||
              selectedKeyForIntegration?.api_key_config
            )?.forEach((item: any) => {
              initial[item.name] = "";
            });
            setApiKeyConfigValues(initial);
            setInitialApiKeyConfigValues(initial);
            const integrationId =
              selectedKeyForIntegration.id ||
              selectedKeyForIntegration.integration_id;
            setToolConnectionStatus((prev) => ({
              ...prev,
              [integrationId]: false,
            }));
          }
        } catch (err) {
          // Optionally, show error message
          setApiKeyConfigValues({});
          setInitialApiKeyConfigValues({});
        } finally {
          setIsApiKeyLoading(false);
          setHasFetchedApiKey(true);
          setRefreshCredentials(false);
        }
      } else if (
        selectedKeyForIntegration?.integration_type === "oauth" ||
        selectedKeyForIntegration?.connection_type === "oauth"
      ) {
        setIsOauthLoading(true);
        setIsOauthConnected(false);
        try {
          if (selectedKeyForIntegration?.is_connected) {
            setIsOauthConnected(true);
            return;
          }
          const data = await apiKeyAndIntegration.getOauthCredentials(
            selectedKeyForIntegration.id ||
              selectedKeyForIntegration?.integration_id
          );
          setIsOauthConnected(data?.user_integration_status?.is_connected);
        } catch (err) {
          setIsOauthConnected(false);
          const integrationId =
            selectedKeyForIntegration.id ||
            selectedKeyForIntegration.integration_id;
          setToolConnectionStatus((prev) => ({
            ...prev,
            [integrationId]: false,
          }));
        } finally {
          setIsOauthLoading(false);
          setRefreshCredentials(false);
        }
      }
    };

    fetchCredentials();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedKeyForIntegration, refreshCredentials]);

  const handleConfigInputChange = (name: string, value: string) => {
    setApiKeyConfigValues((prev) => ({ ...prev, [name]: value }));
  };

  const allRequiredFilled = (
    selectedKeyForIntegration?.schema_definition ||
    selectedKeyForIntegration?.api_key_config
  )?.every(
    (item: any) =>
      !item.required ||
      (apiKeyConfigValues[item.name] &&
        apiKeyConfigValues[item.name].trim() !== "")
  );

  // Check if any value has changed compared to the initial loaded credentials
  const hasChanged = Object.keys(apiKeyConfigValues).some(
    (key) => apiKeyConfigValues[key] !== initialApiKeyConfigValues[key]
  );

  const handleSaveApiKeyCredentials = async () => {
    if (!selectedKeyForIntegration?.id) return;
    setIsSaving(true);
    try {
      await apiKeyAndIntegration.addApiKeyCredentials(
        selectedKeyForIntegration.id,
        apiKeyConfigValues
      );
      queryClient.invalidateQueries({ queryKey: ["integrations"] });
      queryClient.invalidateQueries({ queryKey: ["connectedKeys"] });
      // Reset pagination when credentials are updated
      setIntegrationsPage(1);
      setConnectedIntegrationsPage(1);
      setAllIntegrations([]);
      setAllConnectedIntegrations([]);
      setRefreshCredentials(true);
      // Optionally, show a success message or refresh integrations
      // setSelectedKeyForIntegration(null);
    } catch (err) {
      // Optionally, show error message
      // eslint-disable-next-line no-console
      console.error(err);
    } finally {
      setIsSaving(false);
      const integrationId =
        selectedKeyForIntegration.id ||
        selectedKeyForIntegration.integration_id;
      setToolConnectionStatus((prev) => ({
        ...prev,
        [integrationId]: false,
      }));
    }
  };

  const handleConnectOauth = async () => {
    if (!selectedKeyForIntegration?.id) return;
    setIsOauthSaving(true);
    try {
      const redirect_url = window.location.href;
      const user_id = user?.id;
      const oauthParams = new URLSearchParams({
        user_id: user_id || "", // Using email as user_id since that's what we have
        redirect_url: redirect_url || "", // Redirect back to current page after OAuth
      });
      const authUrl = `${
        process.env.NEXT_PUBLIC_API_URL
      }/integrations/${selectedKeyForIntegration.id}/oauth/authorize?${oauthParams.toString()}`;
      window.location.href = authUrl;
    } catch (err) {
      console.error(err);
    } finally {
      setIsOauthSaving(false);
    }
  };

  const handleDisconnectOauth = async () => {
    if (
      !selectedKeyForIntegration?.id &&
      !selectedKeyForIntegration?.integration_id
    )
      return;
    setIsOauthLoading(true);
    try {
      const response = await apiKeyAndIntegration.disconnectOauthCredentials(
        selectedKeyForIntegration.id || selectedKeyForIntegration.integration_id
      );
      toast.success(response?.message || "Credential removed successfully", {
        position: "top-center",
      });
      setIsOauthConnected(false);

      // Update the selectedKeyForIntegration to reflect disconnected state
      setSelectedKeyForIntegration((prev: any) => ({
        ...prev,
        is_connected: false,
      }));

      queryClient.invalidateQueries({ queryKey: ["integrations"] });
      queryClient.invalidateQueries({ queryKey: ["connectedKeys"] });
      // Reset pagination when credentials are updated
      setIntegrationsPage(1);
      setConnectedIntegrationsPage(1);
      setAllIntegrations([]);
      setAllConnectedIntegrations([]);

      const integrationId =
        selectedKeyForIntegration.id ||
        selectedKeyForIntegration.integration_id;
      setToolConnectionStatus((prev) => ({
        ...prev,
        [integrationId]: false,
      }));
      setRefreshCredentials(true);
    } catch (err) {
      // Optionally show error
    } finally {
      setIsOauthLoading(false);
    }
  };

  const integrationId =
    selectedKeyForIntegration?.id || selectedKeyForIntegration?.integration_id;

  return (
    <CenteredLoader
      isLoading={
        (isFetchingConnectedKeys || isFetching) &&
        integrationsPage === 1 &&
        connectedIntegrationsPage === 1
      }
    >
      <div className="flex flex-col w-[100%] gap-6">
        <div className="flex flex-col gap-2 border border-border-color rounded-lg w-full">
          <div className="bg-color-light rounded-lg p-4">
            <div className="relative flex-shrink-0">
              <Search
                strokeWidth={1.2}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary w-6 h-6"
              />
              <Input
                placeholder={"Search Integrations"}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 text-sm"
              />
            </div>
          </div>
        </div>
        <div
          className={`flex flex-col gap-8  w-[100%] ${selectedKeyForIntegration ? "w-[70%]" : "w-[100%]"}`}
        >
          <RenderKeyIntegration
            isLoading={
              isFetchingConnectedKeys && connectedIntegrationsPage === 1
            }
            title="Your Integrations"
            data={allConnectedIntegrations}
            setSelectedKeyForIntegration={setSelectedKeyForIntegration}
            hasMore={hasMore?.connectedTools}
            onLoadMore={loadMoreConnectedIntegrations}
            isLoadingMore={
              isFetchingConnectedKeys && connectedIntegrationsPage > 1
            }
          />
          <RenderKeyIntegration
            isLoading={isFetching && integrationsPage === 1}
            setSelectedKeyForIntegration={setSelectedKeyForIntegration}
            title="All Integrations"
            data={allIntegrations}
            hasMore={hasMore?.allTools}
            onLoadMore={loadMoreIntegrations}
            isLoadingMore={isFetching && integrationsPage > 1}
          />
        </div>
        <Sheet
          open={!!selectedKeyForIntegration}
          onOpenChange={(open) => {
            if (!open) setSelectedKeyForIntegration(null);
          }}
        >
          <SheetContent side="right" className="w-[25%] max-sm:w-[80%] p-6">
            <div className="flex gap-4 items-center">
              {selectedKeyForIntegration?.logo && (
                <div className="flex h-full items-center">
                  <Image
                    src={selectedKeyForIntegration.logo}
                    alt={selectedKeyForIntegration.name}
                    width={24}
                    height={24}
                  />
                </div>
              )}
              <p className="font-satoshi-bold">
                {selectedKeyForIntegration?.name ||
                  selectedKeyForIntegration?.integration_name}
              </p>
            </div>
            {selectedKeyForIntegration?.integration_type == "api_key" ||
            selectedKeyForIntegration?.connection_type == "api_key" ? (
              <div className="flex flex-col gap-3">
                {isApiKeyLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <LoadingSpinner message="Loading saved keys..." />
                  </div>
                ) : (
                  <>
                    {(
                      selectedKeyForIntegration?.schema_definition ||
                      selectedKeyForIntegration?.api_key_config
                    )?.map((item: any) => {
                      return (
                        <div className="flex flex-col gap-2" key={item.name}>
                          <p>{item?.name}</p>
                          <Input
                            type="password"
                            className="text-sm"
                            placeholder={item?.description}
                            required={item?.required}
                            value={apiKeyConfigValues[item.name] || ""}
                            onChange={(e) =>
                              handleConfigInputChange(item.name, e.target.value)
                            }
                            disabled={
                              isApiKeyLoading ||
                              toolConnectionStatus[integrationId]
                            }
                          />
                        </div>
                      );
                    })}
                    {toolConnectionStatus[integrationId] ? (
                      <Button
                        onClick={handleDisconnectOauth}
                        disabled={isOauthLoading}
                        className="w-full"
                        variant="primary"
                      >
                        {isOauthLoading ? "Removing..." : "Remove"}
                      </Button>
                    ) : (
                      <Button
                        onClick={handleSaveApiKeyCredentials}
                        disabled={!allRequiredFilled || isSaving || !hasChanged}
                        className="w-full"
                        variant="primary"
                      >
                        {isSaving ? "Saving..." : "Save"}
                      </Button>
                    )}
                  </>
                )}
              </div>
            ) : selectedKeyForIntegration?.integration_type == "oauth" ||
              selectedKeyForIntegration?.connection_type == "oauth" ? (
              <div className="flex flex-col gap-6">
                {!isOauthConnected ? (
                  <Button
                    onClick={handleConnectOauth}
                    disabled={isOauthSaving}
                    className="w-full"
                    variant="primary"
                  >
                    <Plus />
                    <p>{isOauthSaving ? "Connecting..." : "Connect"}</p>
                  </Button>
                ) : (
                  <Button
                    onClick={handleDisconnectOauth}
                    disabled={isOauthLoading}
                    className="w-full"
                    variant="destructive"
                  >
                    <p>{isOauthLoading ? "Disconnecting..." : "Disconnect"}</p>
                  </Button>
                )}
              </div>
            ) : (
              ""
            )}
          </SheetContent>
        </Sheet>
      </div>
    </CenteredLoader>
  );
}

export default IntegrationsAndApiKeys;
