import { mcpApi } from "@/app/api/mcp";
import { userApi } from "@/app/api/user";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { UserGeneralSettingsResponse } from "@/shared/interfaces";
import { useQuery } from "@tanstack/react-query";
import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
} from "react";
import { useForm } from "react-hook-form";

interface GeneralSettingRef {
  getFormData: () => any;
  resetForm: () => void;
  isDirty: () => boolean;
}

const GeneralSetting = forwardRef<GeneralSettingRef>((props, ref) => {
  const { data: userGeneralSettings } = useQuery<UserGeneralSettingsResponse>({
    queryKey: ["userGeneralSettings"],
    queryFn: () => userApi.getUserGeneralSettings(),
  });

  // Fetch providers and models on mount
  const [isLoadingProviders, setIsLoadingProviders] = useState(false);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [providers, setProviders] = useState<any[]>([]);
  const [models, setModels] = useState<any[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<string>("");
  const [selectedModel, setSelectedModel] = useState<string>("");
  const form = useForm<any>({
    defaultValues: {
      model_provider: userGeneralSettings?.preference?.provider || "",
      model_name: userGeneralSettings?.preference?.model || "",
      temperature: userGeneralSettings?.preference?.temperature.toFixed(1) || 0,
      max_output_tokens:
        userGeneralSettings?.preference?.max_output_tokens || 0,
    },
  });

  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    getFormData: () => {
      const values = form.getValues();
      return {
        model_provider: values.model_provider,
        model_name: values.model_name,
        temperature: values.temperature,
        max_output_tokens: values.max_output_tokens,
      };
    },
    resetForm: () => {
      form.reset({
        model_provider: userGeneralSettings?.preference?.provider || "",
        model_name: userGeneralSettings?.preference?.model || "",
        temperature:
          userGeneralSettings?.preference?.temperature.toFixed(1) || 0,
        max_output_tokens:
          userGeneralSettings?.preference?.max_output_tokens || 0,
      });
    },
    isDirty: () => {
      const values = form.getValues();
      const defaultValues = {
        model_provider: userGeneralSettings?.preference?.provider || "",
        model_name: userGeneralSettings?.preference?.model || "",
        temperature:
          userGeneralSettings?.preference?.temperature.toFixed(1) || 0,
        max_output_tokens:
          userGeneralSettings?.preference?.max_output_tokens || 0,
      };
      return JSON.stringify(values) !== JSON.stringify(defaultValues);
    },
  }));

  // Update form when userGeneralSettings data is loaded
  useEffect(() => {
    if (userGeneralSettings?.preference) {
      form.reset({
        model_provider: userGeneralSettings.preference.provider || "",
        model_name: userGeneralSettings.preference.model || "",
        temperature: userGeneralSettings.preference.temperature.toFixed(1) || 0,
        max_output_tokens:
          userGeneralSettings.preference.max_output_tokens || 0,
      });
      // Set selected values for UI
      setSelectedProvider(userGeneralSettings.preference.provider || "");
      setSelectedModel(userGeneralSettings.preference.model || "");
    }
  }, [userGeneralSettings, form]);

  useEffect(() => {
    const fetchProvidersAndModels = async () => {
      setIsLoadingProviders(true);
      try {
        const res = await mcpApi.getProviders(1, 60);
        const providerList = res?.providers || [];
        setProviders(providerList);
        if (providerList?.length && form.getValues("model_provider")) {
          //check provider list have model or not
          let providerExist = providerList?.filter(
            (item) => item?.provider == form.getValues("model_provider")
          );
          if (!providerExist?.length) return;
        }
        if (providerList.length > 0) {
          let providerExist = providerList?.filter(
            (item) => item?.provider == form.getValues("model_provider")
          );
          const firstProvider = providerList[0];
          const currentProvider =
            providerExist[0]?.provider || firstProvider.provider;
          setSelectedProvider(currentProvider);
          form.setValue("model_provider", currentProvider);
          setIsLoadingModels(true);
          try {
            const modelRes = await mcpApi.getModels(
              providerExist[0]?.id || firstProvider.id,
              1,
              60
            );
            const modelList = modelRes?.models || [];
            setModels(modelList);
            if (modelList?.length && form.getValues("model_name")) {
              //check provider list have model or not
              let modelExist = modelList?.filter(
                (item) => item?.model == form.getValues("model_name")
              );
              if (!modelExist?.length) {
                return;
              }
            }
            if (modelList.length > 0) {
              let modelExist = modelList?.filter(
                (item) => item?.model == form.getValues("model_name")
              );
              const currentModel = modelExist[0]?.model || modelList[0].model;
              setSelectedModel(currentModel);
              form.setValue("model_name", currentModel);
            }
          } finally {
            setIsLoadingModels(false);
          }
        }
      } finally {
        setIsLoadingProviders(false);
      }
    };
    fetchProvidersAndModels();
  }, [userGeneralSettings]);

  // Fetch models when provider changes (user selection)
  useEffect(() => {
    if (!selectedProvider) return;
    setIsLoadingModels(true);
    const fetchModels = async () => {
      try {
        let providerExist = providers?.filter(
          (item) => item?.provider == selectedProvider
        );

        const modelRes = await mcpApi.getModels(providerExist[0].id, 1, 60);
        const modelList = modelRes?.models || [];
        setModels(modelList);
        if (modelList.length > 0) {
          setSelectedModel(modelList[0].model);
          form.setValue("model_name", modelList[0].model);
        } else {
          setSelectedModel("");
          form.setValue("model_name", "");
        }
      } finally {
        setIsLoadingModels(false);
      }
    };
    // Only fetch if not initial mount (skip if already handled by first effect)
    if (providers.length > 0) {
      fetchModels();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedProvider]);
  return (
    <div className="border-border-muted flex flex-col gap-6 rounded-[8px] border bg-white p-[19px]">
      <div className="flex flex-col gap-6">
        <div className="flex flex-col">
          <p className="font-satoshi-bold text-sm">
            Default LLM for Web Search and General Queries
          </p>
          <span className="font-satoshi-regular text-text-secondary text-sm">
            Select and configure the large-language model (LLM) your agent will
            use to generate responses and solutions. Choose from pre-integrated
            providers or add your own for more flexibility.
          </span>
        </div>
        <Form {...form}>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <FormField
              control={form.control}
              name="model_provider"
              render={({ field }) => (
                <FormItem className="flex flex-col gap-2">
                  <FormLabel className="font-satoshi-bold text-sm data-[error=true]:text-gray-900">
                    Select LLM Provider
                  </FormLabel>
                  <FormControl>
                    <Select
                      value={selectedProvider}
                      onValueChange={async (value) => {
                        setSelectedProvider(value);
                        field.onChange(value);
                        form.setValue("model_name", "");
                      }}
                      disabled={isLoadingProviders}
                    >
                      <SelectTrigger className="w-full text-sm aria-invalid:border-gray-200 aria-invalid:ring-0">
                        {isLoadingProviders ? (
                          <span className="flex items-center">Loading...</span>
                        ) : (
                          <SelectValue placeholder="Select a provider" />
                        )}
                      </SelectTrigger>
                      <SelectContent>
                        {providers.map((provider) => (
                          <SelectItem
                            key={provider.id}
                            value={provider.provider}
                          >
                            {provider.provider}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <div className="min-h-[20px]">
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="model_name"
              render={({ field }) => (
                <FormItem className="flex flex-col gap-2">
                  <FormLabel className="font-satoshi-bold text-sm data-[error=true]:text-gray-900">
                    Select Language Model
                  </FormLabel>
                  <FormControl>
                    <Select
                      value={selectedModel}
                      onValueChange={(value) => {
                        setSelectedModel(value);
                        field.onChange(value);
                      }}
                      disabled={isLoadingModels}
                    >
                      <SelectTrigger className="w-full text-sm aria-invalid:border-gray-200 aria-invalid:ring-0">
                        {isLoadingModels ? (
                          <span className="flex items-center">Loading...</span>
                        ) : (
                          <SelectValue placeholder="Select a model" />
                        )}
                      </SelectTrigger>
                      <SelectContent>
                        {models?.length ? (
                          models.map((model) => (
                            <SelectItem key={model.id} value={model.model}>
                              {model.model}
                            </SelectItem>
                          ))
                        ) : (
                          <p className="text-text-secondary text-sm">
                            No models found
                          </p>
                        )}
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <div className="min-h-[20px]">
                    <FormMessage />
                  </div>
                </FormItem>
              )}
            />
          </div>

          <div className="flex flex-col gap-6">
            <FormField
              control={form.control}
              name="temperature"
              render={({ field }) => (
                <FormItem className="flex flex-col gap-2">
                  <div className="flex flex-row justify-between gap-10">
                    <div>
                      <FormLabel className="font-satoshi-bold text-sm">
                        Temperature
                      </FormLabel>
                      <p className="text-text-secondary text-sm">
                        How creative or deterministic the agent’s responses will
                        be. Lower values (0.1-1.0) make replies more focused and
                        reliable, while higher values (1.1-2.0) lead to more
                        varied or imaginative outputs.
                      </p>
                    </div>
                    <FormControl>
                      <Input
                        type="number"
                        name="temperature"
                        value={form.watch("temperature") ?? 0}
                        onChange={(e) => {
                          let value = parseFloat(e.target.value);
                          if (isNaN(value)) value = 0;
                          value = Math.max(0, Math.min(2, value));
                          form.setValue("temperature", value, {
                            shouldDirty: true,
                            shouldValidate: true,
                          });
                        }}
                        min={0}
                        max={2}
                        step={0.1}
                        className="h-8 max-w-[80px] text-center text-sm aria-invalid:border-gray-200 aria-invalid:ring-0"
                      />
                    </FormControl>
                  </div>

                  <Slider
                    value={[field.value ?? 0]}
                    onValueChange={(value) => {
                      form.setValue("temperature", value[0], {
                        shouldDirty: true,
                      });
                    }}
                    max={2}
                    min={0}
                    step={0.1}
                    className="mt-2 w-full"
                  />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="max_output_tokens"
              render={({ field }) => (
                <FormItem className="flex flex-row gap-2">
                  <div className="flex flex-col">
                    <FormLabel className="font-satoshi-bold text-sm">
                      Maximum Output Tokens
                    </FormLabel>
                    <p className="text-text-secondary text-sm">
                      The highest number of tokens that an agent's model can
                      produce. It's crucial to set this value wisely: if it's
                      too low, the agent may struggle to function, and if it's
                      set above the model's capacity, it could lead to errors.
                    </p>
                  </div>
                  <FormControl>
                    <Input
                      type="number"
                      name="max_output_tokens"
                      value={field.value ?? 0}
                      onChange={(e) =>
                        field.onChange(parseInt(e.target.value) || 0)
                      }
                      min="0"
                      max="4096"
                      step="10"
                      className="h-8 max-w-[80px] text-center text-sm aria-invalid:border-gray-200 aria-invalid:ring-0"
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </Form>
      </div>
    </div>
  );
});

export default GeneralSetting;
