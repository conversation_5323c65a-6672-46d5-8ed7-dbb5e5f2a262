"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { LoadingSpinner } from "@/components/shared/LoadingSpinner";
import { userApi } from "@/app/api/user";
import { organizationApi } from "@/app/api/organization";
import { UserInfo, UserUpdate, Organization } from "@/shared/interfaces";
import { useUserStore } from "@/hooks/use-user";
import { useOrgStore } from "@/hooks/use-organization";
import { authApi } from "@/app/api/auth";
import { useMixpanelTrack } from "@/hooks/useMixPanelTrack";
import { AnalyticsEvents } from "@/shared/enums";

// Profile form schema
const profileSchema = z.object({
  fullName: z.string().min(3, "Full name is required"),
  email: z.string().email("Invalid email address"),
  phoneNumber: z
    .string()
    .optional()
    .refine((val) => !val || /^\d{10}$/.test(val), {
      message: "Phone number must be 10 digits",
    }),
  profile_image: z.string().optional(),
});

type ProfileFormData = z.infer<typeof profileSchema>;

export default function ProfileComponent() {
  const queryClient = useQueryClient();
  const { user, setUser } = useUserStore();
  const {
    currentOrganization,
    isLoadingOrganization: isLoadingCurrentOrg,
    setCurrentOrganization,
    setIsLoadingOrganization,
  } = useOrgStore();
  const track = useMixpanelTrack();
  const [isAlertDialogOpen, setIsAlertDialogOpen] = useState(false);
  const [selectedOrgId, setSelectedOrgId] = useState<string>("");
  const [selectedOrgName, setSelectedOrgName] = useState<string>("");
  const [isSwitchingModalOpen, setIsSwitchingModalOpen] = useState(false);
  const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
  const [logoutInProgress, setLogoutInProgress] = useState(false);

  // Fetch user data
  const {
    data: userInfo,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["user", "info"],
    queryFn: userApi.getCurrentUser,
  });

  // Fetch organizations from API
  const {
    data: organizations = [],
    isLoading: isLoadingOrganizations,
    error: organizationsError,
  } = useQuery({
    queryKey: ["user", "organizations"],
    queryFn: organizationApi.getOrganizationDetails,
  });

  // Form setup
  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phoneNumber: "",
      profile_image: "",
    },
  });

  // Update form values when user data loads
  useEffect(() => {
    if (userInfo) {
      form.reset({
        fullName: userInfo.fullName || "",
        email: userInfo.email || "",
        phoneNumber: userInfo.phoneNumber || "",
      });
    }
  }, [userInfo, form]);

  // Update profile mutation
  const { mutate: updateProfileMutation, isPending: isUpdatingProfileDetails } =
    useMutation({
      mutationFn: (data: UserUpdate) => userApi.updateUserDetails(data),
      onSuccess: (updatedUser) => {
        toast.success("Profile updated successfully");
        // Update the user store with new data
        if (user) {
          setUser({
            ...user,
            fullName: updatedUser.fullName,
            email: updatedUser.email,
            phoneNumber: updatedUser.phoneNumber,
          });
        }

        // Invalidate and refetch user data
        queryClient.invalidateQueries({ queryKey: ["user", "info"] });
      },
      onError: (error: any) => {
        toast.error(error.message || "Failed to update profile");
      },
    });

  // Switch organization mutation
  const {
    mutate: switchOrganizationMutation,
    isPending: isSwitchingOrganization,
  } = useMutation({
    mutationFn: (organisationId: string) =>
      organizationApi.switchOrganizationWithCookies(organisationId),
    onMutate: () => {
      // Close confirmation dialog and show switching modal
      setIsAlertDialogOpen(false);
      setIsSwitchingModalOpen(true);
      setIsLoadingOrganization(true);
    },
    onSuccess: async (response) => {
      try {
        // Get current organization details after switch
        const currentOrgDetails =
          await organizationApi.getCurrentOrganizationDetails();

        // Update store with current organization details
        setCurrentOrganization(currentOrgDetails);

        // Invalidate and refetch organizations list to get updated data
        queryClient.invalidateQueries({ queryKey: ["user", "organizations"] });

        // Close switching modal and show success after data is stored
        setIsSwitchingModalOpen(false);
        toast.success("Organization switched successfully");
      } catch (error) {
        setIsLoadingOrganization(false);
        setIsSwitchingModalOpen(false);
        toast.error("Organization switched but failed to reload data");
      }
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to switch organization");
      setIsSwitchingModalOpen(false);
      setIsLoadingOrganization(false);
    },
  });

  // Remove the logout mutation block (it's not used)

  const handleLogoutClick = () => {
    setIsLogoutModalOpen(true);
  };

  const handleConfirmLogout = async () => {
    try {
      setLogoutInProgress(true); // Use renamed variable
      await authApi.logout();
      window.location.href = "/";
    } catch (error) {
      toast.error("Failed to logout");
      setLogoutInProgress(false); // Use renamed variable
    }
  };

  const handleCancelLogout = () => {
    setIsLogoutModalOpen(false);
  };

  // Handler for organization selection
  const handleOrganizationSelect = (orgId: string) => {
    const selectedOrg = organizations?.find(
      (org: Organization) => org.id === orgId
    );
    // Use currentOrganization.id to determine current org instead of is_primary
    const currentOrgId = currentOrganization?.id;
    if (selectedOrg && selectedOrg.id !== currentOrgId) {
      setSelectedOrgId(orgId);
      setSelectedOrgName(selectedOrg.name);
      setIsAlertDialogOpen(true);
    }
  };

  // Handler for confirming organization switch
  const handleConfirmSwitch = () => {
    if (selectedOrgId) {
      switchOrganizationMutation(selectedOrgId);
    }
  };

  // Handler for cancelling organization switch
  const handleCancelSwitch = () => {
    setIsAlertDialogOpen(false);
    setSelectedOrgId("");
    setSelectedOrgName("");
  };

  const onSubmit = (data: ProfileFormData) => {
    const updateData: UserUpdate = {
      full_name: data.fullName,
      phone_number: data.phoneNumber || null,
    };

    updateProfileMutation(updateData);
  };

  if (isLoading) {
    return (
      <div className="w-full flex flex-col gap-[24px]">
        {/* Header Section Skeleton */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <Skeleton className="h-7 w-32" />
            <Skeleton className="h-5 w-64" />
          </div>
          <div className="flex items-center gap-4">
            <Skeleton className="h-10 w-20" />
            <Skeleton className="h-10 w-20" />
          </div>
        </div>

        {/* Profile Form Skeleton */}
        <Card className="bg-color-card-color border-border-muted p-[20px] shadow-none bg-white">
          <CardContent className="p-0">
            <div className="space-y-8">
              {/* Full Name Field Skeleton */}
              <div className="flex">
                <div className="space-y-2 min-w-[320px]">
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <Skeleton className="h-12 w-[486px]" />
              </div>

              <hr className="border-color-border-default" />

              {/* Email Field Skeleton */}
              <div className="flex">
                <div className="space-y-2 min-w-[320px]">
                  <Skeleton className="h-5 w-28" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <Skeleton className="h-12 w-[486px]" />
              </div>

              <hr className="border-color-border-default" />

              {/* Phone Number Field Skeleton */}
              <div className="flex">
                <div className="space-y-2 min-w-[320px]">
                  <Skeleton className="h-5 w-28" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <Skeleton className="h-12 w-[486px]" />
              </div>

              <hr className="border-color-border-default" />

              {/* Organization Field Skeleton */}
              <div className="flex">
                <div className="space-y-2 min-w-[320px]">
                  <Skeleton className="h-5 w-24" />
                  <Skeleton className="h-4 w-48" />
                </div>
                <Skeleton className="h-12 w-[486px]" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Logout Button Skeleton */}
        <div>
          <div className="flex">
            <div className="flex flex-col gap-2">
              <Skeleton className="h-10 w-[100px]" />
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="text-center text-color-error">
          Failed to load profile information
        </div>
      </div>
    );
  }

  return (
    <div className="w-full flex flex-col gap-[24px]">
      {/* Header Section */}
      <div className="flex flex-col xl:flex-row items-start gap-[20px] xl:items-center justify-between mt-[50px] md:mt-0">
        <div className="space-y-1">
          <h1 className="text-xl font-satoshi-bold text-color-text-primary">
            Your Profile
          </h1>
          <p className="text-sm font-satoshi-regular text-text-secondary">
            Collaborate, build, and deploy AI agents securely in one workplace.
          </p>
        </div>

        <div className="flex items-center gap-4 justify-end w-full xl:w-auto">
          <Button
            onClick={() => {
              form.reset();
            }}
            type="button"
            variant={"tertiary"}
            className="w-[80px] p-3"
          >
            Cancel
          </Button>
          <Button
            onClick={form.handleSubmit(onSubmit)}
            type="button"
            variant="primary"
            className="min-w-[80px] p-3"
            disabled={!form.formState.isDirty || isUpdatingProfileDetails}
          >
            {isUpdatingProfileDetails && (
              <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            )}

            {"Save"}
          </Button>
        </div>
      </div>

      {/* Profile Form */}
      <Card className="bg-color-card-color border-border-muted p-[20px] shadow-none bg-white">
        <CardContent className="p-0">
          <Form {...form}>
            <form id="profile-form" onSubmit={form.handleSubmit(onSubmit)}>
              {/* Full Name Field */}
              <div>
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem className="flex flex-col xl:flex-row">
                      <div className="space-y-2 lg:min-w-[320px]">
                        <FormLabel className="text-sm font-satoshi-bold data-[error=true]:text-text-primary">
                          Full Name
                        </FormLabel>
                        <p className="text-sm font-satoshi-regular text-text-secondary">
                          Enter full name, including first & last name
                        </p>
                      </div>
                      <div className="flex flex-col gap-2">
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="Enter your full name"
                            className={`h-12 px-3 max-w-[486px] lg:min-w-[486px] min-w-0 text-sm rounded-lg transition-colors`}
                          />
                        </FormControl>
                        <FormMessage className="!text-color-error text-sm" />
                      </div>
                    </FormItem>
                  )}
                />

                {/* Divider */}
                <div className="h-px bg-color-border-default w-full" />
              </div>
              <hr className="border-color-border-default my-[20px]" />
              {/* Email Address Field */}
              <div>
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="flex flex-col xl:flex-row">
                      <div className="space-y-2 lg:min-w-[320px]">
                        <FormLabel className="text-sm font-satoshi-bold data-[error=true]:text-text-primary">
                          Email Address
                        </FormLabel>
                        <p className="text-sm font-satoshi-regular text-text-secondary">
                          This is the email you used to log in
                        </p>
                      </div>
                      <FormControl>
                        <Input
                          {...field}
                          disabled={true} // Email is always disabled as per design
                          type="email"
                          className="h-12 px-3 max-w-[486px] text-sm bg-background-muted text-text-primary rounded-lg"
                        />
                      </FormControl>
                      <FormMessage className="text-color-error text-sm" />
                    </FormItem>
                  )}
                />

                {/* Divider */}
                <div className="h-px bg-color-border-default w-full" />
              </div>
              <hr className="border-color-border-default my-[20px]" />

              {/* Phone Number Field */}
              <div>
                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem className="flex flex-col xl:flex-row">
                      <div className="space-y-2 lg:min-w-[320px]">
                        <FormLabel className="text-sm font-satoshi-bold !text-text-primary data-[error=true]:text-color-primary">
                          Phone Number
                        </FormLabel>
                        <p className="text-sm font-satoshi-regular text-text-secondary">
                          Your number helps us keep account secure
                        </p>
                      </div>
                      <div className="flex flex-col gap-2">
                        <FormControl>
                          <Input
                            type="number"
                            value={field.value || ""}
                            onChange={(e) => {
                              // Only allow up to 10 digits
                              let val = e.target.value.replace(/[^\d]/g, "");
                              if (val.length > 10) val = val.slice(0, 10);
                              field.onChange(val);
                            }}
                            onWheel={(e) =>
                              (e.target as HTMLInputElement).blur()
                            }
                            placeholder="Enter your phone number"
                            className={`h-12 px-3 max-w-[486px] lg:min-w-[486px] min-w-0 text-sm rounded-lg transition-colors`}
                          />
                        </FormControl>
                        <FormMessage className="!text-color-error text-sm" />
                      </div>
                    </FormItem>
                  )}
                />

                {/* Divider */}
                <div className="h-px bg-color-border-default w-full" />
              </div>

              <hr className="border-color-border-default my-[20px]" />

              {/* Organization Switcher Field */}
              <div>
                <div className="flex flex-col xl:flex-row">
                  <div className="space-y-2 lg:min-w-[320px]">
                    <Label className="text-sm font-satoshi-bold !text-text-primary">
                      Organization
                    </Label>
                    <p className="text-sm font-satoshi-regular text-text-secondary">
                      Switch between your organizations
                    </p>
                  </div>
                  <div className="flex flex-col gap-2">
                    <Select
                      value={currentOrganization?.id || ""}
                      onValueChange={handleOrganizationSelect}
                      disabled={
                        isLoadingOrganizations || !organizations?.length
                      }
                    >
                      <SelectTrigger className="h-12 px-3 max-w-[486px] lg:min-w-[486px] min-w-0 text-sm rounded-lg transition-colors py-6 ml-2">
                        <SelectValue placeholder="Select organization" />
                      </SelectTrigger>
                      <SelectContent>
                        {organizations?.map((org: Organization) => (
                          <SelectItem
                            key={org.id}
                            value={org.id}
                            textValue={org.name}
                          >
                            <div className="flex items-center gap-2">
                              {org.logo ? (
                                <img
                                  src={org.logo}
                                  alt={`${org.name} logo`}
                                  className="h-6 w-6 rounded-sm object-contain"
                                />
                              ) : (
                                <div className="h-6 w-6 rounded-sm bg-gray-200 flex items-center justify-center text-xs text-gray-500">
                                  {org.name.charAt(0)}
                                </div>
                              )}
                              <span>{org.name}</span>
                              {org.id === currentOrganization?.id && (
                                <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded">
                                  Current
                                </span>
                              )}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {isLoadingOrganizations && (
                      <p className="text-sm text-text-secondary">
                        Loading organizations...
                      </p>
                    )}
                  </div>
                </div>

                {/* Divider */}
                <div className="h-px bg-color-border-default w-full" />
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>

      {/* Logout Button Section */}
      <div>
        <div className="flex">
          <div className="flex flex-col gap-2">
            <Button
              variant="destructive"
              onClick={handleLogoutClick}
              className="w-[100px]"
            >
              Logout
            </Button>
          </div>
        </div>
      </div>

      {/* Organization Switch Confirmation Dialog */}
      <AlertDialog open={isAlertDialogOpen} onOpenChange={setIsAlertDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Switch Organization</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to switch to "{selectedOrgName}"? This will
              reload your session with the new organization's data.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelSwitch}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmSwitch}>
              Switch Organization
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Organization Switching Loading Modal */}
      <AlertDialog open={isSwitchingModalOpen} onOpenChange={() => {}}>
        <AlertDialogContent className="max-w-md">
          <AlertDialogHeader>
            <div className="flex items-center justify-center mb-4">
              <div className="w-12 h-12 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
            </div>
            <AlertDialogTitle className="text-center">
              Switching Organization
            </AlertDialogTitle>
            <AlertDialogDescription className="text-center">
              We're switching you to "{selectedOrgName}". Please don't refresh
              or close this page.
            </AlertDialogDescription>
          </AlertDialogHeader>
        </AlertDialogContent>
      </AlertDialog>

      {/* Logout Confirmation Dialog */}
      <AlertDialog open={isLogoutModalOpen} onOpenChange={setIsLogoutModalOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Logout</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to log out? You will need to log in again to
              access your account.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelLogout}>
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                const date = new Date();
                track(AnalyticsEvents.USER_LOGOUT, {
                  location: window.location.href,
                  timestamp: date.toString(),
                });
                handleConfirmLogout();
              }}
              disabled={logoutInProgress} // Use renamed variable
            >
              {logoutInProgress ? ( // Use renamed variable
                <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              ) : null}
              Logout
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
