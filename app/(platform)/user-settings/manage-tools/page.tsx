"use client";

import { Card, CardContent } from "@/components/ui/card";
import React, { useState, useCallback, useEffect } from "react";
import ToolCard from "../../_components/ToolCard";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { mcpApi } from "@/app/api/mcp";
import { apiKeyAndIntegration } from "@/app/api/apiKeysAndIntegration";
import { LoadingSpinner } from "@/components/shared/LoadingSpinner";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { toast } from "sonner";
import { useUserStore } from "@/hooks/use-user";
import { marketplaceMcpsUrl } from "@/shared/constants";
import Image from "next/image";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import { dashboardRoute } from "@/shared/routes";

function ManageTools() {
  const { user } = useUserStore();
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState("");
  const [toolFilter, setToolFilter] = useState<boolean>(true); // Default to "Quick Tools"
  const [oauthAuthStatus, setOauthAuthStatus] = useState<{
    [toolId: string]: {
      isAuthenticated: boolean;
      isLoading: boolean;
      toolName?: string;
      error?: string;
    };
  }>({});
  const [envKeyValues, setEnvKeyValues] = useState<{
    [toolId: string]: { [key: string]: string };
  }>({});
  const [fetchedToolIds, setFetchedToolIds] = useState<Set<string>>(new Set());
  const [originalEnvKeyValues, setOriginalEnvKeyValues] = useState<{
    [toolId: string]: { [key: string]: string };
  }>({});
  const [disconnectingTool, setDisconnectingTool] = useState<any>({});
  const [removingTool, setRemovingTool] = useState<string>("");
  const [toolConnectionStatus, setToolConnectionStatus] = useState<
    Record<string, boolean>
  >({});
  const [apiKeyConfig, setApiKeyConfig] = useState<{
    [integrationId: string]: any[];
  }>({});
  const router = useRouter();

  const {
    data: mcpData,
    isFetching: isLoadingTools,
    refetch: refetchMcpTools,
  } = useQuery({
    queryKey: ["mcps", toolFilter],
    queryFn: () => mcpApi.getMcpServersByUser(1, 60, toolFilter),
    staleTime: 30 * 60 * 100,
  });
  // Use any type to avoid TypeScript errors with the extended properties
  const tools: any[] = mcpData?.data || [];

  // Function to check OAuth authentication status for a specific tool
  const checkOAuthAuthStatus = useCallback(
    async (toolId: string, oauth_details: any) => {
      if (!oauth_details) return;

      // Set loading state
      setOauthAuthStatus((prev) => ({
        ...prev,
        [toolId]: {
          isAuthenticated: false,
          isLoading: true,
          toolName: oauth_details.tool_name,
        },
      }));

      try {
        // First check if the tool is already connected directly from the tool object
        const tool = tools.find((t) => t.id === toolId);
        if (
          tool &&
          tool.integrations &&
          tool.integrations.length > 0 &&
          tool.is_connected
        ) {
          setOauthAuthStatus((prev) => ({
            ...prev,
            [toolId]: {
              isAuthenticated: true,
              isLoading: false,
              toolName: oauth_details.tool_name,
            },
          }));
          return;
        }

        // If not directly connected, check via API
        const data = await apiKeyAndIntegration.getOauthCredentials(toolId);

        setOauthAuthStatus((prev) => ({
          ...prev,
          [toolId]: {
            isAuthenticated:
              data?.user_integration_status?.is_connected || false,
            isLoading: false,
            toolName: oauth_details.tool_name,
          },
        }));
      } catch (error: any) {
        // Handle errors
        setOauthAuthStatus((prev) => ({
          ...prev,
          [toolId]: {
            isAuthenticated: false,
            isLoading: false,
            toolName: oauth_details.tool_name,
            error: "not_authenticated",
          },
        }));
      }
    },
    [tools]
  );

  // Check OAuth authentication when tools are loaded
  useEffect(() => {
    if (tools && tools.length > 0) {
      tools.forEach((tool: any) => {
        if (tool.oauth_details && !oauthAuthStatus[tool.id]) {
          checkOAuthAuthStatus(tool.id, tool.oauth_details);
        }
      });
    }
  }, [tools, checkOAuthAuthStatus, oauthAuthStatus]);

  // Fetch environment key values for tools with provided credentials
  const { mutate: getMcpsEnvsById, isPending: isGettingMcpsEnvsById } =
    useMutation({
      mutationFn: (integrationId: string) =>
        apiKeyAndIntegration.getApiKeyCredentials(integrationId),
      onSuccess: (response, integrationId) => {
        if (response) {
          // Store credentials if available
          if (response.credentials) {
            setEnvKeyValues((prev) => ({
              ...prev,
              [integrationId]: response.credentials,
            }));
            // Store original values for comparison
            setOriginalEnvKeyValues((prev) => ({
              ...prev,
              [integrationId]: response.credentials,
            }));
          }

          // Store API key configuration
          if (response.api_key_config) {
            setApiKeyConfig((prev) => ({
              ...prev,
              [integrationId]: response.api_key_config,
            }));
          }

          // Mark this integration as fetched
          setFetchedToolIds((prev) => new Set(prev).add(integrationId));

          // Set connection status
          setToolConnectionStatus((prev) => ({
            ...prev,
            [integrationId]: response?.is_connected || false,
          }));
        }
      },
      onError: (error) => {
        console.error("Failed to fetch environment key values:", error);
        toast.error("Failed to fetch environment key values", {
          position: "top-center",
        });
      },
    });

  // Add/Update environment keys
  const { mutate: addMcpEnvKeys, isPending: isAddingEnvKeys } = useMutation({
    mutationFn: ({
      mcpId,
      envKeyValues,
    }: {
      mcpId: string;
      envKeyValues: { key: string; value: string }[];
    }) => {
      // Convert array of key-value pairs to object format required by apiKeyAndIntegration
      const credentials: Record<string, string> = {};
      envKeyValues.forEach(({ key, value }) => {
        credentials[key] = value;
      });
      return apiKeyAndIntegration.addApiKeyCredentials(mcpId, credentials);
    },
    onSuccess: (response, { mcpId }) => {
      toast.success("Environment keys updated successfully", {
        position: "top-center",
      });
      getMcpsEnvsById(mcpId);
      queryClient.invalidateQueries({ queryKey: ["integrations"] });
      queryClient.invalidateQueries({ queryKey: ["connectedKeys"] });
      queryClient.invalidateQueries({ queryKey: ["mcps"] });
    },
    onError: (error) => {
      console.error("Failed to update environment keys:", error);
      toast.error("Failed to update environment keys", {
        position: "top-center",
      });
    },
  });

  // Remove a tool
  const { mutate: removeTool, isPending: isRemovingTool } = useMutation({
    mutationFn: (toolId: string) => mcpApi.deleteQuickTool(toolId),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["mcps"] });
      toast.success(response.message || "Tool removed successfully", {
        position: "top-center",
      });
      setRemovingTool("");
    },
    onError: (error) => {
      toast.error(error.message);
      setRemovingTool("");
    },
  });

  // Remove OAuth credentials
  const {
    mutate: removeAuthCredentialDetail,
    isPending: isRemovingAuthCredential,
  } = useMutation({
    mutationFn: (tool: any) =>
      apiKeyAndIntegration.disconnectOauthCredentials(tool?.integrations[0]),
    onSuccess: (response) => {
      queryClient.invalidateQueries({ queryKey: ["mcps"] });
      queryClient.invalidateQueries({ queryKey: ["integrations"] });
      queryClient.invalidateQueries({ queryKey: ["connectedKeys"] });
      toast.success(response.message || "Credentials deleted successfully", {
        position: "top-center",
      });

      // For API key tools, refresh the env keys
      if (
        disconnectingTool?.integrations &&
        disconnectingTool?.integrations.length > 0 &&
        disconnectingTool?.integration_type == "api_key"
      ) {
        // Refresh the environment key values for this tool
        getMcpsEnvsById(disconnectingTool.integrations[0]);
      }

      // Re-check OAuth status for OAuth tools
      if (disconnectingTool?.id && disconnectingTool?.oauth_details) {
        checkOAuthAuthStatus(
          disconnectingTool.id,
          disconnectingTool.oauth_details
        );
      }

      // Refresh the data for all tools to ensure accurate connection status
      refetchMcpTools();
    },
    onError: (error) => {
      toast.error(
        `Failed to remove OAuth credentials: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    },
  });

  // Handle OAuth login
  const handleOAuthLogin = (tool: any) => {
    if (!tool.integrations?.length) {
      toast.error("Integration id not found for this tool.");
      return;
    }

    const redirect_url = window.location.href;
    const user_id = user?.id;

    const oauthParams = new URLSearchParams({
      user_id: user_id || "",
      redirect_url: redirect_url || "", // Redirect back to current page after OAuth
    });
    const authUrl = `${
      process.env.NEXT_PUBLIC_API_URL
    }/integrations/${tool.integrations[0]}/oauth/authorize?${oauthParams.toString()}`;
    window.location.href = authUrl;
  };

  // Fetch environment key values for tools with integrations
  useEffect(() => {
    if (tools && tools.length > 0) {
      tools.forEach((tool: any) => {
        if (
          tool.integrations &&
          tool.integrations.length > 0 &&
          tool.integration_type === "api_key" &&
          !fetchedToolIds.has(tool.integrations[0])
        ) {
          getMcpsEnvsById(tool.integrations[0]);
        }
      });
    }
  }, [tools, fetchedToolIds]);

  // Filter tools based on search term
  const filteredTools = tools.filter((tool: any) => {
    return tool.name.toLowerCase().includes(searchTerm.toLowerCase());
  });

  if (isLoadingTools) {
    return <LoadingSpinner message="Loading tools data..." />;
  }

  return (
    <div className="w-full h-full flex flex-col gap-6 md:p-[28px] p-[14px] max-h-[100vh] overflow-y-scroll">
      <div className="flex flex-col xl:flex-row items-start gap-[20px] xl:items-center justify-between mt-[50px] md:mt-0">
        <div className="space-y-1">
          <h1 className="text-xl font-satoshi-bold text-text-primary">
            Manage Your Tools
          </h1>
          <p className="text-sm font-satoshi-regular text-text-secondary">
            Collaborate, build, and deploy AI agents securely in one workplace.
          </p>
        </div>
        <div className="flex gap-4">
          <Button
            variant={"tertiary"}
            className="w-max xl:w-[217px] "
            onClick={() => router.push(dashboardRoute)}
          >
            Back to Home
          </Button>
          <Button
            className="w-max xl:w-[217px]"
            onClick={() => window.open(marketplaceMcpsUrl, "_blank")}
          >
            Add From Marketplace
            <Plus className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <Card className="bg-color-card-color border-border-muted p-[20px] shadow-none bg-white">
        <CardContent className="p-0">
          <div className="flex flex-col xl:flex-row gap-[20px] xl:items-center justify-between mt-[50px] md:mt-0  mb-6">
            <div>
              <h2 className="text-[20px] font-satoshi-bold">Tool Listing</h2>
              <span className="text-[14px] text-text-secondary">
                Plug in tools/MCP servers for quicker task execution through
                your digital employees
              </span>
            </div>
            <div className="flex items-center gap-4">
              <Select
                value={toolFilter.toString()}
                onValueChange={(value: string) => {
                  setToolFilter(value === "true");
                  // Reset states when changing filter
                  setOauthAuthStatus({});
                  setFetchedToolIds(new Set());
                }}
              >
                <SelectTrigger className="w-[180px] text-sm font-satoshi-bold">
                  <SelectValue placeholder="Filter tools" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={"false"}>All Tools</SelectItem>
                  <SelectItem value={"true"}>Quick Tools</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {tools.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16">
              <Image
                src={"/assets/dashboard/create-agent.svg"}
                width={216}
                height={120}
                alt={"tools-img"}
              />
              <p className="text-text-secondary mt-4">No tools added yet</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {tools.map((tool: any) => (
                <ToolCard
                  key={tool.id}
                  tool={tool}
                  oauthStatus={oauthAuthStatus[tool.id]}
                  envKeyValues={envKeyValues[tool.id] || {}}
                  isGettingEnvs={isGettingMcpsEnvsById}
                  isAddingEnvKeys={isAddingEnvKeys}
                  isRemovingOAuth={
                    isRemovingAuthCredential &&
                    disconnectingTool?.id === tool.id
                  }
                  apiKeyConfig={apiKeyConfig}
                  toolConnectionStatus={toolConnectionStatus}
                  onEnvKeyChange={(key, value) => {
                    setEnvKeyValues((prev) => ({
                      ...prev,
                      [tool.id]: {
                        ...(prev[tool.id] || {}),
                        [key]: value,
                      },
                    }));
                  }}
                  onAddEnvKeys={() => {
                    if (!tool.integrations || tool.integrations.length === 0)
                      return;

                    const integrationId = tool.integrations[0];
                    // Use tool.id to get the env values since that's where we store them
                    const currentEnvValues = envKeyValues[tool.id] || {};

                    const allEnvKeyValues = Object.entries(currentEnvValues)
                      .map(([key, value]) => ({
                        key,
                        value: value || "",
                      }))
                      .filter((item) => item.value.trim());

                    if (allEnvKeyValues.length > 0) {
                      addMcpEnvKeys({
                        mcpId: integrationId,
                        envKeyValues: allEnvKeyValues,
                      });
                    }
                  }}
                  onOAuthLogin={() => handleOAuthLogin(tool)}
                  onOAuthDisconnect={() => {
                    removeAuthCredentialDetail(tool);
                    setDisconnectingTool(tool);
                  }}
                  onRemoveTool={() => {
                    setRemovingTool(tool.id);
                    removeTool(tool.id);
                  }}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default ManageTools;
