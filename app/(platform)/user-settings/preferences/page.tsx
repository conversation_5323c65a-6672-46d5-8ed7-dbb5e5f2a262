"use client";
import { But<PERSON> } from "@/components/ui/button";
import React, { useState, useRef } from "react";
import GeneralSetting from "../_components/GeneralSetting";
import IntegrationsAndApiKeys from "../_components/IntegrationsAndApiKeys";
import { userApi } from "@/app/api/user";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

function PreferencesComponent() {
  const [activeTab, setActiveTab] = useState("general_settings");
  const [isSaving, setIsSaving] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const queryClient = useQueryClient();
  const generalSettingRef = useRef<any>(null);

  // Check if form is dirty
  React.useEffect(() => {
    const checkDirty = () => {
      if (generalSettingRef.current?.isDirty) {
        setIsDirty(generalSettingRef.current.isDirty());
      }
    };

    checkDirty();
    const interval = setInterval(checkDirty, 500);
    return () => clearInterval(interval);
  }, []);

  const handleSave = async () => {
    if (activeTab === "general_settings") {
      setIsSaving(true);
      try {
        // Get form data from GeneralSetting component
        const formData = generalSettingRef.current?.getFormData();
        if (!formData) {
          toast.error("Please fill in all required fields");
          return;
        }

        // Call the API to update user general settings
        await userApi.updateUserGeneralSettings({
          provider: formData.model_provider,
          model: formData.model_name,
          temperature: formData.temperature,
          max_output_tokens: formData.max_output_tokens,
        });

        // Show success message
        toast.success("Settings saved successfully");

        // Refetch the updated data
        await queryClient.invalidateQueries({
          queryKey: ["userGeneralSettings"],
        });
        setIsDirty(false);
      } catch (error: any) {
        toast.error(error.message || "Failed to save settings");
      } finally {
        setIsSaving(false);
      }
    }
  };

  const handleCancel = () => {
    // Reset form data if needed
    if (generalSettingRef.current?.resetForm) {
      generalSettingRef.current.resetForm();
    }
    setIsDirty(false);
    toast.info("Changes cancelled");
  };

  return (
    <div className="flex md:p-[28px] p-[14px] w-full flex-col gap-6 md:max-h-[100vh] overflow-auto">
      <div className="flex flex-col xl:flex-row items-start gap-[20px] xl:items-center justify-between mt-[50px] md:mt-0">
        <div className="space-y-1">
          <h1 className="text-xl font-satoshi-bold text-color-text-primary">
            Preferences
          </h1>
          <p className="text-sm font-satoshi-regular text-color-text-secondary">
            Collaborate, build, and deploy AI agents securely in one workplace.
          </p>
        </div>
        <div className="flex items-center gap-4 justify-end w-full xl:w-auto">
          <Button
            type="button"
            variant={"tertiary"}
            className="w-[80px] p-3"
            onClick={handleCancel}
            disabled={isSaving}
          >
            Cancel
          </Button>
          <Button
            type="button"
            variant="primary"
            className="min-w-[80px] p-3"
            onClick={handleSave}
            disabled={isSaving || !isDirty}
          >
            {isSaving ? "Saving..." : "Save"}
          </Button>
        </div>
      </div>
      <div className="flex items-center justify-between mb-[7px] border-b border-border h-max">
        <div className="flex space-x-4">
          <button
            className={`pb-4 px-1 text-sm font-medium ${
              activeTab === "general_settings"
                ? "border-b-2 border-brand-primary text-brand-primary"
                : "text-primary-font"
            }`}
            onClick={() => setActiveTab("general_settings")}
          >
            General Settings
          </button>
        </div>
      </div>
      {activeTab === "general_settings" && (
        <GeneralSetting ref={generalSettingRef} />
      )}
    </div>
  );
}

export default PreferencesComponent;
