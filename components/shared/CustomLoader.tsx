import React from "react";
import { LoaderIcon } from "lucide-react";

interface Props {
  loadingText?: string;
}
const CustomLoader = ({ loadingText }: Props) => {
  return (
    <div className="flex flex-col gap-4 items-center justify-center mt-20  md:w-[450px] lg:w-full">
      <LoaderIcon className="w-10 h-10 animate-spin text-brand-primary" />
      <h1 className="text-brand-primary-font text-xl font-semibold text-center">
        {loadingText || "Loading..."}
      </h1>
    </div>
  );
};

export default CustomLoader;
