import React from "react";
import { BookOpen, Database } from "lucide-react";

interface KnowledgeFetchIndicatorProps {
  className?: string;
}

export const KnowledgeFetchIndicator: React.FC<
  KnowledgeFetchIndicatorProps
> = ({ className = "" }) => {
  return (
    <div
      className={`
        flex items-center gap-4 p-4 rounded-2xl 
        bg-gradient-to-r from-blue-50 to-indigo-50
        border border-blue-200/50
        shadow-sm max-w-lg
        ${className}
      `}
    >
      {/* Icon Container */}
      <div className="flex-shrink-0">
        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg">
          <div className="relative">
            <BookOpen className="w-6 h-6 text-white" />
            <Database className="w-3 h-3 text-white/70 absolute -bottom-1 -right-1" />
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <h4 className="font-satoshi-bold text-base text-blue-700">
            Knowledge Retrieval
          </h4>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse [animation-delay:0.2s]"></div>
            <div className="w-2 h-2 bg-blue-300 rounded-full animate-pulse [animation-delay:0.4s]"></div>
          </div>
        </div>
        <p className="text-sm text-blue-600 leading-relaxed">
          Searching through knowledge base to find relevant information for your
          query...
        </p>
      </div>
    </div>
  );
};
