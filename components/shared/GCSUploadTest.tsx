"use client";

import { useState } from "react";
import { gcsApi, uploadToGCS } from "@/app/api/gcs";
import { toast } from "sonner";
import { Loader2 } from "lucide-react";

export const GCSUploadTest = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [uploadedUrl, setUploadedUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    setIsLoading(true);
    setError(null);

    try {
      // Get presigned URL
      const presignedUrlResponse = await gcsApi.getPresignedUrl(
        file.name,
        file.type,
        "test-uploads" // You can customize this path
      );

      if (!presignedUrlResponse.url) {
        throw new Error("No presigned URL received from server");
      }

      // Upload file using presigned URL
      await uploadToGCS(presignedUrlResponse.url, file);
      toast.success("File uploaded successfully!");
    } catch (error: any) {
      console.error("Upload error:", error);
      const errorMessage = error.message || "Failed to upload file";
      setError(errorMessage);

      // Log detailed error information
      if (error.response) {
        console.error("Error response:", {
          status: error.response.status,
          data: error.response.data,
          headers: error.response.headers,
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg max-w-md mx-auto mt-8">
      <h2 className="text-lg font-semibold mb-4">GCS Upload Test</h2>

      <div className="space-y-4">
        <div>
          <label
            htmlFor="file-upload"
            className="block text-sm font-medium text-gray-700 mb-2"
          >
            Select Image to Upload
          </label>
          <input
            id="file-upload"
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            disabled={isLoading}
            className="block w-full text-sm text-gray-500
              file:mr-4 file:py-2 file:px-4
              file:rounded-md file:border-0
              file:text-sm file:font-semibold
              file:bg-brand-primary file:text-white
              hover:file:bg-brand-primary/90
              disabled:opacity-50 disabled:cursor-not-allowed"
          />
        </div>

        {isLoading && (
          <div className="flex items-center justify-center gap-2 text-sm text-gray-600">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Uploading...</span>
          </div>
        )}

        {error && (
          <div className="text-sm text-red-500 p-2 bg-red-50 rounded">
            Error: {error}
          </div>
        )}

        {uploadedUrl && (
          <div className="space-y-2">
            <p className="text-sm font-medium">Uploaded Image:</p>
            <img
              src={uploadedUrl}
              alt="Uploaded file preview"
              className="max-w-full h-auto rounded-lg border"
            />
            <p className="text-xs text-gray-500 break-all">
              URL: {uploadedUrl}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
