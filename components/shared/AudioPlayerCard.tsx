import React, { useRef, useState } from "react";
import { Play, Pause, Volume2 } from "lucide-react";

interface AudioPlayerCardProps {
  src: string;
}

const AudioPlayerCard: React.FC<AudioPlayerCardProps> = ({ src }) => {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState<number | null>(null);

  const handlePlayPause = () => {
    if (!audioRef.current) return;
    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const handleEnded = () => setIsPlaying(false);

  const handleLoadedMetadata = () => {
    if (audioRef.current) {
      setDuration(audioRef.current.duration);
    }
  };

  const formatDuration = (seconds: number | null) => {
    if (seconds == null || isNaN(seconds)) return "--:--";
    const m = Math.floor(seconds / 60);
    const s = Math.floor(seconds % 60);
    return `${m}:${s.toString().padStart(2, "0")}`;
  };

  return (
    <div className="flex items-center justify-between rounded-2xl border border-[var(--brand-secondary)] bg-white px-6 py-4 shadow-sm mb-4  ">
      {/* Play Button */}
      <button
        className="flex items-center justify-center w-10 h-10 rounded-full bg-[var(--brand-secondary)] hover:bg-[var(--brand-primary)] transition-colors mr-6 shadow-md"
        onClick={handlePlayPause}
        aria-label={isPlaying ? "Pause" : "Play"}
        type="button"
      >
        {isPlaying ? (
          <Pause className="w-5 h-5 text-white" />
        ) : (
          <Play className="w-5 h-5 text-white" />
        )}
      </button>
      <audio
        ref={audioRef}
        src={src}
        onEnded={handleEnded}
        onLoadedMetadata={handleLoadedMetadata}
      />

      {/* Duration and Volume */}
      <div className="flex flex-row items-end ml-4 min-w-[60px] gap-4">
        <span className="text-text-secondary text-sm font-satoshi-bold">
          {formatDuration(duration)}
        </span>
        <Volume2 className="w-6 h-6 text-gray-700 mt-1" />
      </div>
    </div>
  );
};

export default AudioPlayerCard;
