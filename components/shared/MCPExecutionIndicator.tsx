import React from "react";
import { Wrench } from "lucide-react";
import { sanitizeString } from "@/services/helper";

interface MCPExecutionIndicatorProps {
  toolName: string;
  logo?: string;
  description?: string;
  className?: string;
}

export const MCPExecutionIndicator: React.FC<MCPExecutionIndicatorProps> = ({
  toolName,
  logo,
  description,
  className = "",
}) => {
  // Truncate description to 150-200 characters
  const truncateDescription = (text: string, maxLength: number = 175) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength).trim() + "...";
  };

  // Check if logo is valid (not empty, null, or just whitespace)
  const hasValidLogo = logo && logo.trim() !== "";

  return (
    <div
      className={`
        flex items-center gap-3 p-4 rounded-2xl 
        bg-gradient-to-r from-purple-50 to-purple-100
        border-8 border-brand-primary/10
        shadow-sm max-w-lg
        ${className}
      `}
    >
      {/* Logo/Icon with Infinite Rotating Ring */}
      <div className="flex-shrink-0 relative">
        {/* Rotating Spinner Ring */}
        <svg
          className="absolute inset-0 w-12 h-12 animate-spin"
          viewBox="0 0 48 48"
        >
          {/* Background circle */}
          <circle
            cx="24"
            cy="24"
            r="20"
            fill="none"
            stroke="#e5e7eb"
            strokeWidth="2"
          />
          {/* Spinning circle */}
          <circle
            cx="24"
            cy="24"
            r="20"
            fill="none"
            stroke="url(#progressGradient)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeDasharray="31.416 31.416" /* 25% of circumference visible */
          />
          <defs>
            <linearGradient
              id="progressGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" stopColor="#8b5cf6" />
              <stop offset="100%" stopColor="#a855f7" />
            </linearGradient>
          </defs>
        </svg>

        {/* Logo/Icon Container */}
        {hasValidLogo ? (
          <div className="w-12 h-12 rounded-full overflow-hidden bg-white border-2 border-purple-200 p-1.5">
            <img
              src={logo}
              alt={`${sanitizeString(toolName)} logo`}
              className="w-full h-full object-contain rounded-full"
              onError={(e) => {
                // Fallback to Wrench icon if image fails to load
                const target = e.target as HTMLImageElement;
                target.style.display = "none";
                const parent = target.parentElement;
                if (parent) {
                  parent.innerHTML = `
                    <div class="w-full h-full bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center rounded-full">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/>
                      </svg>
                    </div>
                  `;
                }
              }}
            />
          </div>
        ) : (
          <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 flex items-center justify-center">
            <Wrench className="w-5 h-5 text-white" />
          </div>
        )}
      </div>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-1">
          <h4 className="font-satoshi-bold text-base text-brand-primary truncate">
            {sanitizeString(toolName)} MCP
          </h4>
        </div>
        <p className="text-sm text-gray-700 leading-relaxed">
          {description ? (
            truncateDescription(sanitizeString(description))
          ) : (
            <>
              The tool{" "}
              <span className="font-satoshi-medium text-purple-600">
                {sanitizeString(toolName)}
              </span>{" "}
              is getting executed, kindly wait until your output is ready
            </>
          )}
        </p>
      </div>
    </div>
  );
};
