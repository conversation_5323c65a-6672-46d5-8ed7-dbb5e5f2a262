"use client";
import React, { useState } from "react";
import { But<PERSON> } from "../ui/button";
import { XIcon } from "lucide-react";

// If shadcn/ui Alert is available, use it. Otherwise, fallback to a styled div.
// import { <PERSON><PERSON>, AlertTitle, AlertDescription } from "@/components/ui/alert";

const MobileWarningBanner: React.FC = () => {
  const [isMobileWarningBanner, setIsMobileWarningBanner] = useState(true);
  if (!isMobileWarningBanner) return null;
  return (
    <div className="flex z-100 justify-evenly items-center  lg:hidden w-full bg-warning text-white text-center py-2 px-4 font-satoshi-bold text-sm fixed top-0 left-0 right-0 shadow-sm">
      <div className="flex-1">
        Mobile and Tablet support is coming soon! The Ruh App is currently best
        experienced on desktop.
      </div>
      <Button
        variant="linkSecondary"
        onClick={() => {
          setIsMobileWarningBanner(false);
        }}
        className="text-white"
      >
        <XIcon />
      </Button>
    </div>
  );
};

export default MobileWarningBanner;
