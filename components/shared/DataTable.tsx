"use client";

import * as React from "react";
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  isLoading?: boolean;
  loadingText?: string;
  emptyText?: string;
  emptyIcon?: React.ReactNode;
  emptyAction?: React.ReactNode;
  expandedRows?: Set<string>;
  renderExpandedRow?: (row: TData) => React.ReactNode;
  getRowId?: (row: TData) => string;
}

export function DataTable<TData, TValue>({
  columns,
  data,
  isLoading = false,
  loadingText = "Loading...",
  emptyText = "No data found",
  emptyIcon,
  emptyAction,
  expandedRows = new Set<string>(),
  renderExpandedRow,
  getRowId = (row: any) => row.id,
}: DataTableProps<TData, TValue>) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );
  const [columnVisibility, setColumnVisibility] =
    React.useState<VisibilityState>({});

  const table = useReactTable({
    data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onSortingChange: setSorting,
    getSortedRowModel: getSortedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
    },
  });

  if (isLoading) {
    return (
      <div className="relative overflow-hidden border-b border-border sm:rounded-lg min-h-[300px] flex justify-center items-center">
        <div className="py-10 text-center">
          <div className="flex flex-col items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary mb-2"></div>
            <div className="text-sm text-muted-foreground">{loadingText}</div>
          </div>
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="relative overflow-hidden border-b border-border sm:rounded-lg min-h-[300px]">
        <div className="py-12 text-center">
          <div className="flex flex-col items-center justify-center space-y-4">
            {emptyIcon}
            <div className="space-y-1">
              <h3 className="text-lg sm:text-xl font-bold text-brand-primary-font">
                {emptyText}
              </h3>
            </div>
            {emptyAction}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative overflow-hidden border-b border-border sm:rounded-lg">
      <div
        className="overflow-x-auto scrollbar-hide"
        style={{ WebkitOverflowScrolling: "touch" }}
      >
        <Table className="min-w-[1000px] divide-y divide-border bg-card text-foreground">
          <TableHeader className="bg-muted text-muted-foreground">
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    className="px-4 py-3 text-left text-xs font-medium uppercase tracking-wider"
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="bg-card divide-y divide-border">
            {table.getRowModel().rows.map((row) => {
              const rowId = getRowId(row.original);
              const isExpanded = expandedRows.has(rowId);

              return (
                <React.Fragment key={row.id}>
                  <TableRow
                    className="hover:bg-muted/50"
                    data-state={row.getIsSelected() && "selected"}
                  >
                    {row.getVisibleCells().map((cell) => (
                      <TableCell
                        key={cell.id}
                        className="px-4 py-4 whitespace-nowrap"
                      >
                        {flexRender(
                          cell.column.columnDef.cell,
                          cell.getContext()
                        )}
                      </TableCell>
                    ))}
                  </TableRow>

                  {/* Render expanded content if row is expanded */}
                  {isExpanded && renderExpandedRow && (
                    <TableRow className="bg-transparent border-0">
                      <TableCell
                        colSpan={columns.length}
                        className="p-0 border-0"
                      >
                        {renderExpandedRow(row.original)}
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              );
            })}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
