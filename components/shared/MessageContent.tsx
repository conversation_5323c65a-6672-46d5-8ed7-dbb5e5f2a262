"use client";

import React, { useMemo, useState } from "react";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeHighlight from "rehype-highlight";
import rehypeRaw from "rehype-raw";
import { FILTERED_MESSAGE_KEYWORDS } from "@/shared/constants";
import "highlight.js/styles/github-dark.css";

interface MessageContentProps {
  content: string;
  showCopyAll?: boolean;
  isUser?: boolean;
}

interface CopyButtonProps {
  text: string;
  className?: string;
  size?: "sm" | "default";
}

interface CodeEditorModalProps {
  isOpen: boolean;
  onClose: () => void;
  initialCode: string;
  language: string;
  onSave?: (code: string) => void;
}

const MessageContent = React.memo(
  ({ content, showCopyAll = true, isUser = false }: MessageContentProps) => {
    // Remove filtered keywords from content
    const filteredContent = useMemo(() => {
      let result = content;
      FILTERED_MESSAGE_KEYWORDS.forEach((keyword) => {
        // Remove all occurrences of the keyword (as a whole word or line)
        const regex = new RegExp(keyword + "(\\n)?", "g");
        result = result.replace(regex, "");
      });
      return result.trim();
    }, [content]);

    // Custom components for react-markdown
    const components = useMemo(
      () => ({
        pre: ({ children, ...props }: any) => {
          return (
            <pre {...props} className="max-w-[90vw] overflow-x-auto">
              {children}
            </pre>
          );
        },

        code: ({ children, className, ...props }: any) => {
          return (
            <code
              {...props}
              className={`${className} !text-white font-satoshi-bold text-sm`}
            >
              {children}
            </code>
          );
        },

        // Custom blockquote styling
        blockquote: ({ children, ...props }: any) => (
          <blockquote
            {...props}
            className="border-l-4 border-primary/30 pl-4 py-2 my-4 bg-muted/20 rounded-r-lg italic"
          >
            {children}
          </blockquote>
        ),

        // Custom table styling
        table: ({ children, ...props }: any) => (
          <div className="overflow-x-auto my-4 w-full">
            <table
              {...props}
              className="min-w-full border-collapse text-sm"
              style={{
                tableLayout: "auto",
                width: "100%",
              }}
            >
              {children}
            </table>
          </div>
        ),

        thead: ({ children, ...props }: any) => (
          <thead {...props} className="bg-muted/50">
            {children}
          </thead>
        ),

        th: ({ children, ...props }: any) => (
          <th
            {...props}
            className="border border-border px-4 py-2 text-left font-semibold break-words whitespace-normal bg-muted/50 align-top"
            style={{
              wordBreak: "break-word",
              minWidth: "120px",
              overflowWrap: "break-word",
              whiteSpace: "normal",
            }}
          >
            {children}
          </th>
        ),

        td: ({ children, ...props }: any) => (
          <td
            {...props}
            className="border border-border px-4 py-2 break-words whitespace-normal align-top"
            style={{
              wordBreak: "break-word",
              minWidth: "120px",
              overflowWrap: "break-word",
              whiteSpace: "normal",
            }}
          >
            {children}
          </td>
        ),

        // Custom link styling
        a: ({ children, href, ...props }: any) => {
          let domain = "";
          try {
            domain = href ? new URL(href).hostname : "";
          } catch (e) {
            domain = "";
          }
          return (
            <span className="block mb-2">
              <a
                className="text-primary hover:text-primary/80 underline underline-offset-2 inline items-center gap-1 break-all hyphens-auto"
                {...props}
                href={href}
                target="_blank"
                rel="noopener noreferrer"
                style={{ wordBreak: "break-all", hyphens: "auto" }}
              >
                {domain && (
                  <img
                    src={`https://www.google.com/s2/favicons?domain=${domain}&sz=24`}
                    alt="favicon"
                    className="inline-block w-4 h-4 mr-1 align-middle rounded !my-0"
                    style={{ minWidth: 24, minHeight: 24 }}
                    loading="lazy"
                  />
                )}
                {children}
              </a>
            </span>
          );
        },

        // Custom heading styling
        h1: ({ children, ...props }: any) => (
          <h1 {...props} className="text-2xl font-bold mt-6 mb-4 first:mt-0">
            {children}
          </h1>
        ),

        h2: ({ children, ...props }: any) => (
          <h2 {...props} className="text-xl font-semibold mt-5 mb-3 first:mt-0">
            {children}
          </h2>
        ),

        h3: ({ children, ...props }: any) => (
          <h3 {...props} className="text-lg font-semibold mt-4 mb-2 first:mt-0">
            {children}
          </h3>
        ),

        h4: ({ children, ...props }: any) => (
          <h4
            {...props}
            className="text-base font-semibold mt-3 mb-2 first:mt-0"
          >
            {children}
          </h4>
        ),

        h5: ({ children, ...props }: any) => (
          <h5 {...props} className="text-sm font-semibold mt-3 mb-2 first:mt-0">
            {children}
          </h5>
        ),

        h6: ({ children, ...props }: any) => (
          <h6 {...props} className="text-xs font-semibold mt-3 mb-2 first:mt-0">
            {children}
          </h6>
        ),

        // Custom list styling
        // ul: ({ children, ...props }: any) => (
        //   <ul {...props} className="list-disc list-inside my-4 space-y-1">
        //     {children}
        //   </ul>
        // ),

        // ol: ({ children, ...props }: any) => (
        //   <ol {...props} className="list-decimal list-inside my-4 space-y-1">
        //     {children}
        //   </ol>
        // ),

        // li: ({ children, ...props }: any) => (
        //   <li {...props} className="leading-relaxed">
        //     {children}
        //   </li>
        // ),

        // Custom paragraph styling
        p: ({ children, ...props }: any) => (
          <p {...props} className="leading-relaxed my-3 first:mt-0 last:mb-0">
            {children}
          </p>
        ),

        // Custom horizontal rule
        hr: ({ ...props }: any) => (
          <hr {...props} className="my-6 border-border" />
        ),

        // Custom strong/bold styling
        strong: ({ children, ...props }: any) => (
          <strong {...props} className="font-semibold">
            {children}
          </strong>
        ),

        // Custom emphasis/italic styling
        em: ({ children, ...props }: any) => (
          <em {...props} className="italic">
            {children}
          </em>
        ),
      }),
      []
    );

    // Memoize the markdown processing
    const processedContent = useMemo(() => {
      return (
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeHighlight, rehypeRaw]}
          components={components}
        >
          {filteredContent}
        </ReactMarkdown>
      );
    }, [filteredContent, components]);

    return (
      <div className="group relative min-w-0">
        <div className="markdown-content break-words prose prose-sm max-w-none dark:prose-invert min-w-0">
          {processedContent}
        </div>
      </div>
    );
  }
);

MessageContent.displayName = "MessageContent";

export default MessageContent;
