import React, { useState, useEffect } from "react";

interface UniversalTableColumn<T> {
  key: string;
  label: string;
  render?: (row: T, rowIndex: number) => React.ReactNode;
}

interface UniversalTableProps<T> {
  data: T[];
  columns: UniversalTableColumn<T>[];
  pageSize?: number;
  totalRecords?: number;
  isPagination?: boolean;
  onPageChange?: (newPage: number) => void;
  totalPages?: number;
  loader?: boolean;
  emptyState?: { logo?: React.ReactNode; title?: string; subtitle?: string };
  rowClickable?: boolean;
  onRowClick?: (row: T) => void;
  className?: string;
  isLoading?: boolean;
}

function CustomTable<T>({
  data,
  columns,
  pageSize = 10,
  totalRecords = 0,
  isPagination = false,
  onPageChange,
  totalPages = 1,
  loader = false,
  isLoading = false,
  emptyState = {
    logo: null,
    title: "No data found",
    subtitle: "It looks like you have not done any action yet.",
  },
  rowClickable = false,
  onRowClick,
  className,
}: UniversalTableProps<T>) {
  const [currentPage, setCurrentPage] = useState(0);

  useEffect(() => {
    if (onPageChange) {
      onPageChange(currentPage);
    }
  }, [currentPage, onPageChange]);

  const goToPreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 0));
  };
  const goToNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages - 1));
  };

  // Skeleton loader rows
  const skeletonRows = Array.from({ length: pageSize }).map((_, idx) => (
    <tr key={idx} className="animate-pulse border-b last:border-b-0">
      {columns.map((col, colIdx) => (
        <td key={col.key || colIdx} className="px-4 py-2">
          <div className="h-4 bg-gray-200 rounded w-3/4" />
        </td>
      ))}
    </tr>
  ));

  return (
    <div className={className}>
      <div className="overflow-x-auto rounded-lg border border-gray-200 bg-white  lg:w-full">
        <table className="min-w-full">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((col) => (
                <th
                  key={col.key}
                  className="px-4 py-3 text-left text-sm font-satoshi-bold border-b"
                >
                  {col.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              skeletonRows
            ) : data.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length}
                  className="px-4 py-10 text-center text-gray-400"
                >
                  {emptyState.logo}
                  <div className="mt-2 text-base font-semibold">
                    {emptyState.title}
                  </div>
                  <div className="text-sm text-gray-400">
                    {emptyState.subtitle}
                  </div>
                </td>
              </tr>
            ) : (
              data.map((row, rowIndex) => (
                <tr
                  key={rowIndex}
                  className={`border-b last:border-b-0 hover:bg-gray-50 ${rowClickable ? "cursor-pointer" : ""}`}
                  onClick={
                    rowClickable && onRowClick
                      ? () => onRowClick(row)
                      : undefined
                  }
                >
                  {columns.map((col, colIdx) => (
                    <td key={col.key || colIdx} className="p-4 text-sm">
                      {col.render
                        ? col.render(row, rowIndex)
                        : (row as any)[col.key]}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      {/* Pagination */}
      {isPagination && data.length > 0 && (
        <div className="flex justify-between items-center gap-2 mt-4 px-2">
          <p className="text-sm text-gray-600">
            Showing {Math.min(currentPage * pageSize + 1, totalRecords)} to{" "}
            {Math.min((currentPage + 1) * pageSize, totalRecords)} of{" "}
            {totalRecords} entries
          </p>
          <div className="flex items-center gap-2">
            <button
              className="px-2 py-1 rounded border text-sm disabled:opacity-50"
              onClick={goToPreviousPage}
              disabled={currentPage === 0}
            >
              Prev
            </button>
            <div className="page px-3 py-1 rounded bg-gray-100 font-semibold text-sm">
              {currentPage + 1}
            </div>
            <button
              className="px-2 py-1 rounded border text-sm disabled:opacity-50"
              onClick={goToNextPage}
              disabled={currentPage + 1 === totalPages || totalPages === 0}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default CustomTable;
