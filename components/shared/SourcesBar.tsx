// File: components/shared/SourcesBar.tsx
// Component: SourcesBar - Displays search sources with responsive design

import React from "react";
import { BuildingIcon, Globe, Lightbulb } from "lucide-react";
import { StreamMessageSource } from "@/shared/enums";
import { <PERSON><PERSON> } from "../ui/button";
import BookOutlineIcon from "@/public/assets/Icons-components/BookOutlineIcon";
import { Separator } from "../ui/separator";
// You can import your custom BuildingIcon if available
// import BuildingIcon from "@/public/assets/Icons-components/BuildingIcon";

function getDomain(url: string) {
  try {
    return new URL(url).hostname.replace(/^www\./, "");
  } catch {
    return url;
  }
}

interface SourcesBarProps {
  employeeSearchType?: string;
  searchSources?: any[];
}

const SourcesBar: React.FC<SourcesBarProps> = ({
  employeeSearchType,
  searchSources = [],
}) => {
  if (!employeeSearchType || !searchSources.length) return null;

  return (
    <div className="w-full max-w-full">
      <div className="flex flex-col gap-3 w-full">
        <div className="font-satoshi-regular text-xs text-text-secondary">
          {searchSources.length} Results
        </div>

        <div className="flex items-center  gap-2">
          <BookOutlineIcon width={24} height={24} />
          <span className="font-satoshi-bold text-base text-text-primary">
            Sources
          </span>
        </div>

        {employeeSearchType === StreamMessageSource.WEB_SEARCH_EMPLOYEE ? (
          <Button
            variant="tertiary"
            className="bg-transparent border border-border-light text-text-primary font-satoshi-regular w-fit"
          >
            <Globe className="w-4 h-4 mr-1" />
            Global
          </Button>
        ) : (
          <Button
            variant="tertiary"
            className="bg-transparent border border-border-light text-text-primary font-satoshi-regular w-fit"
          >
            <BuildingIcon className="w-4 h-4 mr-1" />
            Company
          </Button>
        )}
      </div>

      <Separator className="my-3" />

      <div className="flex flex-wrap gap-2 w-full">
        {searchSources.map((source, idx) => {
          const domain = getDomain(source.url);
          return (
            <Button
              variant="tertiary"
              key={source.name + idx}
              className="flex-shrink-0 gap-2 bg-transparent border border-border-light text-text-primary font-satoshi-regular py-2 px-3 h-auto max-w-full"
              onClick={() => {
                window.open(source.url, "_blank");
              }}
            >
              <img
                src={`https://www.google.com/s2/favicons?domain=${domain}&sz=24`}
                alt="favicon"
                className="inline-block w-4 h-4 mr-1 align-middle rounded flex-shrink-0"
                style={{ minWidth: 16, minHeight: 16 }}
                loading="lazy"
              />
              <span className="break-words whitespace-normal text-left leading-normal">
                {source.name}
              </span>
            </Button>
          );
        })}
      </div>

      <div className="flex items-center justify-center gap-2 mt-6 w-fit">
        <Lightbulb width={24} height={24} />
        <span className="font-satoshi-bold text-base text-text-primary">
          Answer
        </span>
      </div>
    </div>
  );
};

export default SourcesBar;
