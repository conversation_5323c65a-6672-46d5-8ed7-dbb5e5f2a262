import React from "react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface CustomTooltipProps {
  title?: React.ReactNode;
  description?: React.ReactNode;
  children: React.ReactNode;
  side?: "top" | "right" | "bottom" | "left";
  align?: "start" | "center" | "end";
}

export default function CustomTooltip({
  title,
  description,
  children,
  side = "top",
  align = "center",
}: CustomTooltipProps) {
  return (
    <Tooltip>
      <TooltipTrigger asChild>{children}</TooltipTrigger>
      <TooltipContent side={side} align={align}>
        {title ? (
          <div>
            <div className="font-satoshi-bold text-white text-sm mb-1">
              {title}
            </div>
            {description && (
              <div className="text-xs text-white">{description}</div>
            )}
          </div>
        ) : (
          description && <div className="text-xs text-white">{description}</div>
        )}
      </TooltipContent>
    </Tooltip>
  );
}
