"use client";

import { cn } from "@/lib/utils";
import { useUserStore } from "@/hooks/use-user";
import { useState } from "react";
import Image from "next/image";
import { userSettingsRoute } from "@/shared/routes";
import { useRouter } from "next/navigation";
import { useNotificationStore } from "@/hooks/use-notification";
import { useMixpanelTrack } from "@/hooks/useMixPanelTrack";
import { AnalyticsEvents } from "@/shared/enums";

interface UserAvatarProps {
  className?: string;
  fallbackClassName?: string;
}

export const UserAvatar = ({
  className,
  fallbackClassName,
}: UserAvatarProps) => {
  const { user } = useUserStore();
  const [imageError, setImageError] = useState(false);
  const router = useRouter();
  const { closeNotification } = useNotificationStore();
  const track = useMixpanelTrack();

  // Generate initials from user's fullName
  const getInitials = (fullName?: string | null): string => {
    if (!fullName) return "U";

    const names = fullName.trim().split(" ");
    if (names.length === 1) return names[0].charAt(0).toUpperCase();

    return (
      names[0].charAt(0) + names[names.length - 1].charAt(0)
    ).toUpperCase();
  };

  // Handle edge case where user store might be deleted from storage
  const initials = user?.fullName ? getInitials(user.fullName) : "U";

  const handleClick = () => {
    const date = new Date();
    track(AnalyticsEvents.PROFILE_SETTING_CLICKED, {
      location: window.location.href,
      timestamp: date.toString(),
    });
    closeNotification();
    router.push(userSettingsRoute);
  };

  const renderAvatar = () => {
    if (user?.profileImage && !imageError) {
      return (
        <div
          className={cn(
            "relative flex cursor-pointer items-center justify-center overflow-hidden rounded-sm",
            className
          )}
          onClick={handleClick}
        >
          <Image
            src={user.profileImage}
            alt={user.fullName || "User"}
            fill
            className="object-cover"
            onError={() => setImageError(true)}
          />
        </div>
      );
    }

    return (
      <div
        className={cn(
          "relative flex cursor-pointer items-center justify-center overflow-hidden rounded-sm",
          className
        )}
        onClick={handleClick}
      >
        <div
          className={cn(
            "flex h-full w-full items-center justify-center bg-brand-primary text-white",
            fallbackClassName
          )}
        >
          <span className="text-sm font-medium">{initials}</span>
        </div>
      </div>
    );
  };

  return renderAvatar();
};
