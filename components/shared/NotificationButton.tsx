"use client";

import { BellIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { useNotificationStore } from "@/hooks/use-notification";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface NotificationButtonProps {
  className?: string;
}

export const NotificationButton = ({ className }: NotificationButtonProps) => {
  const { isNotificationOpen, toggleNotification, hasUnread } =
    useNotificationStore();

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <button
          onClick={toggleNotification}
          className={cn(
            "relative flex items-center justify-center w-9 h-9 rounded-sm transition-all duration-100 ease-in",
            isNotificationOpen
              ? "bg-brand-clicked text-brand-primary"
              : "hover:bg-brand-card-hover hover:text-brand-primary",
            className
          )}
          aria-label="Notifications"
        >
          <BellIcon size={24} strokeWidth={1.2} />
          {hasUnread && (
            <span
              className="absolute -top-0.5 -right-0.5 inline-flex h-2.5 w-2.5 items-center justify-center rounded-full bg-red-500"
              aria-label="Unread notifications"
            />
          )}
        </button>
      </TooltipTrigger>
      <TooltipContent side="right">Notifications</TooltipContent>
    </Tooltip>
  );
};
