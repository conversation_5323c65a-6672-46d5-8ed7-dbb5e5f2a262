"use client";

import { useState, useRef, useEffect } from "react";
import {
  Play,
  Volume2,
  VolumeX,
  Maximize,
  Download,
  Pause,
} from "lucide-react";
import { Button } from "@/components/ui/button";

export default function CustomVideoPlayer({ url }: { url: string }) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showControls, setShowControls] = useState(true);

  useEffect(() => {
    const video = videoRef.current;
    if (!video) return;

    const updateTime = () => setCurrentTime(video.currentTime);
    const updateDuration = () => setDuration(video.duration);

    video.addEventListener("timeupdate", updateTime);
    video.addEventListener("loadedmetadata", updateDuration);
    video.addEventListener("ended", () => setIsPlaying(false));

    return () => {
      video.removeEventListener("timeupdate", updateTime);
      video.removeEventListener("loadedmetadata", updateDuration);
      video.removeEventListener("ended", () => setIsPlaying(false));
    };
  }, []);

  const togglePlay = () => {
    const video = videoRef.current;
    if (!video) return;

    if (isPlaying) {
      video.pause();
    } else {
      video.play();
    }
    setIsPlaying(!isPlaying);
  };

  const toggleMute = () => {
    const video = videoRef.current;
    if (!video) return;

    video.muted = !isMuted;
    setIsMuted(!isMuted);
  };

  const toggleFullscreen = () => {
    const video = videoRef.current;
    if (!video) return;

    if (document.fullscreenElement) {
      document.exitFullscreen();
    } else {
      video.requestFullscreen();
    }
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const handleDownload = () => {
    const link = document.createElement("a");
    link.href = url;
    link.download = "video.mp4";
    link.click();
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: "Artist Video",
        url: window.location.href,
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto  rounded-2xl">
      <div className="space-y-4">
        {/* Video Player Container */}
        <div
          className="relative rounded-2xl overflow-hidden bg-black border-4 border-black shadow-2xl shadow-blue-400/20"
          onMouseEnter={() => setShowControls(true)}
          onMouseLeave={() => setShowControls(true)}
        >
          <video
            ref={videoRef}
            className="w-full aspect-video object-contain"
            src={url}
            onClick={togglePlay}
          />

          {/* Video Controls Overlay */}
          <div
            className={`absolute inset-0 transition-opacity duration-300 ${
              showControls ? "opacity-100" : "opacity-0"
            }`}
          >
            {/* Top Right - Fullscreen Button */}
            <div className="absolute top-4 right-4">
              <Button
                variant="ghost"
                size="icon"
                onClick={toggleFullscreen}
                className="bg-transparent hover:text-white hover:bg-transparent text-white font-bold"
              >
                <Maximize className="h-5 w-5" />
              </Button>
            </div>

            {/* Bottom Controls */}
            <div className="absolute bottom-0 left-0 right-0 p-6">
              <div className="flex items-center justify-between">
                {/* Left - Play/Pause Button */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="bg-transparent hover:text-white hover:bg-transparent text-white font-bold"
                  onClick={togglePlay}
                >
                  {isPlaying ? (
                    <Pause className="h-10 w-10 " />
                  ) : (
                    <Play className="h-10 w-10" />
                  )}
                </Button>

                {/* Right - Volume and Time */}
                <div className="flex items-center gap-4">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={toggleMute}
                    className="bg-transparent hover:text-white hover:bg-transparent text-white font-bold"
                  >
                    {isMuted ? (
                      <VolumeX className="h-5 w-5" />
                    ) : (
                      <Volume2 className="h-5 w-5" />
                    )}
                  </Button>
                  <span className="text-white text-lg font-medium">
                    {formatTime(currentTime)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-6">
          <Button variant="ghost" onClick={handleDownload}>
            <Download className="h-5 w-5 mr-2" />
            Download
          </Button>
        </div>
      </div>
    </div>
  );
}
