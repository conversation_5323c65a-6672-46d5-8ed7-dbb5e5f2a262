import React, { useEffect, useRef, useState } from "react";
import { FileText, X } from "lucide-react";
import { GlobalWorkerOptions, getDocument } from "pdfjs-dist";
import { EmployeeChatAttachment } from "@/hooks/useEmployeeManagementStore";
GlobalWorkerOptions.workerSrc = "/pdf.worker.min.mjs";

interface PreviewFileProps {
  previewFile: EmployeeChatAttachment | null;
  onClose: () => void;
  className?: string;
}

const PreviewFile: React.FC<PreviewFileProps> = ({
  previewFile,
  onClose,
  className,
}) => {
  const [pdfError, setPdfError] = useState<string | null>(null);
  const [pdfPages, setPdfPages] = useState<Array<HTMLCanvasElement | null>>([]);
  const pdfContainerRef = useRef<HTMLDivElement>(null);

  // Reset pages when opening a new PDF
  useEffect(() => {
    if (previewFile && previewFile.file_type === "application/pdf") {
      setPdfPages([]);
      setPdfError(null);
    }
  }, [previewFile]);

  // Render all PDF pages at a fixed scale
  useEffect(() => {
    if (
      previewFile &&
      previewFile.file_type === "application/pdf" &&
      pdfContainerRef.current
    ) {
      setPdfError(null);
      const renderAllPages = async () => {
        try {
          const loadingTask = getDocument(previewFile.file_url);
          const pdf = await loadingTask.promise;
          const canvases: Array<HTMLCanvasElement> = [];
          for (let i = 1; i <= pdf.numPages; i++) {
            const page = await pdf.getPage(i);
            const viewport = page.getViewport({ scale: 2 });
            const canvas = document.createElement("canvas");
            const context = canvas.getContext("2d")!;
            canvas.width = viewport.width;
            canvas.height = viewport.height;
            await page.render({ canvasContext: context, viewport }).promise;
            canvases.push(canvas);
          }
          setPdfPages(canvases);
        } catch (err: any) {
          setPdfError(err?.message || "Failed to load PDF.");
        }
      };
      renderAllPages();
    }
  }, [previewFile]);

  if (!previewFile) return null;

  return (
    <div
      className={
        "fixed inset-0 z-[1000] flex items-center justify-center bg-black/40 backdrop-blur-[6px] transition-all animate-in fade-in-0 " +
        (className || "")
      }
      style={{ backdropFilter: "blur(12px)" }}
    >
      <button
        className="absolute top-[60px] right-[10px] sm:top-6 sm:right-8 text-white bg-black/60 rounded-full p-2 hover:bg-black/80 z-[1001]"
        onClick={onClose}
        aria-label="Close preview"
      >
        <X className="w-7 h-7" />
      </button>
      <div className="relative bg-white/90 rounded-xl shadow-xl max-w-3xl w-full max-h-[90vh] flex flex-col items-center justify-center p-6 overflow-auto">
        {/* PDF Preview */}
        {previewFile?.file_type === "application/pdf" ? (
          <div
            ref={pdfContainerRef}
            style={{
              maxHeight: "70vh",
              overflow: "auto",
              background: "white",
              width: "100%",
            }}
            className="w-full flex flex-col items-center"
          >
            {/* Download Button Only */}
            <div
              className="sticky top-0 z-10 bg-white/95 flex items-center gap-2 py-2 px-4 w-full border-b border-gray-200 mb-2"
              style={{ justifyContent: "flex-end" }}
            >
              <a
                href={previewFile.file_url}
                download={previewFile.file_name}
                className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition"
                target="_blank"
                rel="noopener noreferrer"
              >
                Download
              </a>
            </div>
            {pdfError ? (
              <div className="text-red-500 text-center">{pdfError}</div>
            ) : pdfPages.length > 0 ? (
              pdfPages.map((canvas, idx) => (
                <div key={idx} className="mb-4 flex justify-center">
                  <img
                    src={canvas?.toDataURL()}
                    alt={`PDF page ${idx + 1}`}
                    style={{
                      background: "white",
                      boxShadow: "0 2px 8px rgba(0,0,0,0.08)",
                      borderRadius: 8,
                    }}
                  />
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500">Loading PDF...</div>
            )}
          </div>
        ) : previewFile?.file_type?.startsWith("image/") ? (
          <img
            src={previewFile.file_url}
            alt={previewFile.file_name}
            className="max-w-full max-h-[70vh] rounded shadow"
          />
        ) : (
          <div className="flex flex-col items-center justify-center w-full h-[400px]">
            <FileText className="h-12 w-12 text-primary mb-2" />
            <span className="text-sm">
              Preview not available.{" "}
              <a
                href={previewFile?.file_url}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 underline"
              >
                Download
              </a>
            </span>
          </div>
        )}
        <div className="mt-2 text-center text-xs text-muted-foreground">
          {previewFile.file_name}
        </div>
      </div>
    </div>
  );
};

export default PreviewFile;
