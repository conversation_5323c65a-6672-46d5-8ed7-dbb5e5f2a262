import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils"; // If you have a classnames util
import Image from "next/image";

// Types
export type ChatFilter = "all" | "task" | "search";

export interface RecentChatsSidebarProps {
  loading?: boolean;
  onNewChat: () => void;
  onSearch: (query: string) => void;
  onRefresh: () => void;
  filter: ChatFilter;
  onFilterChange: (filter: ChatFilter) => void;
  // chats?: ChatSummary[]; // For future extension
}

const FILTERS: { label: string; value: ChatFilter }[] = [
  { label: "All", value: "all" },
  { label: "Task", value: "task" },
  { label: "Search", value: "search" },
];

export default function RecentChatsSidebar({
  loading,
  onNewChat,
  onSearch,
  onRefresh,
  filter,
  onFilterChange,
}: RecentChatsSidebarProps) {
  const [search, setSearch] = React.useState("");

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
    onSearch(e.target.value);
  };

  return (
    <aside className="w-full max-w-xs h-full bg-white flex flex-col px-4 py-6 border-r border-[--border-default]">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold">Recent Chats</h2>
        <Button
          variant="secondary"
          className="bg-gradient-to-r from-[#ae00d0] to-[#7b5aff] text-white font-semibold px-4 py-2 rounded-lg flex items-center gap-2 shadow"
          onClick={onNewChat}
        >
          <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
            <rect
              x="3"
              y="3"
              width="14"
              height="14"
              rx="3"
              stroke="white"
              strokeWidth="1.5"
            />
            <path
              d="M10 7v6M7 10h6"
              stroke="white"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
          </svg>
          New Chat
        </Button>
      </div>

      {/* Search Bar */}
      <div className="relative mb-4">
        <Input
          type="text"
          placeholder="Search Chat"
          value={search}
          onChange={handleSearchChange}
          className="pl-10 pr-10 h-10 bg-[--background-muted] rounded-lg"
        />
        {/* Magnifier icon */}
        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400">
          <svg width="18" height="18" fill="none" viewBox="0 0 18 18">
            <circle
              cx="8.5"
              cy="8.5"
              r="6.5"
              stroke="#C2C2C2"
              strokeWidth="1.5"
            />
            <path
              d="M15 15L13 13"
              stroke="#C2C2C2"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
          </svg>
        </span>
        {/* Refresh/history icon */}
        <button
          type="button"
          aria-label="Refresh"
          onClick={onRefresh}
          className="absolute right-2 top-1/2 -translate-y-1/2 p-1 rounded hover:bg-[--background-accent] transition"
        >
          <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
            <path
              d="M10 3a7 7 0 1 1-6.32 4.01"
              stroke="#7b5aff"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
            <path
              d="M3 3v4h4"
              stroke="#7b5aff"
              strokeWidth="1.5"
              strokeLinecap="round"
            />
          </svg>
        </button>
      </div>

      {/* Filter Tabs */}
      <div className="flex items-center gap-2 mb-6">
        <span className="text-base font-medium">Filter:</span>
        <Tabs value={filter} onValueChange={onFilterChange as any}>
          <TabsList className="bg-[--tab-color] rounded-lg p-1 h-8">
            {FILTERS.map((f) => (
              <TabsTrigger
                key={f.value}
                value={f.value}
                className={cn(
                  "px-3 py-1 rounded-md text-sm font-medium",
                  filter === f.value
                    ? "bg-white shadow text-[--primary]"
                    : "text-black"
                )}
              >
                {f.label}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      {/* Empty State */}
      <div className="flex-1 flex flex-col items-center justify-center">
        <div className="rounded-full bg-[--background-accent] p-6 mb-4">
          {/* Clipboard SVG */}
          <svg width="48" height="48" fill="none" viewBox="0 0 48 48">
            <rect x="12" y="8" width="24" height="32" rx="4" fill="#F7E6FA" />
            <rect x="16" y="12" width="16" height="4" rx="2" fill="#AE00D0" />
            <rect x="16" y="20" width="16" height="2" rx="1" fill="#C2C2C2" />
            <rect x="16" y="26" width="16" height="2" rx="1" fill="#C2C2C2" />
            <rect x="16" y="32" width="10" height="2" rx="1" fill="#C2C2C2" />
          </svg>
        </div>
        <div className="text-center text-gray-400 text-base font-medium">
          Start fresh — no chat
          <br />
          history found
        </div>
      </div>
    </aside>
  );
}
