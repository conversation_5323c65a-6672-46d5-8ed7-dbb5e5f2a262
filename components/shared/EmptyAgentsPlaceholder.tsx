"use client";

import { emptyAgentsContainer } from "@/shared/constants";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import MdcIcon from "@/public/assets/Icons-components/MdcIcon";
import { createEmployeeRoute } from "@/shared/routes";
import { Plus } from "lucide-react";
import { Button } from "../ui/button";
import { resetEmployeeCreateState } from "@/lib/utils";
import { useMixpanelTrack } from "@/hooks/useMixPanelTrack";
import { AnalyticsEvents } from "@/shared/enums";

export const EmptyAgentsPlaceholder = () => {
  const router = useRouter();
  const track = useMixpanelTrack();

  return (
    <div className="py-14 flex flex-col gap-8 w-full justify-center items-center">
      <MdcIcon />
      <div className="flex flex-col gap-1 justify-center items-center">
        <h1>Your workforce is empty</h1>
        <p className="text-text-secondary">
          Click the button below to get started with adding / creating employees
        </p>
      </div>
      <Button
        variant="primary"
        onClick={() => {
          resetEmployeeCreateState();
          router.push(createEmployeeRoute);
          track(AnalyticsEvents.CLICKED_CREATE_EMPLOYEE);
        }}
      >
        <Plus />
        Create An Employee
      </Button>
    </div>
  );
};
