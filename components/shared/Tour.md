# Tour/Guide System Documentation

## Overview

The Tour system provides a customizable, reusable solution for creating guided tours across different modules in the application. It's designed to replace react-joyride with a React 19-compatible solution that precisely matches the Figma design specifications.

## Design Specifications

The tour component is built to match the exact Figma design with:

- **Primary Color**: `#ae00d0` (Purple background for tooltips)
- **Text Color**: `#ffffff` (White text on purple background)
- **Typography**: Satoshi font family with proper weights
- **Border Radius**: `8px` for consistent rounded corners
- **Shadow**: `0px 4px 20px rgba(0, 0, 0, 0.15)` for depth
- **Overlay**: `rgba(0, 0, 0, 0.4)` for softer background dimming
- **Progress Indicators**: White dots with opacity variations
- **Spacing**: Consistent padding and margins as per Figma specs

## Features

- ✅ **React 19 Compatible**: Built specifically for React 19 and Next.js 15
- ✅ **Figma Design Accurate**: Matches exact Figma specifications with purple theme
- ✅ **Customizable**: Fully customizable tooltips, positioning, and styling
- ✅ **Reusable**: Easy to configure for different modules
- ✅ **Responsive**: Works on different screen sizes
- ✅ **Accessible**: Keyboard navigation and screen reader support
- ✅ **Design System**: Uses Figma design tokens and Satoshi typography
- ✅ **TypeScript**: Fully typed with comprehensive interfaces

## Quick Start

### 1. Import Required Dependencies

```tsx
import { useTourContext } from "@/lib/providers/TourProvider";
import {
  createTourConfig,
  createTourStep,
  markTourCompleted,
} from "@/lib/utils/tourUtils";
```

### 2. Create Tour Configuration

```tsx
const myModuleTourConfig = createTourConfig([
  createTourStep(
    "welcome",
    ".tour-welcome",
    "Welcome to My Module",
    "This is a brief introduction to get you started.",
    { placement: "center" }
  ),
  createTourStep(
    "feature-1",
    ".tour-feature-1",
    "Feature 1",
    "This is how you use feature 1.",
    { placement: "bottom" }
  ),
  // Add more steps...
]);
```

### 3. Add Tour Classes to UI Elements

```tsx
<div className="tour-welcome">
  <h1>Welcome Screen</h1>
</div>
<div className="tour-feature-1">
  <button>Feature 1</button>
</div>
```

### 4. Implement Tour in Component

```tsx
export function MyComponent() {
  const { startTour } = useTourContext();

  const handleStartTour = () => {
    startTour(myModuleTourConfig, {
      onComplete: () => markTourCompleted("MY_MODULE"),
      onSkip: () => markTourCompleted("MY_MODULE"),
    });
  };

  return (
    <div>
      <button onClick={handleStartTour}>Start Tour</button>
      {/* Your component content */}
    </div>
  );
}
```

## API Reference

### TourStep Interface

```tsx
interface TourStep {
  id: string; // Unique identifier
  target: string; // CSS selector for target element
  title: string; // Step title
  content: string; // Step description
  placement?: "top" | "bottom" | "left" | "right" | "center";
  showSkip?: boolean; // Show skip button
  showPrevious?: boolean; // Show previous button
  showNext?: boolean; // Show next button
  showFinish?: boolean; // Show finish button (last step)
  disableBeacon?: boolean; // Disable beacon animation
  disableOverlay?: boolean; // Disable overlay for this step
  offset?: number; // Offset from target element
  spotlightPadding?: number; // Padding around spotlight
}
```

### TourConfig Interface

```tsx
interface TourConfig {
  steps: TourStep[];
  showProgress?: boolean; // Show progress indicators
  showSkipButton?: boolean; // Show skip tour button
  continuous?: boolean; // Enable continuous mode
  disableOverlay?: boolean; // Disable overlay globally
  disableScrolling?: boolean; // Disable auto-scrolling
  hideBackButton?: boolean; // Hide back button
  hideCloseButton?: boolean; // Hide close button
  scrollToFirstStep?: boolean; // Auto-scroll to first step
  spotlightClicks?: boolean; // Allow clicks on spotlight area
  spotlightPadding?: number; // Global spotlight padding
  styles?: {
    options?: {
      primaryColor?: string;
      backgroundColor?: string;
      textColor?: string;
      overlayColor?: string;
      spotlightShadow?: string;
      zIndex?: number;
    };
  };
}
```

### Utility Functions

#### `createTourConfig(steps, overrides?)`

Creates a tour configuration with default settings.

#### `createTourStep(id, target, title, content, overrides?)`

Creates a tour step with default settings.

#### `markTourCompleted(moduleKey)`

Marks a tour as completed for a specific module.

#### `isTourCompleted(moduleKey)`

Checks if a tour has been completed for a specific module.

#### `resetTourCompletion(moduleKey)`

Resets tour completion status for a specific module.

### Common Tour Steps

Pre-built step templates for common UI patterns:

```tsx
import { commonTourSteps } from "@/lib/utils/tourUtils";

const config = createTourConfig([
  commonTourSteps.welcome(".tour-welcome", "My Module"),
  commonTourSteps.navigation(".tour-nav"),
  commonTourSteps.search(".tour-search"),
  commonTourSteps.actions(".tour-actions"),
  commonTourSteps.settings(".tour-settings"),
]);
```

## Best Practices

### 1. CSS Class Naming

Use descriptive, module-specific class names:

```tsx
// Good
.tour-global-agent-welcome
.tour-employees-list
.tour-settings-profile

// Avoid
.tour-step-1
.tour-element
```

### 2. Step Ordering

Order steps logically based on user workflow:

1. Welcome/Overview
2. Primary actions
3. Secondary features
4. Settings/Configuration

### 3. Content Guidelines

- Keep titles short and descriptive
- Write content in 2nd person ("You can...")
- Limit content to 1-2 sentences
- Focus on benefits, not just features

### 4. Placement Strategy

- Use `center` for welcome/overview steps
- Use `bottom` for top navigation elements
- Use `top` for bottom actions/inputs
- Use `left`/`right` for sidebar elements

### 5. Performance

- Only load tour configurations when needed
- Use lazy loading for large step sets
- Implement tour completion tracking

## Examples

### Basic Tour

```tsx
const basicTour = createTourConfig([
  createTourStep("intro", ".tour-intro", "Welcome", "Welcome to our app!"),
  createTourStep(
    "feature",
    ".tour-feature",
    "Main Feature",
    "This is the main feature."
  ),
]);
```

### Advanced Tour with Custom Styling

```tsx
const advancedTour = createTourConfig(
  [
    createTourStep(
      "welcome",
      ".tour-welcome",
      "Welcome",
      "Getting started...",
      {
        placement: "center",
        disableOverlay: false,
        spotlightPadding: 20,
      }
    ),
  ],
  {
    showProgress: true,
    styles: {
      options: {
        primaryColor: "#custom-color",
        zIndex: 10000,
      },
    },
  }
);
```

### Tour with Callbacks

```tsx
const handleStartTour = () => {
  startTour(tourConfig, {
    onStart: () => console.log("Tour started"),
    onComplete: () => {
      markTourCompleted("MODULE_NAME");
      // Analytics tracking
      analytics.track("tour_completed", { module: "MODULE_NAME" });
    },
    onSkip: () => {
      markTourCompleted("MODULE_NAME");
      analytics.track("tour_skipped", { module: "MODULE_NAME" });
    },
    onNext: (step, index) => {
      analytics.track("tour_step_viewed", { step: step.id, index });
    },
  });
};
```

## Troubleshooting

### Common Issues

1. **Tour not showing**: Check if target elements exist in DOM
2. **Positioning issues**: Verify CSS selectors and element visibility
3. **Overlay problems**: Check z-index conflicts
4. **Performance**: Implement tour completion tracking

### Debug Mode

Enable debug logging:

```tsx
const tourConfig = createTourConfig(steps, {
  // Add debug callbacks
});
```

## Migration from react-joyride

If migrating from react-joyride:

1. Replace `react-joyride` imports with our tour system
2. Convert step configurations to our format
3. Update CSS selectors (remove `#` for IDs, use `.` for classes)
4. Replace joyride callbacks with our callback system
5. Update styling to use our design system colors

## Contributing

When adding new features:

1. Update TypeScript interfaces
2. Add utility functions for common patterns
3. Update documentation
4. Add examples
5. Test across different modules

## Support

For issues or questions:

- Check this documentation
- Review existing tour implementations
- Create GitHub issue with reproduction steps
