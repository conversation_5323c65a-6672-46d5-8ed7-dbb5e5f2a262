import React, { useState } from "react";
import { Sparkles } from "lucide-react";
import { toast } from "sonner";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { llmActionsApi } from "@/app/api/llmActions";
import { AIChatMode } from "@/shared/enums";

interface EnhancePromptButtonProps {
  /**
   * The current input text to enhance
   */
  inputText: string;
  /**
   * Callback function to update the input with enhanced text
   */
  onTextUpdate: (enhancedText: string) => void;
  /**
   * The mode to use for enhancement: 'ACT' for tasks, 'ASK' for questions
   */
  mode: AIChatMode;
  /**
   * Whether the button should be disabled
   */
  disabled?: boolean;
  /**
   * Additional CSS classes
   */
  className?: string;
  /**
   * Minimum character length required to enable the button
   */
  minLength?: number;
}

export default function EnhancePromptButton({
  inputText,
  onTextUpdate,
  mode,
  disabled = false,
  className = "",
  minLength = 10,
}: EnhancePromptButtonProps) {
  const [isImprovingPrompt, setIsImprovingPrompt] = useState(false);

  const handleImprovePrompt = async (e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }

    if (!inputText.trim() || inputText.trim().length <= minLength) {
      return;
    }

    setIsImprovingPrompt(true);

    try {
      const response = await llmActionsApi.improveUserPrompt(
        inputText.trim(),
        mode
      );

      // Update the input with the improved prompt
      onTextUpdate(response.improved_prompt);

      // Show success toast
      toast.success("Prompt enhanced successfully!");
    } catch (error) {
      console.error("Failed to improve prompt:", error);
      toast.error("Failed to improve prompt. Please try again later.");
    } finally {
      setIsImprovingPrompt(false);
    }
  };

  const isButtonDisabled =
    disabled ||
    isImprovingPrompt ||
    !inputText.trim() ||
    inputText.trim().length <= minLength;

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <span>
          <button
            onClick={(e) => handleImprovePrompt(e)}
            onMouseDown={(e) => e.preventDefault()}
            type="button"
            className={`flex items-center rounded-sm bg-gray-200/50 p-2 mt-1 disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
            disabled={isButtonDisabled}
          >
            <Sparkles
              className={`h-4 w-4 flex-shrink-0 ${
                isImprovingPrompt ? "animate-spin" : ""
              }`}
              strokeWidth={1.1}
            />
          </button>
        </span>
      </TooltipTrigger>
      <TooltipContent>
        <p>
          {isImprovingPrompt
            ? "Enhancing prompt..."
            : "Click to enhance the prompt"}
        </p>
      </TooltipContent>
    </Tooltip>
  );
}
