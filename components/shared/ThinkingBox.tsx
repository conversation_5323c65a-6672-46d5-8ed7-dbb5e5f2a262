"use client";

import React, { useEffect, useRef, useState } from "react";
import { createTextStreamer } from "@/services/helper";
import { ChevronDown, ChevronUp } from "lucide-react";
import { cn } from "@/lib/utils";

interface ThinkingBoxProps {
  content: string;
  title?: string;
  className?: string;
  speed?: number;
  interval?: number;
  width?: string;
  maxHeight?: string;
}

const ThinkingBox: React.FC<ThinkingBoxProps> = ({
  content,
  title = "Analyzing and thinking...",
  className = "",
  speed = 3,
  interval = 50,
  width = "w-full max-w-lg",
  maxHeight = "max-h-64",
}) => {
  const [streamedText, setStreamedText] = useState("");
  const [isCollapsed, setIsCollapsed] = useState(false);
  const streamerRef = useRef<ReturnType<typeof createTextStreamer> | null>(null);

  useEffect(() => {
    if (content) {
      // Stop any existing streamer
      if (streamerRef.current) {
        streamerRef.current.stop();
      }

      // Create new streamer with new content for chunk streaming
      streamerRef.current = createTextStreamer(content, speed, interval);

      // Start streaming
      streamerRef.current.start(
        (text) => setStreamedText(text),
        () => {
          // Streaming complete
        }
      );
    } else {
      // Clear streamed text when no content
      setStreamedText("");
      if (streamerRef.current) {
        streamerRef.current.stop();
      }
    }

    // Cleanup on unmount or content change
    return () => {
      if (streamerRef.current) {
        streamerRef.current.stop();
      }
    };
  }, [content, speed, interval]);

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  if (!content) return null;

  return (
    <div className={cn("bg-white border border-border-muted rounded-xl overflow-hidden transition-all duration-500 ease-out animate-in fade-in-0 slide-in-from-bottom-4", width, className)}>
      {/* Header with thinking indicator and collapse functionality */}
      <div 
        className="flex items-center gap-3 p-4 bg-background-muted border-b border-border-muted cursor-pointer hover:bg-background-muted/80 transition-colors"
        onClick={toggleCollapse}
      >
        <div className="flex items-center gap-2 flex-1">
          <div className="relative">
            <div className="w-3 h-3 bg-primary rounded-full animate-pulse"></div>
            <div className="absolute inset-0 w-3 h-3 bg-secondary rounded-full animate-ping opacity-75"></div>
          </div>
          <span className="text-sm font-satoshi-medium text-text-primary">
            {title}
          </span>
        </div>
        <div className="flex-shrink-0">
          {isCollapsed ? (
            <ChevronDown className="w-4 h-4 text-muted-foreground" />
          ) : (
            <ChevronUp className="w-4 h-4 text-muted-foreground" />
          )}
        </div>
      </div>

      {/* Content - collapsible */}
      <div className={cn(
        "transition-all duration-300 ease-in-out overflow-hidden",
        isCollapsed ? "max-h-0 opacity-0" : "max-h-none opacity-100"
      )}>
        <div className="p-6">
          <div className={cn("text-sm text-text-secondary leading-relaxed min-h-[1.5rem] overflow-y-auto", maxHeight)}>
            {streamedText}
            {streamedText !== content && (
              <span className="animate-pulse text-primary">|</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThinkingBox; 