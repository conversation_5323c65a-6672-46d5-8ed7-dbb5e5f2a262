"use client";

import React, { useEffect, useState, useCallback, useRef } from "react";
import { createPortal } from "react-dom";
import {
  TourStep,
  TourConfig,
  TourCallbacks,
  SpotlightPadding,
  TooltipOffset,
} from "@/shared/interfaces";
import { useTour } from "@/hooks/useTour";
import { X, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "../ui/button";

interface TourProps {
  config: TourConfig;
  callbacks?: TourCallbacks;
  isActive?: boolean;
  onClose?: () => void;
}

interface TooltipPosition {
  top: number;
  left: number;
  placement:
    | "top"
    | "bottom"
    | "left"
    | "right"
    | "center"
    | "top-left"
    | "top-right"
    | "top-center"
    | "bottom-left"
    | "bottom-right"
    | "bottom-center"
    | "left-top"
    | "left-bottom"
    | "left-center"
    | "right-top"
    | "right-bottom"
    | "right-center";
}

const Tour: React.FC<TourProps> = ({
  config,
  callbacks,
  isActive = false,
  onClose,
}) => {
  const [mounted, setMounted] = useState(false);
  const [tooltipPosition, setTooltipPosition] =
    useState<TooltipPosition | null>(null);
  const [targetElement, setTargetElement] = useState<Element | null>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const overlayRef = useRef<HTMLDivElement>(null);

  const tour = useTour(config.steps, callbacks);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (isActive && !tour.isActive) {
      tour.startTour();
    } else if (!isActive && tour.isActive) {
      tour.endTour();
    }
  }, [isActive, tour]);

  // Helper function to get spotlight padding values
  const getSpotlightPadding = (step: TourStep): Required<SpotlightPadding> => {
    const defaultPadding = config.spotlightPadding ?? 8;

    if (typeof step.spotlightPadding === "number") {
      return {
        top: step.spotlightPadding,
        right: step.spotlightPadding,
        bottom: step.spotlightPadding,
        left: step.spotlightPadding,
      };
    } else if (
      step.spotlightPadding &&
      typeof step.spotlightPadding === "object"
    ) {
      return {
        top: step.spotlightPadding.top ?? defaultPadding,
        right: step.spotlightPadding.right ?? defaultPadding,
        bottom: step.spotlightPadding.bottom ?? defaultPadding,
        left: step.spotlightPadding.left ?? defaultPadding,
      };
    } else {
      return {
        top: defaultPadding,
        right: defaultPadding,
        bottom: defaultPadding,
        left: defaultPadding,
      };
    }
  };

  const calculateTooltipPosition = useCallback(
    (element: Element, step: TourStep): TooltipPosition => {
      const rect = element.getBoundingClientRect();
      const windowWidth = window.innerWidth;
      const windowHeight = window.innerHeight;
      const tooltipWidth = 320; // Approximate tooltip width
      const tooltipHeight = 200; // Approximate tooltip height
      const offset = step.offset || 12;

      // Get custom tooltip offset
      const tooltipOffset: TooltipOffset = step.tooltipOffset || { x: 0, y: 0 };

      let placement = step.placement || "bottom";
      let top = 0;
      let left = 0;

      // Calculate position based on placement
      switch (placement) {
        case "top":
          top = rect.top - tooltipHeight - offset;
          left = rect.left + rect.width / 2 - tooltipWidth / 2;
          break;
        case "bottom":
          top = rect.bottom + offset;
          left = rect.left + rect.width / 2 - tooltipWidth / 2;
          break;
        case "left":
          top = rect.top + rect.height / 2 - tooltipHeight / 2;
          left = rect.left - tooltipWidth - offset;
          break;
        case "right":
          top = rect.top + rect.height / 2 - tooltipHeight / 2;
          left = rect.right + offset;
          break;
        case "center":
          top = windowHeight / 2 - tooltipHeight / 2;
          left = windowWidth / 2 - tooltipWidth / 2;
          break;
        // New compound placements
        case "top-left":
          top = rect.top - tooltipHeight - offset;
          left = rect.left;
          break;
        case "top-right":
          top = rect.top - tooltipHeight - offset;
          left = rect.right - tooltipWidth;
          break;
        case "top-center":
          top = rect.top - tooltipHeight - offset;
          left = rect.left + rect.width / 2 - tooltipWidth / 2;
          break;
        case "bottom-left":
          top = rect.bottom + offset;
          left = rect.left;
          break;
        case "bottom-right":
          top = rect.bottom + offset;
          left = rect.right - tooltipWidth;
          break;
        case "bottom-center":
          top = rect.bottom + offset;
          left = rect.left + rect.width / 2 - tooltipWidth / 2;
          break;
        case "left-top":
          top = rect.top;
          left = rect.left - tooltipWidth - offset;
          break;
        case "left-bottom":
          top = rect.bottom - tooltipHeight;
          left = rect.left - tooltipWidth - offset;
          break;
        case "left-center":
          top = rect.top + rect.height / 2 - tooltipHeight / 2;
          left = rect.left - tooltipWidth - offset;
          break;
        case "right-top":
          top = rect.top;
          left = rect.right + offset;
          break;
        case "right-bottom":
          top = rect.bottom - tooltipHeight;
          left = rect.right + offset;
          break;
        case "right-center":
          top = rect.top + rect.height / 2 - tooltipHeight / 2;
          left = rect.right + offset;
          break;
      }

      // Apply custom tooltip offset
      top += tooltipOffset.y || 0;
      left += tooltipOffset.x || 0;

      // Adjust if tooltip goes off-screen with better fallback logic
      const originalPlacement = placement;

      // Helper function to get fallback placement
      const getFallbackPlacement = (original: typeof placement) => {
        // For compound placements, try to maintain the primary direction
        if (original.includes("-")) {
          const [primary, secondary] = original.split("-");

          // Check if we need to flip the primary direction
          if (primary === "right" && left + tooltipWidth > windowWidth - 10) {
            return `left-${secondary}` as typeof placement;
          } else if (primary === "left" && left < 10) {
            return `right-${secondary}` as typeof placement;
          } else if (primary === "top" && top < 10) {
            return `bottom-${secondary}` as typeof placement;
          } else if (
            primary === "bottom" &&
            top + tooltipHeight > windowHeight - 10
          ) {
            return `top-${secondary}` as typeof placement;
          }
        } else {
          // Simple placements fallback logic
          if (original === "right" && left + tooltipWidth > windowWidth - 10) {
            return "left";
          } else if (original === "left" && left < 10) {
            return "right";
          } else if (original === "top" && top < 10) {
            return "bottom";
          } else if (
            original === "bottom" &&
            top + tooltipHeight > windowHeight - 10
          ) {
            return "top";
          }
        }
        return original;
      };

      // Apply fallback if needed
      const fallbackPlacement = getFallbackPlacement(originalPlacement);
      if (fallbackPlacement !== originalPlacement) {
        placement = fallbackPlacement;

        // Recalculate position with fallback placement
        switch (placement) {
          case "top":
            top = rect.top - tooltipHeight - offset;
            left = rect.left + rect.width / 2 - tooltipWidth / 2;
            break;
          case "bottom":
            top = rect.bottom + offset;
            left = rect.left + rect.width / 2 - tooltipWidth / 2;
            break;
          case "left":
            top = rect.top + rect.height / 2 - tooltipHeight / 2;
            left = rect.left - tooltipWidth - offset;
            break;
          case "right":
            top = rect.top + rect.height / 2 - tooltipHeight / 2;
            left = rect.right + offset;
            break;
          case "top-left":
            top = rect.top - tooltipHeight - offset;
            left = rect.left;
            break;
          case "top-right":
            top = rect.top - tooltipHeight - offset;
            left = rect.right - tooltipWidth;
            break;
          case "top-center":
            top = rect.top - tooltipHeight - offset;
            left = rect.left + rect.width / 2 - tooltipWidth / 2;
            break;
          case "bottom-left":
            top = rect.bottom + offset;
            left = rect.left;
            break;
          case "bottom-right":
            top = rect.bottom + offset;
            left = rect.right - tooltipWidth;
            break;
          case "bottom-center":
            top = rect.bottom + offset;
            left = rect.left + rect.width / 2 - tooltipWidth / 2;
            break;
          case "left-top":
            top = rect.top;
            left = rect.left - tooltipWidth - offset;
            break;
          case "left-bottom":
            top = rect.bottom - tooltipHeight;
            left = rect.left - tooltipWidth - offset;
            break;
          case "left-center":
            top = rect.top + rect.height / 2 - tooltipHeight / 2;
            left = rect.left - tooltipWidth - offset;
            break;
          case "right-top":
            top = rect.top;
            left = rect.right + offset;
            break;
          case "right-bottom":
            top = rect.bottom - tooltipHeight;
            left = rect.right + offset;
            break;
          case "right-center":
            top = rect.top + rect.height / 2 - tooltipHeight / 2;
            left = rect.right + offset;
            break;
        }

        // Reapply custom tooltip offset after fallback
        top += tooltipOffset.y || 0;
        left += tooltipOffset.x || 0;
      }

      // Final adjustments to keep tooltip within viewport bounds
      if (left < 10) {
        left = 10;
      } else if (left + tooltipWidth > windowWidth - 10) {
        left = windowWidth - tooltipWidth - 10;
      }

      if (top < 10) {
        top = 10;
      } else if (top + tooltipHeight > windowHeight - 10) {
        top = windowHeight - tooltipHeight - 10;
      }

      return { top, left, placement };
    },
    []
  );

  const updateTooltipPosition = useCallback(() => {
    if (!tour.currentStepData || !tour.isActive) return;

    const element = document.querySelector(tour.currentStepData.target);
    if (!element) return;

    setTargetElement(element);
    const position = calculateTooltipPosition(element, tour.currentStepData);
    setTooltipPosition(position);
  }, [tour.currentStepData, tour.isActive, calculateTooltipPosition]);

  useEffect(() => {
    updateTooltipPosition();

    const handleResize = () => updateTooltipPosition();
    const handleScroll = () => updateTooltipPosition();

    window.addEventListener("resize", handleResize);
    window.addEventListener("scroll", handleScroll, true);

    return () => {
      window.removeEventListener("resize", handleResize);
      window.removeEventListener("scroll", handleScroll, true);
    };
  }, [updateTooltipPosition]);

  const handleClose = useCallback(() => {
    tour.endTour();
    onClose?.();
  }, [tour, onClose]);

  const handleSkip = useCallback(() => {
    tour.skipTour();
    onClose?.();
  }, [tour, onClose]);

  const getSpotlightElement = useCallback(() => {
    if (
      !targetElement ||
      !tour.currentStepData ||
      tour.currentStepData.disableOverlay
    ) {
      return null;
    }

    const rect = targetElement.getBoundingClientRect();
    const padding = getSpotlightPadding(tour.currentStepData);

    // Get computed styles to extract border radius
    const computedStyle = window.getComputedStyle(targetElement);
    const borderRadius = computedStyle.borderRadius || "0px";

    // Create a more sophisticated SVG-based spotlight that respects border radius
    const spotlightId = `spotlight-${tour.currentStep}`;

    return (
      <>
        {/* SVG definitions for the spotlight mask */}
        <svg
          className="absolute inset-0 pointer-events-none"
          style={{ width: "100vw", height: "100vh" }}
        >
          <defs>
            <mask id={spotlightId}>
              {/* White rectangle covering entire screen */}
              <rect width="100%" height="100%" fill="white" />
              {/* Black rounded rectangle for the cutout with individual padding */}
              <rect
                x={rect.left - padding.left}
                y={rect.top - padding.top}
                width={rect.width + padding.left + padding.right}
                height={rect.height + padding.top + padding.bottom}
                rx={borderRadius}
                ry={borderRadius}
                fill="black"
              />
            </mask>
          </defs>
          {/* Apply the mask to create the spotlight effect */}
          <rect
            width="100%"
            height="100%"
            fill="rgba(0, 0, 0, 0.4)"
            mask={`url(#${spotlightId})`}
          />
        </svg>
      </>
    );
  }, [
    targetElement,
    tour.currentStepData,
    tour.currentStep,
    getSpotlightPadding,
  ]);

  // Helper function to render content (string or React node)
  const renderContent = (content: string | React.ReactNode) => {
    if (typeof content === "string") {
      return (
        <div
          className="text-white text-sm font-satoshi-regular"
          dangerouslySetInnerHTML={{ __html: content }}
        />
      );
    } else {
      return (
        <div className="text-white text-sm font-satoshi-regular">{content}</div>
      );
    }
  };

  if (!mounted || !tour.isActive || !tour.currentStepData || !tooltipPosition) {
    return null;
  }

  const currentStep = tour.currentStepData;
  const stepNumber = tour.currentStep + 1;
  const totalSteps = tour.steps.length;

  return createPortal(
    <div className="fixed inset-0 z-[9999]">
      {/* Overlay */}
      {!config.disableOverlay && !currentStep.disableOverlay && (
        <>
          {/* SVG-based spotlight with border radius and custom padding */}
          {getSpotlightElement()}
        </>
      )}

      {/* Tooltip - Figma Design */}
      <div
        ref={tooltipRef}
        className="absolute w-80 rounded-lg shadow-lg z-[10000] bg-secondary"
        style={{
          top: tooltipPosition.top,
          left: tooltipPosition.left,
          maxWidth: "320px",
          borderRadius: "8px",
          boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.15)",
        }}
      >
        <div className="flex flex-col gap-[14px] p-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            {/* Step counter */}
            <div className="text-white text-base font-satoshi-bold">
              {stepNumber}/{totalSteps} Steps
            </div>

            {/* Close button */}
            {!config.hideCloseButton && (
              <button className="text-white " onClick={handleClose}>
                <X size={24} />
              </button>
            )}
          </div>

          {/* Content - Support HTML and React nodes */}
          {renderContent(currentStep.content)}

          {/* Actions */}
          <div className="flex items-center justify-start">
            <div className="flex items-center space-x-3">
              {/* Previous button */}
              <Button
                onClick={tour.previousStep}
                variant={"secondary"}
                className="min-w-[100px] mr-4"
                disabled={tour.isFirstStep}
              >
                <span>Previous</span>
              </Button>
            </div>

            <div className="flex items-center space-x-3">
              {/* Next/Finish button */}
              <Button
                onClick={tour.isLastStep ? handleClose : tour.nextStep}
                variant={"tertiary"}
                className="min-w-[100px]"
              >
                {tour.isLastStep ? "Finish" : "Next"}
              </Button>
            </div>
          </div>
        </div>

        {/* Tooltip arrow */}
        <div
          className={`absolute w-6 h-6 transform rotate-45 bg-secondary ${
            tooltipPosition.placement === "top"
              ? "bottom-[-8px] left-1/2 -translate-x-1/2"
              : tooltipPosition.placement === "bottom"
              ? "top-[-8px] left-1/2 -translate-x-1/2"
              : tooltipPosition.placement === "left"
              ? "right-[-12px] top-1/2 -translate-y-1/2"
              : tooltipPosition.placement === "right"
              ? "left-[-12px] top-1/2 -translate-y-1/2"
              : tooltipPosition.placement === "top-left"
              ? "bottom-[-8px] left-6"
              : tooltipPosition.placement === "top-right"
              ? "bottom-[-8px] right-6"
              : tooltipPosition.placement === "top-center"
              ? "bottom-[-8px] left-1/2 -translate-x-1/2"
              : tooltipPosition.placement === "bottom-left"
              ? "top-[-8px] left-6"
              : tooltipPosition.placement === "bottom-right"
              ? "top-[-8px] right-6"
              : tooltipPosition.placement === "bottom-center"
              ? "top-[-8px] left-1/2 -translate-x-1/2"
              : tooltipPosition.placement === "left-top"
              ? "right-[-12px] top-6"
              : tooltipPosition.placement === "left-bottom"
              ? "right-[-12px] bottom-6"
              : tooltipPosition.placement === "left-center"
              ? "right-[-12px] top-1/2 -translate-y-1/2"
              : tooltipPosition.placement === "right-top"
              ? "left-[-12px] top-6"
              : tooltipPosition.placement === "right-bottom"
              ? "left-[-12px] bottom-6"
              : tooltipPosition.placement === "right-center"
              ? "left-[-12px] top-1/2 -translate-y-1/2"
              : "hidden"
          }`}
        />
      </div>
    </div>,
    document.body
  );
};

export default Tour;
