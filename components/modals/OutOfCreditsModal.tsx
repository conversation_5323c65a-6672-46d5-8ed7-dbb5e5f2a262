"use client";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { usePricingModalStore } from "@/hooks/use-pricing";
import { useOutOfCreditsModalStore } from "@/hooks/use-out-of-credits-modal";

export const OutOfCreditsModal = () => {
  const { isOpen, closeModal } = useOutOfCreditsModalStore();
  const { openModal: openPricingModal } = usePricingModalStore();

  const handleTopUp = () => {
    closeModal();
    openPricingModal("topup"); // Open pricing modal with topup tab active
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={closeModal}>
      <AlertDialogContent className="bg-brand-card border border-brand-border-color">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-brand-primary-font">
            Out of Credits
          </AlertDialogTitle>
          <AlertDialogDescription className="text-brand-secondary-font">
            You don't have enough credits to send this message. Please top up
            your account to continue using Ruh AI.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel className="border-brand-border-color text-brand-secondary-font hover:bg-brand-card/50">
            Cancel
          </AlertDialogCancel>
          <AlertDialogAction
            onClick={handleTopUp}
            className="brand-gradient-indicator text-brand-white-text hover:opacity-90"
          >
            Top Up Credits
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
