import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useEmployeeCreateStore } from "@/hooks/useEmployeeCreateStore";
import { toast } from "sonner";
import { mcpApi } from "@/app/api/mcp";

export interface PublishAgentModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPublish: (provider: string, model: string) => void;
  onDiscard: () => void;
}

export const PublishAgentModal: React.FC<PublishAgentModalProps> = ({
  open,
  onOpenChange,
  onPublish,
  onDiscard,
}) => {
  const { data, setData } = useEmployeeCreateStore();

  // State for form fields - initialize from store data
  const [ruhCredentials, setRuhCredentials] = useState(data.ruh_credentials);
  const [selectedProvider, setSelectedProvider] = useState<string>(
    data.model_provider || ""
  );
  const [selectedModel, setSelectedModel] = useState<string>(
    data.model_name || ""
  );

  // Provider/model API state
  const [providers, setProviders] = useState<any[]>([]);
  const [models, setModels] = useState<any[]>([]);
  const [isLoadingProviders, setIsLoadingProviders] = useState(false);
  const [isLoadingModels, setIsLoadingModels] = useState(false);

  // Update local state when store data changes
  useEffect(() => {
    if (data.ruh_credentials !== undefined) {
      setRuhCredentials(data.ruh_credentials);
    }
    if (data.model_provider) {
      setSelectedProvider(data.model_provider);
    }
    if (data.model_name) {
      setSelectedModel(data.model_name);
    }
  }, [data]);

  // Fetch providers and models on mount
  useEffect(() => {
    const fetchProvidersAndModels = async () => {
      setIsLoadingProviders(true);
      try {
        const res = await mcpApi.getProviders(1, 60);
        const providerList = res?.providers || [];
        setProviders(providerList);
        if (providerList?.length && selectedProvider) {
          let providerExist = providerList?.filter(
            (item) => item?.provider === selectedProvider
          );
          if (!providerExist?.length) return;
        }
        if (providerList.length > 0) {
          let providerExist = providerList?.filter(
            (item) => item?.provider === selectedProvider
          );
          const firstProvider = providerList[0];
          const providerToUse =
            providerExist[0]?.provider || firstProvider.provider;
          setSelectedProvider(providerToUse);
          setIsLoadingModels(true);
          try {
            const modelRes = await mcpApi.getModels(
              providerExist[0]?.id || firstProvider.id,
              1,
              60
            );
            const modelList = modelRes?.models || [];
            setModels(modelList);
            if (modelList?.length && selectedModel) {
              let modelExist = modelList?.filter(
                (item) => item?.model === selectedModel
              );
              if (!modelExist?.length) return;
            }
            if (modelList.length > 0) {
              let modelExist = modelList?.filter(
                (item) => item?.model === selectedModel
              );
              setSelectedModel(modelExist[0]?.model || modelList[0].model);
            }
          } finally {
            setIsLoadingModels(false);
          }
        }
      } finally {
        setIsLoadingProviders(false);
      }
    };
    fetchProvidersAndModels();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fetch models when provider changes (user selection)
  useEffect(() => {
    if (!selectedProvider) return;
    setIsLoadingModels(true);
    const fetchModels = async () => {
      try {
        let providerExist = providers?.filter(
          (item) => item?.provider === selectedProvider
        );
        if (!providerExist?.length) return;
        const modelRes = await mcpApi.getModels(providerExist[0].id, 1, 60);
        const modelList = modelRes?.models || [];
        setModels(modelList);
        if (modelList.length > 0) {
          setSelectedModel(modelList[0].model);
        } else {
          setSelectedModel("");
        }
      } finally {
        setIsLoadingModels(false);
      }
    };
    if (providers.length > 0) {
      fetchModels();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedProvider]);

  const handlePublish = () => {
    // If using custom model, validate required fields
    if (!selectedProvider) {
      toast.error("Please select a model provider", {
        position: "top-center",
      });
      return;
    }
    if (!selectedModel) {
      toast.error("Please select a language model", {
        position: "top-center",
      });
      return;
    }

    const finalData = {
      ...data,
      model_provider: selectedProvider,
      model_name: selectedModel,
      is_imported: false,
    } as any;
    // Remove unwanted keys from data
    setData(finalData);
    onPublish(selectedProvider, selectedModel);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="md:min-w-[599px] p-8 gap-6 rounded-2xl">
        <DialogHeader className="gap-2">
          <DialogTitle className="text-[20px] font-satoshi-bold text-left">
            Are you sure you want to publish this agent?
          </DialogTitle>
          <DialogDescription className="text-left text-text-secondary">
            Here's a summary of the current model configuration
          </DialogDescription>
        </DialogHeader>
        {!ruhCredentials && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="mb-2">
                <label className="text-sm font-medium text-gray-900">
                  Select LLM Provider
                </label>
                <Select
                  value={selectedProvider}
                  onValueChange={(value) => {
                    setSelectedProvider(value);
                    setSelectedModel("");
                  }}
                  disabled={isLoadingProviders}
                >
                  <SelectTrigger className="w-full text-sm">
                    {isLoadingProviders ? (
                      <span className="flex items-center">Loading...</span>
                    ) : (
                      <SelectValue placeholder="Select a provider" />
                    )}
                  </SelectTrigger>
                  <SelectContent>
                    {providers.map((provider) => (
                      <SelectItem key={provider.id} value={provider.provider}>
                        {provider.provider}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="mb-2">
                <label className="text-sm font-medium text-gray-900">
                  Select Language Model
                </label>
                <Select
                  value={selectedModel}
                  onValueChange={setSelectedModel}
                  disabled={!selectedProvider || isLoadingModels}
                >
                  <SelectTrigger className="w-full text-sm">
                    {isLoadingModels ? (
                      <span className="flex items-center">Loading...</span>
                    ) : (
                      <SelectValue placeholder="Select a model" />
                    )}
                  </SelectTrigger>
                  <SelectContent>
                    {models.length > 0 ? (
                      models.map((model) => (
                        <SelectItem key={model.id} value={model.model}>
                          {model.model}
                        </SelectItem>
                      ))
                    ) : (
                      <span className="text-sm text-text-secondary px-2">
                        No models found
                      </span>
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}
        <div className="flex gap-3">
          <Button variant="tertiary" onClick={onDiscard} type="button">
            Discard
          </Button>
          <Button variant="primary" onClick={handlePublish} type="button">
            {"Save & Publish"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
