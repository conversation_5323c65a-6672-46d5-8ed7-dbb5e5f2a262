"use client";

import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { usePricingModalStore } from "@/hooks/use-pricing";
import { Info, Loader2, Check } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { PrimaryButton } from "../shared/PrimaryButton";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { subscriptionApi } from "@/app/api/subscription";
import { organizationApi } from "@/app/api/organization";
import { toast } from "sonner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useOrgStore } from "@/hooks/use-organization";
import { Button } from "../ui/button";

interface Plan {
  credit_amount: number;
  id: number;
  is_default: boolean;
  name: string;
  plan_id_code: string;
  price: number;
  stripe_price_id: string;
}

interface SubscriptionDetails {
  id: string;
  plan_id_code: string;
  organisation_id: string;
  status: string;
  subscription_credits: number;
  current_credits: number;
  current_period_start: string;
  current_period_end: string;
}

export const PricingModal = () => {
  const { isOpen, closeModal, defaultTab } = usePricingModalStore();
  const { currentOrganization } = useOrgStore();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState<string>(
    defaultTab || "subscription"
  );

  useEffect(() => {
    if (defaultTab) {
      setActiveTab(defaultTab);
    }
  }, [defaultTab]);

  const {
    data: plansData,
    isLoading: isLoadingPlans,
    error: plansError,
  } = useQuery({
    queryKey: ["plans"],
    queryFn: () => subscriptionApi.getPlans(),
    enabled: isOpen,
  });

  const {
    data: subscriptionData,
    isLoading: isLoadingSubscription,
    error: subscriptionError,
  } = useQuery({
    queryKey: ["subscription", currentOrganization?.id],
    queryFn: () =>
      subscriptionApi.getSubscriptionDetails(currentOrganization?.id || ""),
    enabled: !!currentOrganization?.id && isOpen,
  });

  const {
    data: topupPlansData,
    isLoading: isLoadingTopupPlans,
    error: topupPlansError,
  } = useQuery({
    queryKey: ["topupPlans"],
    queryFn: () => subscriptionApi.listTopupPlans(),
    enabled: isOpen && activeTab === "topup",
  });

  const activateFreePlanMutation = useMutation({
    mutationFn: () =>
      subscriptionApi.activateDefaultPlan(currentOrganization?.id || ""),
    onSuccess: (data) => {
      if (data.success) {
        toast.success(data.message || "Free plan activated successfully!");
        queryClient.invalidateQueries({
          queryKey: ["subscription", currentOrganization?.id],
        });
        queryClient.invalidateQueries({ queryKey: ["plans"] });
      } else {
        toast.error(data.message || "Failed to activate free plan.");
      }
    },
    onError: (error) => {
      toast.error("An error occurred while activating the free plan.");
      console.error("Error activating free plan:", error);
    },
  });

  const checkoutMutation = useMutation({
    mutationFn: (plan_id_code: string) => {
      const organisation_id = currentOrganization?.id || "";
      const payload = {
        organisation_id,
        plan_id_code,
        success_url: window.location.origin,
        cancel_url: window.location.origin,
      };
      if (activeTab === "subscription") {
        return subscriptionApi.createCheckoutSession(payload);
      } else {
        const { plan_id_code, ...rest } = payload;
        return subscriptionApi.createTopupCheckoutSession({
          ...rest,
          topup_plan_id_code: plan_id_code,
        });
      }
    },
    onSuccess: (data) => {
      if (data.checkout_url) {
        window.location.href = data.checkout_url;
      } else {
        toast.error("Failed to create checkout session. Please try again.");
      }
    },
    onError: (error) => {
      toast.error("Failed to create checkout session. Please try again.");
      console.error("Error creating checkout session:", error);
    },
  });

  const plans = plansData?.plans || [];
  const subscription = subscriptionData?.subscription || null;
  const topupPlans = topupPlansData?.topup_plans || [];

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
    }).format(price);
  };

  const isCurrentPlan = (plan: Plan) => {
    if (!subscription) return false;
    return (
      subscription.plan_id_code.toLowerCase() === plan.plan_id_code.toLowerCase()
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[900px] bg-gray-50 p-12 rounded-3xl border border-brand-border-color overflow-y-auto max-h-[90vh]">
        <DialogHeader className="mb-8">
          <DialogTitle className="text-2xl text-brand-primary-font font-bold text-center">
            Purchase a subscription
          </DialogTitle>
          <DialogDescription className="text-base text-brand-secondary-font text-center mt-1">
            Choose the plan that works for you.
          </DialogDescription>
        </DialogHeader>

        <Tabs
          value={activeTab}
          defaultValue={defaultTab || "subscription"}
          className="w-full"
          onValueChange={setActiveTab}
        >
          <TabsList className="grid w-full grid-cols-2 h-12 items-center justify-center rounded-lg bg-gray-200 p-1 text-gray-500 shadow-inner dark:bg-gray-800 dark:text-gray-400">
            <TabsTrigger
              value="subscription"
              className="inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
            >
              Subscription Plans
            </TabsTrigger>
            <TabsTrigger
              value="topup"
              className="inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
            >
              Top-up Plans
            </TabsTrigger>
          </TabsList>
          <TabsContent value="subscription" className="pt-8">
            {plansError && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-700 text-center">
                  {(plansError as any).message}
                </p>
              </div>
            )}
            {isLoadingPlans || isLoadingSubscription ? (
              <div className="flex justify-center items-center py-16">
                <div className="flex flex-col items-center gap-4">
                  <Loader2 className="h-8 w-8 animate-spin text-brand-primary-font" />
                  <p className="text-brand-secondary-font">
                    Loading your plans...
                  </p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {[...plans]
                  .sort((a, b) => a.price - b.price)
                  .map((plan, index) => (
                    <div
                      key={index}
                      className={`rounded-2xl p-8 flex flex-col ${
                        isCurrentPlan(plan)
                          ? "bg-[#1C144C] text-white"
                          : "bg-white text-black border"
                      }`}
                    >
                      <div className="flex-grow">
                        <h3 className="text-xl font-semibold">{plan.name}</h3>
                        <div className="text-4xl font-bold my-4">
                          ${plan.price}
                          <span className="text-sm font-normal">
                            {" "}
                            /month
                          </span>
                        </div>
                        {
                          plan.plan_id_code === 'free' ? (
                            <>
                              <p
                                className={`text-sm ${
                                  isCurrentPlan(plan)
                                    ? "text-gray-300"
                                    : "text-gray-500"
                                }`}
                              >
                                billed monthly
                              </p>
                              <ul className="mt-6 space-y-3">
                                <li className="flex items-center gap-2">
                                  <Check
                                    className={`w-4 h-4 ${
                                      isCurrentPlan(plan)
                                        ? "text-white"
                                        : "text-black"
                                    }`}
                                  />
                                  <span className="text-sm">20 RUH Credits</span>
                                </li>
                                <li className="flex items-center gap-2">
                                  <Check
                                    className={`w-4 h-4 ${
                                      isCurrentPlan(plan)
                                        ? "text-white"
                                        : "text-black"
                                    }`}
                                  />
                                  <span className="text-sm">1 pre-built employee</span>
                                </li>
                                <li className="flex items-center gap-2">
                                  <Check
                                    className={`w-4 h-4 ${
                                      isCurrentPlan(plan)
                                        ? "text-white"
                                        : "text-black"
                                    }`}
                                  />
                                  <span className="text-sm">3 pre-built workflows</span>
                                </li>
                                <li className="flex items-center gap-2">
                                  <Check
                                    className={`w-4 h-4 ${
                                      isCurrentPlan(plan)
                                        ? "text-white"
                                        : "text-black"
                                    }`}
                                  />
                                  <span className="text-sm">
                                    Workflow creation access
                                  </span>
                                </li>
                                <li className="flex items-center gap-2">
                                  <Check
                                    className={`w-4 h-4 ${
                                      isCurrentPlan(plan)
                                        ? "text-white"
                                        : "text-black"
                                    }`}
                                  />
                                  <span className="text-sm">
                                    Employee creation access
                                  </span>
                                </li>
                              </ul>
                            </>
                          ) : (
                            <>
                              <p
                                className={`text-sm ${
                                  isCurrentPlan(plan)
                                    ? "text-gray-300"
                                    : "text-gray-500"
                                }`}
                              >
                                billed monthly
                              </p>
                              <ul className="mt-6 space-y-3">
                                <li className="flex items-center gap-2">
                                  <Check
                                    className={`w-4 h-4 ${
                                      isCurrentPlan(plan)
                                        ? "text-white"
                                        : "text-black"
                                    }`}
                                  />
                                  <span className="text-sm">100 RUH Credits</span>
                                </li>
                                <li className="flex items-center gap-2">
                                  <Check
                                    className={`w-4 h-4 ${
                                      isCurrentPlan(plan)
                                        ? "text-white"
                                        : "text-black"
                                    }`}
                                  />
                                  <span className="text-sm">1 pre-built employee</span>
                                </li>
                                <li className="flex items-center gap-2">
                                  <Check
                                    className={`w-4 h-4 ${
                                      isCurrentPlan(plan)
                                        ? "text-white"
                                        : "text-black"
                                    }`}
                                  />
                                  <span className="text-sm">3 pre-built workflows</span>
                                </li>
                                <li className="flex items-center gap-2">
                                  <Check
                                    className={`w-4 h-4 ${
                                      isCurrentPlan(plan)
                                        ? "text-white"
                                        : "text-black"
                                    }`}
                                  />
                                  <span className="text-sm">
                                    Workflow creation access
                                  </span>
                                </li>
                                <li className="flex items-center gap-2">
                                  <Check
                                    className={`w-4 h-4 ${
                                      isCurrentPlan(plan)
                                        ? "text-white"
                                        : "text-black"
                                    }`}
                                  />
                                  <span className="text-sm">
                                    Employee creation access
                                  </span>
                                </li>
                              </ul>
                            </>
                          )
                        }
                      </div>
                      <PrimaryButton
                        onClick={() => {
                          if (plan.plan_id_code === "free") {
                            activateFreePlanMutation.mutate();
                          } else {
                            checkoutMutation.mutate(plan.plan_id_code);
                          }
                        }}
                        disabled={
                          checkoutMutation.isPending ||
                          activateFreePlanMutation.isPending ||
                          isCurrentPlan(plan) ||
                          (subscription?.plan_id_code.toLowerCase() === "pro" &&
                            plan.plan_id_code.toLowerCase() === "free")
                        }
                        className={`w-full mt-6 py-3 text-base font-semibold rounded-lg ${
                          isCurrentPlan(plan)
                            ? "bg-gray-600 text-gray-400 cursor-not-allowed"
                            : "bg-purple-600 text-white"
                        }`}
                      >
                        {activateFreePlanMutation.isPending &&
                        plan.plan_id_code === "free" ? (
                          <div className="flex items-center justify-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            <span>Activating...</span>
                          </div>
                        ) : checkoutMutation.isPending &&
                          checkoutMutation.variables === plan.plan_id_code ? (
                          <div className="flex items-center justify-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            <span>Processing...</span>
                          </div>
                        ) : isCurrentPlan(plan) ? (
                          "Current Plan"
                        ) : plan.plan_id_code === "free" ? (
                          "Activate Plan"
                        ) : (
                          "Choose Plan"
                        )}
                      </PrimaryButton>
                    </div>
                  ))}
              </div>
            )}

            {!isLoadingPlans && plans.length === 0 && !plansError && (
              <div className="flex justify-center items-center py-16">
                <p className="text-brand-secondary-font">
                  No plans available at the moment.
                </p>
              </div>
            )}
          </TabsContent>
          <TabsContent value="topup" className="pt-8">
            {topupPlansError && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-700 text-center">
                  {(topupPlansError as any).message}
                </p>
              </div>
            )}
            {isLoadingTopupPlans ? (
              <div className="flex justify-center items-center py-16">
                <div className="flex flex-col items-center gap-4">
                  <Loader2 className="h-8 w-8 animate-spin text-brand-primary-font" />
                  <p className="text-brand-secondary-font">
                    Loading top-up plans...
                  </p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...topupPlans]
                  .sort((a, b) => a.price - b.price)
                  .map((plan, index) => (
                    <div
                      key={index}
                      className="rounded-xl p-6 flex flex-col bg-white text-black border-2 border-gray-200 hover:border-purple-300 transition-all duration-200 shadow-sm hover:shadow-md"
                    >
                      <div className="flex-grow text-center">
                        <h3 className="text-lg font-semibold text-gray-800 mb-3">
                          {plan.name}
                        </h3>
                        <div className="text-3xl font-bold text-gray-900 mb-2">
                          ${plan.price}
                        </div>
                        <div className="inline-block bg-purple-100 px-3 py-1 rounded-full mb-4">
                          <span className="text-sm font-medium text-purple-700">
                            {plan.credit_amount.toLocaleString()} RCUs
                          </span>
                        </div>
                        <p className="text-sm text-gray-500 mb-4">
                          One-time purchase
                        </p>
                      </div>
                      <PrimaryButton
                        onClick={() => checkoutMutation.mutate(plan.plan_id_code)}
                        disabled={checkoutMutation.isPending}
                        className={`w-full py-3 text-sm font-semibold rounded-lg bg-purple-600 hover:bg-purple-700 text-white transition-colors duration-200 disabled:bg-purple-400 ${
                          checkoutMutation.isPending
                            ? "opacity-60 pointer-events-none"
                            : ""
                        }`}
                      >
                        {checkoutMutation.isPending &&
                        checkoutMutation.variables === plan.plan_id_code ? (
                          <div className="flex items-center justify-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            <span>Processing...</span>
                          </div>
                        ) : (
                          "Buy Now"
                        )}
                      </PrimaryButton>
                    </div>
                  ))}
              </div>
            )}
            {!isLoadingTopupPlans &&
              topupPlans.length === 0 &&
              !topupPlansError && (
                <div className="flex justify-center items-center py-16">
                  <p className="text-brand-secondary-font">
                    No top-up plans available at the moment.
                  </p>
                </div>
              )}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};