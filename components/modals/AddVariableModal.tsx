import React from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";

interface AddVariableModalProps {
  open: boolean;
  onClose: () => void;
  onSave: (variable: {
    name: string;
    description: string;
    type: string;
    default_value?: string;
  }, index?: number | null) => void;
  editingIndex: number | null;
  variableName: string;
  setVariableName: (v: string) => void;
  variableDescription: string;
  setVariableDescription: (v: string) => void;
  variableType: string;
  setVariableType: (v: string) => void;
  variableDefault: string;
  setVariableDefault: (v: string) => void;
}

const AddVariableModal: React.FC<AddVariableModalProps> = ({
  open,
  onClose,
  onSave,
  editingIndex,
  variableName,
  setVariableName,
  variableDescription,
  setVariableDescription,
  variableType,
  setVariableType,
  variableDefault,
  setVariableDefault,
}) => {
  return (
    <Dialog open={open} onOpenChange={open => { if (!open) onClose(); }}>
      <DialogContent className="p-8 max-w-lg rounded-2xl flex flex-col md:gap-6 gap-1">
        <DialogHeader className="gap-2">
          <DialogTitle className="text-[20px] font-satoshi-bold mb-1">{editingIndex !== null ? "Edit Variable" : "Add Variables"}</DialogTitle>
          <DialogDescription className="text-text-secondary text-sm">
            Add a new variable to customize your agent's behavior
          </DialogDescription>
        </DialogHeader>
        {/* Variable Name */}
        <div className="flex flex-col gap-2">
          <Label htmlFor="variable-name" className="font-satoshi-bold">Variable Name</Label>
          <Input
          className="!text-sm"
            id="variable-name"
            placeholder="Enter name"
            value={variableName}
            onChange={e => setVariableName(e.target.value)}
          />
          <span className="text-sm text-text-secondary font-satoshi-regular">A unique identifier for this variable</span>
        </div>
        {/* Description */}
        <div className="flex flex-col gap-2">
          <Label htmlFor="variable-description" className="font-satoshi-bold">Description</Label>
          <Input
          className="!text-sm"
            id="variable-description"
            placeholder="Enter description"
            value={variableDescription}
            onChange={e => setVariableDescription(e.target.value)}
          />
          <span className="text-sm text-text-secondary font-satoshi-regular">Explain what this variable is used for</span>
        </div>
        {/* Variable Type */}
        <div className="flex flex-col gap-2 ">
          <Label htmlFor="variable-type" className="font-satoshi-bold">Variable Type</Label>
          <Select value={variableType} onValueChange={setVariableType}>
            <SelectTrigger id="variable-type" className="w-full text-sm">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="text">Text</SelectItem>
              <SelectItem value="number">Number</SelectItem>
              <SelectItem value="json">JSON</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-text-secondary">The data type of this variable</span>
        </div>
        {/* Default Value */}
        <div className="flex flex-col gap-2">
          <Label htmlFor="variable-default" className="font-satoshi-bold">Default Value (Optional)</Label>
          <Input
          className="!text-sm"
            id="variable-default"
            placeholder="Default value..."
            value={variableDefault}
            onChange={e => setVariableDefault(e.target.value)}
          />
          <span className="text-sm text-text-secondary font-satoshi-regular">The default value if none is provided</span>
        </div>
        {/* Buttons */}
        <div className="flex justify-end gap-2 mt-2">
          <DialogClose asChild>
            <Button
              variant="tertiary"
              className="w-[118px]"
              onClick={onClose}
              type="button"
            >
              Cancel
            </Button>
          </DialogClose>
          <Button
            variant="primary"
            className="w-[118px]"
            onClick={() => {
              if (variableName && variableType) {
                onSave({
                  name: variableName,
                  description: variableDescription,
                  type: variableType,
                  default_value: variableDefault,
                }, editingIndex);
              }
            }}
            type="button"
            disabled={!variableName.trim() || !variableDescription.trim()}
          >
            {editingIndex !== null ? "Save Variable" : "Add Variable"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddVariableModal; 