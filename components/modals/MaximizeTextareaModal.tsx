import React, { useRef, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import EnhancePromptButton from "@/components/shared/EnhancePromptButton";
import { AIChatMode } from "@/shared/enums";

interface MaximizeTextareaModalProps {
  open: boolean;
  onClose: () => void;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  mode?: AIChatMode;
  disabled?: boolean;
}

const MaximizeTextareaModal: React.FC<MaximizeTextareaModalProps> = ({
  open,
  onClose,
  value,
  onChange,
  placeholder = "Start typing your message...",
  mode = AIChatMode.ACT,
  disabled = false,
}) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Focus textarea when modal opens
  useEffect(() => {
    if (open && textareaRef.current) {
      textareaRef.current.focus();
    }
  }, [open]);

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value);
  };

  const handleEnhancedTextUpdate = (enhancedText: string) => {
    onChange(enhancedText);
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        if (!open) onClose();
      }}
    >
      <DialogContent className="p-6 max-w-6xl w-[95vw] max-h-[90vh] rounded-2xl flex flex-col overflow-hidden">
        <DialogHeader className="gap-2 pb-4 flex-shrink-0">
          <DialogTitle className="text-xl font-satoshi-bold">
            Enhanced Prompt Editor
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 flex flex-col gap-4 min-h-0 overflow-hidden">
          {/* Enhanced Prompt Button */}
          <div className="flex justify-start flex-shrink-0">
            <EnhancePromptButton
              inputText={value}
              onTextUpdate={handleEnhancedTextUpdate}
              mode={mode}
              disabled={disabled}
            />
          </div>

          {/* Textarea Container */}
          <div className="flex-1 min-h-0 overflow-hidden p-1">
            <textarea
              ref={textareaRef}
              value={value}
              onChange={handleTextChange}
              placeholder={placeholder}
              disabled={disabled}
              className="w-full h-full min-h-[400px] p-4 border border-gray-200 rounded-lg text-base resize-none focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-background overflow-y-auto"
              style={{
                fontFamily: "inherit",
                lineHeight: "1.5",
              }}
            />
          </div>

          {/* Footer with character count */}
          <div className="flex justify-between items-center pt-2 border-t border-gray-200 flex-shrink-0">
            <span className="text-sm text-text-secondary">
              {value.length} characters
            </span>
            <Button variant="primary" onClick={onClose} className="px-6">
              Done
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MaximizeTextareaModal;
