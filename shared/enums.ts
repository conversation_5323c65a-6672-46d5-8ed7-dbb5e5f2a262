// Enum for model providers
export enum ModelProvider {
  OPENAI = "openai",
  ANTHROPIC = "anthropic",
  META = "meta",
}

export enum OpenAIModelName {
  GPT4 = "gpt-4",
  GPT35TURBO = "gpt-3.5-turbo",
}

export enum AnthropicModelName {
  CLAUDE3OPUS = "claude-3-opus",
  CLAUDE3SONNET = "claude-3-sonnet",
}

export enum MetaModelName {
  LLAMA3 = "llama-3",
}

// Enum for model names
export enum ModelName {
  GPT4 = "gpt-4",
  GPT35TURBO = "gpt-3.5-turbo",
  CLAUDE3OPUS = "claude-3-opus",
  CLAUDE3SONNET = "claude-3-sonnet",
  LLAMA3 = "llama-3",
}

// Enum for employee tone
export enum EmployeeTone {
  PROFESSIONAL = "professional",
  FRIENDLY = "friendly",
  CASUAL = "casual",
  FORMAL = "formal",
  ENTHUSIASTIC = "enthusiastic",
}

// Enum for employee department
export enum EmployeeDepartment {
  ENGINEERING = "engineering",
  MARKETING = "marketing",
  SALES = "sales",
  CUSTOMER_SUPPORT = "customer_support",
  HUMAN_RESOURCES = "human_resources",
  FINANCE = "finance",
  OPERATIONS = "operations",
  GENERAL = "general",
}

// Enum for employee visibility
export enum EmployeeVisibility {
  PUBLIC = "public",
  PRIVATE = "private",
}

// Enum for employee status
export enum EmployeeStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
}

export enum MCPCategory {
  LLM = "llm",
  MULTIMODAL = "multimodal",
  EMBEDDING = "embedding",
  VISION = "vision",
  RAG = "rag",
  AGENT = "agent",
  FINE_TUNING = "fine_tuning",
  PROMPT_ENGINEERING = "prompt_engineering",
  WORKFLOW = "workflow",
  INTEGRATION = "integration",
}

/**
 * Enum for message sender types
 */
export enum SenderType {
  UNSPECIFIED = "SENDER_TYPE_UNSPECIFIED",
  USER = "SENDER_TYPE_USER",
  ASSISTANT = "SENDER_TYPE_ASSISTANT",
  SYSTEM = "SENDER_TYPE_SYSTEM",
}

export enum LivekitRoomTopics {
  INPUT = "lk.chat",
  NORMAL_OUTPUT_MESSAGE = "lk.transcription",
  WORKFLOW_UPDATES = "lk.workflow.updates",
  WORKFLOW_APPROVAL_INPUT = "lk.workflow.approval",
}

export enum StepsStatus {
  STARTED = "started",
  CONNECTING = "connecting",
  CONNECTED = "connected",
  DISCONNECTED = "disconnected",
  COMPLETED = "completed",
  PAUSED = "paused",
  FAILED = "failed",
  ERROR = "error",
  CANCELLED = "cancelled",
  APPROVED = "approved",
  TIME_LOGGED = "time_logged",
}

export enum WorkflowStatus {
  RUNNING = "running",
  COMPLETED = "completed",
  FAILED = "failed",
  CANCELLED = "cancelled",
  UNKNOWN = "unknown",
  WAITING_FOR_APPROVAL = "waiting_for_approval",
}

export enum ResultDataType {
  STRING = "string",
  NUMBER = "number",
  ARRAY = "array",
  OBJECT = "object",
}

export enum ApprovalDecision {
  APPROVE = "approve",
  REJECT = "reject",
}

/**
 * Enum for SSE stream events
 */
export enum SSEEventType {
  KEEP_ALIVE = "keep-alive",
  MESSAGE_STREAM_STARTED = "message_stream_started",
  MESSAGE_STREAMING = "message_streaming",
  MESSAGE_END = "message_end",
  // # Connection Events
  CONNECTION = "connection",

  // # Workflow Events
  WORKFLOW_EXECUTION_STARTED = "workflow_execution_started",
  WORKFLOW_COMPLETED = "workflow-completed",
  WORKFLOW_FAILED = "workflow-failed",
  WORKFLOW_CANCELLED = "workflow-cancelled",
  WORKFLOW_UPDATE = "workflow-update",
  WORKFLOW_EXECUTION_FAILED = "workflow_execution_failed",

  // # MCP Events
  MCP_SUCCESS = "mcp-success",
  MCP_ERROR = "mcp-error",
  MCP_UPDATE = "mcp-update",

  // # Error Events
  ERROR = "error",

  // # Generic Events
  UPDATE = "update",
  MESSAGE = "message",
  MCP_EXECUTION_STARTED = "mcp_execution_started",
  KNOWLEDGE_FETCH_STARTED = "knowledge_fetch_started",

  // # Task Delegation Events
  TASK_DELEGATION_SUCCESS = "task_delegation_success",
  TASK_DELEGATION_FAILED = "task_delegation_failed",
}

/**
 * Enum for message types in streaming
 */
export enum StreamMessageType {
  TEXT = "text",
  STREAMING_CHUNK = "streaming_chunk",
}

export enum MessageSenderType {
  USER = "user",
  ASSISTANT = "assistant",
  SYSTEM = "system",
}

export enum AIChatMode {
  ASK = "ASK",
  ACT = "ACT",
}

export enum TaskStatus {
  TASK_STATUS_UNSPECIFIED = "TASK_STATUS_UNSPECIFIED",
  TASK_STATUS_RUNNING = "TASK_STATUS_RUNNING",
  TASK_STATUS_COMPLETED = "TASK_STATUS_COMPLETED",
  TASK_STATUS_FAILED = "TASK_STATUS_FAILED",
  TASK_STATUS_PAUSED = "TASK_STATUS_PAUSED",
  TASK_STATUS_CANCELLED = "TASK_STATUS_CANCELLED",
}
export enum WorkflowStepStatus {
  PENDING = "pending",
  COMPLETED = "completed",
  STARTED = "started",
  CANCELLED = "cancelled",
  FAILED = "failed",
  ERROR = "error",
  TIME_LOGGED = "time_logged",
  APPROVED = "approved",
  REJECTED = "rejected",
  CONNECTING = "connecting",
  PAUSED = "paused",
}

export enum StreamMessageSource {
  ORCHESTRATOR_EMPLOYEE = "OrchestratorEmployee",
  DISCOVERY_MASTER_EMPLOYEE = "DiscoveryMasterEmployee",
  GENERAL_KNOWLEDGE_EMPLOYEE = "GeneralKnowledgeEmployee",
  WEB_SEARCH_EMPLOYEE = "WebSearchEmployee",
  KNOWLEDGE_BASE_EMPLOYEE = "KnowledgeBaseEmployee",
}

export enum StreamMessageContentType {
  DELEGATION = "delegation",
  RESPONSE = "response_to_user",
}

export enum Mode {
  RESPONSE = "RESPONSE",
  SEARCHING = "searching",
}

export enum SearchModeResources {
  ALL = "ALL",
  RESEARCH = "RESEARCH",
  ORGANIZATION = "ORGANIZATION",
}

/**
 * Enum for message types from API
 */
export enum MessageType {
  WORKFLOW = "MESSAGE_TYPE_WORKFLOW",
}

export enum Employee {
  CARD = "CARD",
}

export enum AnalyticsEvents {
  CLICKED_CREATE_EMPLOYEE = "clicked_create_now",
  CLICKED_CREATE_EMPLOYEE_STEP = "clicked_create_employee_step",
  CLICKED_PUBLISH_EMPLOYEE = "published_employee",
  ADDED_KNOWLEDGE = "added_knowledge",
  ADDED_TOOL = "added_tool",
  ADDED_WORKFLOW = "added_workflow",

  // Home Page Events
  GLOBAL_CHAT_USER_QUERY_ENTER = "user_query_enter",
  GLOBAL_CHAT_USER_QUERY_SENT = "user_query_sent",
  NEW_CHAT_CLICKED = "new_chat_clicked",
  HOME_TAB_CLICKED = "home_tab_clicked",
  EMPLOYEE_TAB_CLICKED = "employees_tab_clicked",
  ADMIN_TAB_CLICKED = "admin_tab_clicked",
  PROFILE_SETTING_CLICKED = "profile_settings_clicked",
  USER_LOGOUT = "user_logout",
}
