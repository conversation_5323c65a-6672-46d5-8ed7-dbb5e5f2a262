import {
  HomeIcon,
  SettingsIcon,
  BotIcon,
  MessageSquareMoreIcon,
  VideoIcon,
  GlobeIcon,
  NotebookPenIcon,
  ClipboardList,
  UserPen,
  HistoryIcon,
  FolderKanban,
  Users,
  BookOpen,
  Tags,
  Workflow,
  UserIcon,
  NotepadTextIcon,
  Palette,
  Layers,
  BookOpenText,
  MessagesSquare,
  Headset,
  Lock,
  SlidersHorizontal,
  BrainCircuit,
  Settings2,
  Trello,
  Receipt,
  WrenchIcon,
  Key,
} from "lucide-react";
import { CarouselSlide, Notification } from "./interfaces";
import {
  aiEmployeeRoute,
  agentRoute,
  dashboardRoute,
  employeesRoute,
  settingsRoute,
} from "./routes";
import { AnalyticsEvents } from "./enums";

// logos path constants
export const fullLogoPath = "/assets/logos/ruh-full-logo.svg";
export const noTextLogoPath = "/assets/logos/ruh-logo-no-text.svg";
export const emptyBenchContainer = "/assets/dashboard/empty_bench_employee.svg";
export const emptyAgentsContainer = "/assets/dashboard/empty_agents.svg";

// Redirection Urls
export const marketplaceMcpsUrl = `${process.env.NEXT_PUBLIC_MARKETPLACE_URL}/mcps`;
export const marketplaceWorkflowsUrl = `${process.env.NEXT_PUBLIC_MARKETPLACE_URL}/workflows`;
export const workflowBuilderUrl = process.env.NEXT_PUBLIC_WORKFLOW_BUILDER_URL;
export const createEmployeeOnDeveloperPortalUrl = `${process.env.NEXT_PUBLIC_DEVELOPER_URL}/dashboard/agents`;
export const marketplaceAgentsUrl = `${process.env.NEXT_PUBLIC_MARKETPLACE_URL}/agents`;

// carousel images for auth carousel component
export const carouselSlides: CarouselSlide[] = [
  {
    image: "/assets/carousel/carousel-1.svg",
    title: "Digitalize Your Workforce with AI Employees You Can Build",
  },
  {
    image: "/assets/carousel/carousel-2.svg",
    title: "Automate Your Everyday Work with Intelligent AI-Powered Workflows",
  },
  {
    image: "/assets/carousel/carousel-3.svg",
    title: "Connect Your AI Employees Across Multiple Communication Channels",
  },
];

// Left bar items for the dashboard
export const leftBarItems = [
  {
    name: "Home",
    icon: HomeIcon,
    // href: dashboardRoute,
    href: dashboardRoute,
    analytics_event_name: AnalyticsEvents.HOME_TAB_CLICKED,
  },
  // {
  //   name: "AI Employee",
  //   icon: BotIcon,
  //   href: aiEmployeeRoute,
  // },
  // {
  //   name: "Employees",
  //   icon: BotIcon,
  //   href: employeesRoute,
  // },

  {
    name: "Employees",
    icon: BotIcon,
    href: employeesRoute,
    analytics_event_name: AnalyticsEvents.EMPLOYEE_TAB_CLICKED,
  },
];

// Chat Navbar Tabs
export const chatNavbarTabs = [
  {
    label: "Chat",
    value: "Chat",
    icon: MessageSquareMoreIcon,
  },
  // {
  //   label: "Workflow",
  //   value: "Workflow",
  //   icon: WorkflowIcon,
  // },
  {
    label: "Conversation History",
    value: "Conversation History",
    icon: HistoryIcon,
  },
];

// Employee Chat Tools
export const tools = [
  {
    label: "Blog Generation",
    toolIcon: NotebookPenIcon,
    id: "blog-generation",
    placeholder: "Enter your blog idea here. What do you have in mind?",
  },
  {
    label: "Video Generation",
    toolIcon: VideoIcon,
    id: "video-generation",
    placeholder: "Enter your video idea here. What do you have in mind?",
  },
  {
    label: "Social Media Creation and Posting",
    toolIcon: GlobeIcon,
    id: "social-media-creation-and-posting",
    placeholder: "Enter your social media idea here. What do you have in mind?",
  },
];

// TODO: Actual images will be coming from database this is dummy data for now
export const employeeCarouselImages = [
  "/assets/carousel/add-carousel-test.png",
  "/assets/carousel/add-carousel-test.png",
  "/assets/carousel/add-carousel-test.png",
];

// TODO: Add connected apps, this is just dummy data for now
export const connectedApps = [];

//Connected Apps icon and text
export const connectedAppsData = [
  {
    id: "gmail",
    name: "Gmail",
    icon: "/assets/icons/GmailIcon.svg",
    connected: false,
  },
  {
    id: "whatsapp",
    name: "WhatsApp",
    icon: "/assets/icons/WhatsappIcon.svg",
    connected: false,
  },
  {
    id: "slack",
    name: "Slack",
    icon: "/assets/icons/SlackIcon.svg",
    connected: false,
  },
];

//Employee Sidebar Buttons
export const employeeButtons = [
  {
    icon: ClipboardList,
    text: "View all employee",
    path: "/employees",
  },
  {
    icon: UserPen,
    text: "Create employee",
    path: "/employees/create",
  },
  // {
  //   icon: UserPlus,
  //   text: "Add employee",
  //   // path: "/dashboard/employees/add",
  //   path: "/dashboard",
  // },
];

//Settings Sidebar Buttons
export const settingsButtons = [
  {
    icon: Trello,
    text: "Departments",
    path: "/admin-settings",
    tourClass: "tour-admin-settings-departments",
  },

  {
    icon: Users,
    text: "Members",
    path: "/admin-settings/manage-members",
    tourClass: "tour-admin-settings-members",
  },
  {
    icon: BrainCircuit,
    text: "Knowledge",
    path: "/admin-settings/knowledge-base",
    tourClass: "tour-admin-settings-knowledge",
  },

  {
    icon: Settings2,
    text: "Workspace Settings",
    path: "/admin-settings/workspace-settings",
    tourClass: "tour-admin-settings-workspace-settings",
  },

  {
    icon: Settings2,
    text: "Token Usage",
    path: "/admin-settings/token-usage",
    tourClass: "tour-admin-settings-token-usage",
  },
  {
    icon: Receipt,
    text: "Payment History",
    path: "/admin-settings/payment-history",
    tourClass: "tour-admin-settings-payment-history",
  },
  {
    icon: Settings2,
    text: "Usage Breakdown",
    path: "/admin-settings/usage-breakdown",
    tourClass: "tour-admin-settings-usage-breakdown",
  },
];

// User Settings Sidebar Buttons
export const userSettingsButtons = [
  {
    icon: UserIcon,
    text: "Profile",
    path: "/user-settings",
    tourClass: "tour-user-settings-profile",
  },
  {
    icon: Lock,
    text: "Password",
    path: "/user-settings/password",
    tourClass: "tour-user-settings-password",
  },
  {
    icon: SlidersHorizontal,
    text: "Preferences",
    path: "/user-settings/preferences",
    tourClass: "tour-user-settings-preferences",
  },
  {
    icon: WrenchIcon,
    text: "Manage Tools",
    path: "/user-settings/manage-tools",
    // tourClass: "tour-user-settings-preferences",
  },
  {
    icon: Key,
    text: "API keys & integrations",
    path: "/user-settings/integrations",
    // tourClass: "tour-user-settings-preferences",
  },

  // {
  //   icon: Palette,
  //   text: "Appearance & Theme",
  //   path: "/",
  // },
  // {
  //   icon: Layers,
  //   text: "Preferences",
  //   path: "/",
  // },
  // {
  //   icon: BookOpenText,
  //   text: "Docs",
  //   path: "/",
  // },
  // {
  //   icon: MessagesSquare,
  //   text: "Notifications",
  //   path: "/",
  // },
  // {
  //   icon: Headset,
  //   text: "Support",
  //   path: "/",
  // },
];

export const faqs = [
  {
    question:
      "What is the difference of usage between Chat mode and Workflow Mode?",
    answer:
      "Chat mode allows for freeform conversation, while Workflow mode guides the AI through specific, predefined tasks.",
  },
  {
    question:
      "How can I access all the workflows that an employee can execute?",
    answer:
      "You can view available workflows in the 'Workflows' section of the employee's profile or by asking the AI directly in Chat mode.",
  },
  {
    question: "How can I create a new chat with the employee?",
    answer:
      "Navigate to the employee's profile and click the 'Start Chat' button, or select the employee from the main chat interface.",
  },
];

export const unpublish_employee = "/assets/dashboard/unpublish_employee.svg";
export const publish_employee = "/assets/dashboard/publish-agent.png";

export const pricingPlans = [
  {
    name: "Free Plan",
    price: "$0",
    features: [
      "600 Free Credits",
      "1 pre-built employee",
      "3 pre-built workflows",
      "Workflow creation access (limited to 5 workflows)",
      "Employee creation access (limited to 10 employees)",
    ],
  },
  {
    name: "Standard Plan",
    price: "$30",
    features: [
      "600 Free Credits",
      "1 pre-built employee",
      "3 pre-built workflows",
      "Workflow creation access (limited to 5 workflows)",
      "Employee creation access (limited to 10 employees)",
    ],
  },
  {
    name: "Pro Plan",
    price: "$60",
    features: [
      "600 Free Credits",
      "1 pre-built employee",
      "3 pre-built workflows",
      "Workflow creation access (limited to 5 workflows)",
      "Employee creation access (limited to 10 employees)",
    ],
  },
];

export const GLOBAL_AGENT = {
  id: "global",
  name: "Ruh AI Agent",
  designation: "AI Agent",
  avatar: "/assets/images/ruh-logo.png",
  description: "Ruh AI Agent",
};

export const LOCAL_STORAGE_KEYS = {
  SELECTED_MODE: "selected_mode",
  SELECTED_SOURCE: "selected_source",
  IS_NEW_CHAT: "is_new_chat",
  RUH_HOME_PAGE_TOUR_COMPLETED: "ruh_home_page_tour_completed",
  RUH_EMPLOYEE_TOUR_COMPLETED: "ruh_employee_tour_completed",
  RUH_ADMIN_SETTINGS_TOUR_COMPLETED: "ruh_admin_settings_tour_completed",
  RUH_USER_SETTINGS_TOUR_COMPLETED: "ruh_user_settings_tour_completed",
  RUH_CREATE_EMPLOYEE_TOUR_COMPLETED: "ruh_create_employee_tour_completed",
  GLOBAL_TASK_QUICK_TOOLS: "global_task_quick_tools",
  ORIGIN_FOR_CREATE_AND_EDIT_AGENT: "origin_for_create_and_edit_agent",
  GLOBAL_CHAT_HISTORY_FILTER: "global_chat_history_filter",
  RUH_GLOBAL_AGENT_PENDING_MESSAGE: "ruh_global_agent_pending_message",
  AGENT_DETAILS_CACHE: "agent_details_cache",
};

// Keywords to filter/remove from message content
export const FILTERED_MESSAGE_KEYWORDS = ["ORCHESTRATOR_TERMINATE"];

export const TASK_MODE_PLACEHOLDER_TEXT = [
  "Generate a pitch deck for investor meeting",
  "Create a blog post on AI in healthcare",
  "Build a React dashboard for user analytics",
  "Summarize this research PDF",
  "Create a product comparison sheet in Excel",
  "Design social media posts for product launch",
  "Automate follow-up emails for leads",
  "Draft a client proposal for XYZ Corp",
  "Translate this document to Spanish",
  "Generate SEO-optimized content briefs",
  "Prepare meeting notes from this transcript",
  "Write a cold outreach email for SaaS product",
  "Analyze customer feedback for top issues",
  "Develop a REST API for task management",
  "Schedule 5 LinkedIn posts for the week",
  "Summarize last quarter’s marketing performance",
  "Generate a weekly sales report",
  "Build a landing page using TailwindCSS",
  "Create a user onboarding checklist",
  "Draft a press release for product update",
];

export const SEARCH_MODE_PLACEHOLDER_TEXT = [
  "What’s the status of the XYZ campaign?",
  "Who is handling the ABC client account?",
  "Where is the latest brand guideline PDF?",
  "What are our current hiring priorities?",
  "Show me the notes from yesterday’s leadership call",
  "List all tasks assigned to the marketing team",
  "Who approved the Q2 marketing budget?",
  "What's the company policy on remote work?",
  "Find the MOU signed with ACME Corp",
  "What was discussed in the last product sprint retro?",
  "Where can I find the onboarding checklist for interns?",
  "Give me the latest version of the pitch deck",
  "Who owns the billing integration project?",
  "What’s the renewal date for Salesforce license?",
  "Show competitor analysis report from March",
  "What’s our NPS score for this quarter?",
  "Find the email template used for partnership outreach",
  "What’s the difference between Plan A and Plan B in the proposal?",
  "List all AI employees involved in content generation",
  "Who’s the point of contact for the India office?",
];

// Tone-specific loading messages
export const TONE_LOADING_MESSAGES = {
  professional: "Please wait a moment while {employeeName} prepares to begin.",
  friendly: "{employeeName} is getting things ready — just a moment!",
  casual: "One sec — {employeeName} is setting things up.",
  formal: "Kindly hold on while {employeeName} prepares for our discussion.",
  enthusiastic: "Just a moment — {employeeName} is getting ready to jump in.",
  default: "Please wait while {employeeName} gets ready.",
};

export const LIMIT = 25;
