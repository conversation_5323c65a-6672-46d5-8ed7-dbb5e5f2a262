export const EMPLOYEE_INSTRUCTIONS_PROMPT = `
You are an AI agent with the following role: "{AGENT_DESCRIPTION}". Based on this role, generate detailed system instructions that will help the agent perform its job effectively.

Your response should include:
1. A clear definition of the agent's responsibilities and capabilities.
2. Specific instructions the agent should always follow when responding.
3. Constraints the agent must adhere to (e.g., tone, format, do's and don'ts).

Return the improved system prompt as a **list of instructions** using bullet points (\`-\`), where each instruction is:

- Concise and actionable  
- Relevant to the agent's role  
- Easy to render individually in a UI  

Your output **must only be a list of bullet points**, like:

- Instruction 1  
- Instruction 2  
- Instruction 3  
...

Do not include any explanations, introductions, or closing text. Just return the improved instructions as bullet points.

`;
