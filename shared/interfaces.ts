import { TrackReference } from "@livekit/components-react";
import { Room } from "livekit-client";
import {
  AnthropicModelName,
  ApprovalDecision,
  EmployeeDepartment,
  EmployeeStatus,
  EmployeeTone,
  EmployeeVisibility,
  MetaModelName,
  ModelProvider,
  OpenAIModelName,
  ResultDataType,
  SenderType,
  StepsStatus,
  WorkflowStatus,
} from "./enums";

// ==========================================
// UI Component Interfaces
// ==========================================

// Password validation indicator props interface
export interface PasswordValidationIndicatorProps {
  password: string;
  showValidation?: boolean;
}

// Carousel slide interface
export interface CarouselSlide {
  image: string;
  title: string;
}

// Primary button props interface
export interface PrimaryButtonProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  isLoading?: boolean;
  disabled?: boolean;
  type?: "button" | "submit" | "reset";
}

// Secondary button props interface
export interface SecondaryButtonProps {
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  isLoading?: boolean;
  disabled?: boolean;
}

// Dialog carousel props interface
export interface DialogCarouselProps {
  images: string[];
}

// Custom separator props interface
export interface CustomSeparatorProps {
  className?: string;
}

export interface User {
  id?: string | null;
  fullName?: string | null;
  email?: string | null;
  profileImage?: string | null;
  company?: string | null;
  department?: string | null;
  jobRole?: string | null;
  phoneNumber?: string | null;
  isFirstLogin?: boolean | null;
  accessToken?: string | null;
}

// Employee avatar props interface
export interface EmployeeAvatarProps {
  src?: string;
  name?: string;
  className?: string;
  onClick?: () => void;
}

// Chat avatar props interface
export interface ChatAvatarProps {
  src?: string;
  name?: string;
  className?: string;
}

// Left bar props interface
export interface LeftBarProps {
  isSecondaryBarOpen: boolean;
  toggleSecondaryBar: () => void;
}

// Employee props interface
export interface EmployeeSidebarCardProps {
  name: string;
  designation: string;
  avatar: string;
  id: string;
}

// Add employee card props interface
export interface AddEmployeeCardProps {
  name: string;
  designation: string;
  description: string;
  image: string;
}

// Employee dialog props interface
export interface EmployeeDialogProps {
  employee: {
    name: string;
    designation: string;
    description: string;
    image: string;
    carouselImages: string[];
  };
}

// Chat navbar props interface
export interface ChatNavbarProps {
  onTabChange?: (value: string) => void;
  employeeName: string;
  employeeDesignation: string;
  employeeAvatar: string;
}

// Employee chat section props interface
export interface EmployeeChatSectionProps {
  agent: Agent;
}

// Chat tool tab props interface
export interface ChatWorkflowTabProps {
  label: string;
  id: string;
}

// ChatInputProps interface
export interface ChatInputProps {
  inputValue: string;
  onInputChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onSendClick: () => void;
  employeeName: string;
  employeeDesignation: string;
  employeeWorkflows: string[] | null;
  disabled?: boolean;
  setIsApprovalRequired: (isApprovalRequired: boolean) => void;
  handleApproval: (decision: ApprovalDecision) => void;
}

// Chat bubble props interface
export interface ChatBubbleProps {
  sender: SenderType;
  avatar?: string;
  name?: string;
}

// Employee chat starter props interface
export interface EmployeeChatStarterProps {
  employeeName: string;
  employeeAvatar?: string;
  employeeDescription: string;
  employeeWorkflows?: string[];
}

// Types for our connected apps
export interface ConnectedApp {
  id: string;
  name: string;
  icon: string;
  connected: boolean;
}

// Employee types
export interface Employee {
  name: string;
  designation: string;
  avatar: string;
  description: string;
  id: string;
  assigned_task?: string;
  mcps?: MCPDetails[];
  workflows?: WorkflowDetails[];
}

export interface BenchedEmployee {
  name: string;
  designation: string;
  description: string;
  image: string;
}

export interface Notification {
  id: string;
  title: string;
  description: string;
  organisation_id: string;
  user_id: string;
  link?: string;
  logo?: string;
  seen: boolean;
  created_at: string;
}

// Define the props type for the tools component
export interface EmployeeToolTabProps {
  onClose: () => void;
}

// Define the tool type
export interface Tool {
  id: string;
  name: string;
  icon: string;
  description: string;
  category: string;
  langgenius?: string;
}

// ==========================================
// LiveKit Interfaces
// ==========================================

/**
 * Agent state for LiveKit voice interactions
 */
export type AgentState =
  | "connecting"
  | "listening"
  | "thinking"
  | "speaking"
  | "disconnected";

/**
 * LiveKit connection state interface
 */
export interface LiveKitConnectionState {
  isConnected: boolean;
  connectionStatus: string;
  connectionError: string | null;
  isMuted: boolean;
  agentState: AgentState;
  remoteTrackRef: TrackReference | null;
  localTrackRef: TrackReference | null;
}

/**
 * Props for Agent Call dialog component
 */
export interface AgentCallDialogProps {
  isOpen: boolean;
  onClose: () => void;
  connectionState: LiveKitConnectionState & { room: Room };
  onToggleMute: () => void;
}

/**
 * Props for audio visualizer component
 */
export interface AudioVisualizerProps {
  agentState: AgentState;
  isConnected: boolean;
  isMuted: boolean;
  remoteTrackRef: TrackReference | null;
  localTrackRef: TrackReference | null;
  room: Room; // Room from livekit-client
}

// ==========================================
// API Response Interfaces
// ==========================================

// Auth API Interfaces

/**
 * Response from login API
 */
export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  accessTokenAge?: number;
  refreshTokenAge?: number;
  success: boolean;
  message: string;
}

/**
 * Response from signup API
 */
export interface SignupResponse {
  success: boolean;
  message: string;
}

/**
 * Combined login result with redirect path
 */
export interface LoginResult {
  loginData: LoginResponse;
  redirectPath: string;
}

/**
 * Confirmation screen props interface
 */
export interface ConfirmationScreenProps {
  title: string;
  message: string;
  email: string;
  onBackToLogin: () => void;
}

/**
 * Response from token refresh API
 */
export interface TokenResponse {
  success: boolean;
  access_token: string;
  token_type: string;
  tokenExpireAt: string;
}

// User API Interfaces

/**
 * User information returned from API
 */
export interface UserInfo {
  id: string;
  email: string;
  fullName: string;
  company: string;
  department: string;
  jobRole: string;
  phoneNumber: string;
  profileImage: string;
  isFirstLogin: boolean;
}

/**
 * Request payload for updating user profile
 */
export interface UserProfileUpdateRequest {
  company?: string | null;
  department?: string | null;
  job_role?: string | null;
}

/**
 * Response from profile update API
 */
export interface ProfileUpdateResponse {
  message: string;
}

/**
 * Data structure for user updates in the frontend
 */
export interface UserUpdateData {
  fullName?: string;
  company?: string;
  department?: string;
  jobRole?: string;
}

/**
 * Request payload for updating user details
 */
export interface UserUpdate {
  full_name?: string | null;
  phone_number?: string | null;
  profile_image?: string | null;
  fcm_token?: string | null;
}

/**
 * Response from user details API
 */
export interface UserResponse {
  email: string;
  fullName: string;
  id: string;
  phoneNumber?: string | null;
  profileImage?: string | null;
  created_at: string;
  updated_at: string;
}

export interface UserGeneralSettingsResponse {
  success: boolean;
  message: string;
  preference: {
    id: string;
    user_id: string;
    provider: string;
    model: string;
    temperature: number;
    max_output_tokens: number;
    created_at: string;
    updated_at: string;
  };
}
// ==========================================
// GCS API Interfaces
// ==========================================

/**
 * Options for uploading files to GCS
 */
export interface GCSUploadOptions {
  contentType?: string;
  metadata?: Record<string, string>;
}

/**
 * Request for the new presigned URL API
 */
export interface PreSignedUrlRequest {
  fileName: string;
  fileType: string;
  filePath: string;
}

/**
 * Response from the new presigned URL API
 */
export interface PreSignedUrlResponse {
  success: boolean;
  url: string;
}

// ==========================================
// Agent API Interfaces
// ==========================================

/**
 * Base interface for Agent properties, used for creation and updates.
 */
export interface AgentBase {
  id: string;
  name: string;
  description: string;
  avatar: string;
  system_message: string;
  // model_provider: ModelProvider | null;
  // model_name: OpenAIModelName | AnthropicModelName | MetaModelName | null;
  model_provider: string;
  model_name: string;
  workflow_ids?: string[] | null;
  mcp_server_ids?: string[] | null;
  agent_topic_type?: string | null;
  visibility: EmployeeVisibility;
  department: string;
  category: EmployeeDepartment;
  is_imported: boolean;
  tone: EmployeeTone;
  files: string[] | null;
  urls: string[] | null;
  ruh_credentials: boolean;
  task_counts?: {
    total: number;
    completed: number;
    inProgress: number;
  };
}

/**
 * Response from get agents API
 */
export interface AgentResponse {
  agents: Agent[];
  success: boolean;
  message?: string;
}

/**
 * Agent information from database as returned by the API
 */
export interface Agent extends AgentBase {
  owner_id: string;
  user_ids: string[];
  owner_type: string;
  template_id: string;
  template_owner_id: string;
  is_bench_employee: boolean;
  is_changes_marketplace: boolean;
  is_a2a: boolean;
  is_customizable: boolean;
  subscriptions: string;
  tags: string[];
  status: string;
  organization_id: string;
  agent_capabilities: {
    capabilities: Array<{
      title: string;
      description: string;
    }>;
    input_modes: string[];
    output_modes: string[];
    response_model: string[];
    id: string;
    created_at: string;
    updated_at: string;
  };
  model_data: {
    model_id: string;
    context_window: number | null;
    model_provider: string;
    model_name: string;
    temperature: number;
    max_tokens: number;
  };
  example_prompts: string[];
  created_at: string;
  updated_at: string;
  variables: Array<{
    name: string;
    description: string;
    type: string;
    default_value: string;
    id: string;
    created_at: string;
    updated_at: string;
  }>;
}

/**
 * Response from get agent details API
 */
/**
 * Extended MCP interface for agent details response
 */
export interface MCPDetails {
  id: string;
  name: string;
  logo: string;
  description: string;
  owner_id: string;
  user_ids: string[];
  owner_type: string;
  urls: Array<{
    url: string;
    type: string;
  }>;
  git_url: string;
  git_branch: string;
  deployment_status: string;
  visibility: string;
  tags: string[];
  status: string;
  created_at: string;
  updated_at: string;
  department: string;
  mcp_tools_config: Record<string, any>;
  is_added: boolean;
}

/**
 * Extended Workflow interface for agent details response
 */
export interface WorkflowDetails {
  id: string;
  name: string;
  description: string;
  workflow_url: string;
  builder_url: string;
  start_nodes: Array<Record<string, any>>;
  owner_id: string;
  user_ids: string[];
  owner_type: string;
  workflow_template_id: string;
  template_owner_id: string;
  url: string;
  is_imported: boolean;
  version: string;
  visibility: string;
  category: string;
  tags: Record<string, any>;
  status: string;
  is_changes_marketplace: boolean;
  created_at: string;
  updated_at: string;
  available_nodes: Array<Record<string, any>>;
}

/**
 * Extended Agent interface for detailed response
 */
export interface AgentWithDetails extends Agent {
  mcps: MCPDetails[];
  workflows: WorkflowDetails[];
}

export interface AgentDetailsResponse {
  success: boolean;
  message: string;
  agent: AgentWithDetails;
}

/**
 * Request for updating an agent
 */
export interface AgentUpdateRequest extends AgentBase {
  // Inherits all fields from AgentBase
  // Currently requires all fields for update as per original definition
}

/**
 * Response from update agent API
 */
export interface AgentUpdateResponse {
  success: boolean;
  message: string;
}

/**
 * Pagination metadata for paginated responses
 */
export interface PaginationMetadata {
  total: number;
  totalPages: number;
  currentPage: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * Paginated response for agent list
 */
export interface UserAgentsResponse {
  data: Agent[];
  metadata: PaginationMetadata;
}

/**
 * Separate Modal component that takes isOpen and onClose props
 */
export interface FullScreenModalWithTabsProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Request body for creating an agent
 */
export interface AgentCreateRequest extends Omit<AgentBase, "id"> {
  // Inherits all fields from AgentBase except id
}

/**
 * Response from creating an agent
 */
export interface AgentCreateResponse {
  success: boolean;
  message: string;
}

/**
 * Payload for updating agent settings
 */
export interface AgentSettingsUpdatePayload {
  user_ids?: string[] | null;
  agent_topic_type?: string | null;
  subscriptions?: string | null;
  tags?: Record<string, any> | null;
  status?: EmployeeStatus | null;
  is_changes_marketplace?: boolean | null;
  is_bench_employee?: boolean | null;
}

/**
 * Payload for updating agent settings
 */
export interface AgentCoreDetailsUpdatePayload {
  name?: string | null;
  description?: string | null;
  avatar?: string | null;
  category?: string | null;
  tone?: EmployeeTone | null;
  agent_topic_type?: string | null;
  model_provider?: string | null;
  model_name?: string | null;
  system_message?: string | null;
  ruh_credentials?: boolean | null;
}

/* Agent capabilities */
export interface AgentCapabilitiesPayload {
  capabilities: { title: string; description: string }[];
}

/* Agent variables */
export interface AgentVariablesPayload {
  variables: {
    name: string;
    description: string;
    type: any;
    default_value: string;
  }[];
}

/**
 * Payload for updating agent knowledge
 */
export interface AgentKnowledgeUpdatePayload {
  files?: string[] | null;
  urls?: string[] | null;
}

/**
 * Payload for updating agent tools
 */
export interface AgentToolsUpdatePayload {
  mcp_server_ids?: string[] | null;
}

/**
 * Payload for updating agent workflows
 */
export interface AgentWorkflowsUpdatePayload {
  workflow_ids?: string[] | null;
}

/**
 * Response from updating agent settings
 */
export interface AgentPartUpdateResponseAPI {
  success: boolean;
  message: string;
}

// =========================================
// Agent Avatars Interfaces
// =========================================

export interface AgentAvatarInDB {
  url: string;
  id: string;
  created_at: string; // Assuming ISO date string
  updated_at: string; // Assuming ISO date string
}

export interface ListAgentAvatarsResponse {
  avatars: AgentAvatarInDB[];
  metadata: PaginationMetadata; // Reusing existing PaginationMetadata
}

// ==========================================
// MCP API Interfaces
// ==========================================

/**
 * Base MCP interface containing common properties
 */
export interface MCPInDB {
  id: string;
  name: string;
  description: string;
  department: string;
  logo: string;
  env_keys?: { key: string }[];
  env_credential_status?: "pending_input" | "provided";
  oauth_details?: {
    tool_name: string;
    provider: string;
  };
}

/**
 * Response from paginated MCP list API
 */
export interface PaginatedMCPResponse {
  data: MCPInDB[];
  metadata: PaginationMetadata;
  quick_tools_only?: boolean;
}

/**
 * Request for getting MCPs by their IDs
 */
export interface MCPsByIdsRequest {
  ids: string[];
}

/**
 * Response from getting MCPs by their IDs
 */
export interface MCPsByIdsResponse {
  success: boolean;
  message: string;
  mcps: MCPInDB[];
  total: number;
}

/**
 * Response from getting MCP environment key values
 */
export interface MCPEnvKeyValuesResponse {
  success: boolean;
  message: string;
  env_key_values: {
    key: string;
    value: string;
  }[];
}

export interface LLMProvidersResponse {
  success: boolean;
  message: string;
  providers: {
    id: string;
    provider: string;
    description: string;
    baseUrl: string;
    isActive: boolean;
    isDefault: boolean;
    createdAt: string;
    updatedAt: string;
    modelCount: number;
  }[];
  pagination: PaginationMetadata;
}

export interface LLMProviderResponse {
  success: boolean;
  message: string;
  models: [
    {
      id: string;
      providerId: string;
      model: string;
      modelId: string;
      description: string;
      pricePerTokens: number;
      maxTokens: number;
      temperature: number;
      providerType: string;
      isActive: boolean;
      isDefault: boolean;
      createdAt: string;
      updatedAt: string;
      provider: LLMProvidersResponse;
    },
  ];
  pagination: PaginationMetadata;
}

export interface WorkflowField {
  field: string;
  type: "string" | "enum" | "number" | "boolean" | "array" | "multiline";
  enum?: string[];
  value?: string | string[];
  transition_id?: string;
}

/**
 * Workflow interface for database
 */
export interface WorkflowInDB {
  id: string;
  name: string;
  description: string;
  category: string;
  start_nodes: WorkflowField[];
  available_nodes: any[];
}

/**
 * Response from paginated workflow list API
 */
export interface PaginatedWorkflowResponse {
  data: WorkflowInDB[];
  metadata: PaginationMetadata;
}

/**
 * Response from getting Workflows by their IDs
 */
export interface WorkflowsByIdsResponse {
  success: boolean;
  message: string;
  workflows: WorkflowInDB[];
  total: number;
}

// ==========================================
// Communication API Interfaces
// ==========================================

/**
 * Enum for channel types in communication
 */
export enum ChannelType {
  UNSPECIFIED = "CHANNEL_TYPE_UNSPECIFIED",
  WEB = "CHANNEL_TYPE_WEB",
}

/**
 * Enum for chat types in communication
 */
export enum ChatType {
  UNSPECIFIED = "CHAT_TYPE_UNSPECIFIED",
  SINGLE = "CHAT_TYPE_SINGLE",
  MULTI = "CHAT_TYPE_MULTI",
  GLOBAL = "CHAT_TYPE_GLOBAL",
  AGENT = "CHAT_TYPE_AGENT",
}

/**
 * Request payload for creating a conversation
 */
export interface ConversationCreate {
  chatType: ChatType;
  agentId?: string;
}

/**
 * Response from conversation creation API
 */
export interface ConversationResponse {
  title: string;
  channel: ChannelType;
  chatType: ChatType;
  summary?: string | null;
  id: string;
  userId: string;
  agentId: string;
  createdAt: string;
  updatedAt: string;
  inputTokens: any | null;
  outputTokens: any | null;
}

/**
 * Response for paginated conversation list
 */
export interface ConversationList {
  data: ConversationResponse[];
  metadata: PaginationMetadata;
}

/**
 * Parameters for getting conversations
 */
export interface GetConversationsParams {
  agentId: string;
  channel?: ChannelType | null;
  chatType?: ChatType | null;
  page?: number;
  limit?: number;
  search?: string;
}

/**
 * Message response from the API
 */
export interface MessageResponse {
  conversationId: string;
  senderType: SenderType;
  content?: string | null;
  workflowId?: string | null;
  workflowResponse?: Record<string, Record<string, any>> | null;
  id: string;
  createdAt: string;
  updatedAt: string;
  data: {
    message: string;
    attachments: {
      file_url: string;
      file_name: string;
      file_type: string;
      file_size: number;
    }[];
    mode: string;
  };
}

/**
 * Paginated message list response
 */
export interface MessageList {
  data: MessageResponse[];
  metadata: PaginationMetadata;
}

/**
 * Parameters for getting messages
 */
export interface GetMessagesParams {
  conversationId: string;
  page?: number;
  limit?: number;
  index?: number;
}

// ==========================================
// Chat Interfaces
// ==========================================

export interface WorkflowPayload {
  workflow_id: string;
  approval: boolean;
  payload: {
    user_dependent_fields: string[];
    user_payload_template: Record<string, any>;
  };
}

export interface LivekitInputMessageType {
  content: string;
  workflow: WorkflowPayload | null;
}

export interface LivekitNormalOutputMessageType {
  is_initial_response: boolean;
  content: string;
  error: boolean;
}

export interface WorkflowResultData {
  data: string | number | WorkflowResultData[] | Record<string, any>;
  data_type: ResultDataType;
  property_name: string;
}

export interface LivekitWorkflowUpdateMessageType {
  tool_name: string;
  approval_required: boolean;
  status: StepsStatus;
  workflow_status: WorkflowStatus;
  result: WorkflowResultData[];
}

export interface WorkflowStep {
  step: string;
  status: StepsStatus;
  timeLogged?: string;
}

export interface ChatMessage {
  id: string;
  content: string | LivekitInputMessageType;
  workflowId: string | null;
  workflowResponse: LivekitWorkflowUpdateMessageType | null;
  senderType: SenderType;
  isApprovalRequired?: boolean;
  isError?: boolean;
}

export interface ApprovalInput {
  decision: ApprovalDecision;
}

// ==========================================
// SSE Streaming Interfaces
// ==========================================

/**
 * Base SSE event structure
 */
export interface SSEEvent {
  event: string;
  data: string;
}

/**
 * Parsed SSE message data for streaming events
 */
export interface SSEStreamData {
  content?: string;
  content_type?: string;
  message_type?: string;
  source?: string;
  status?: string;
  session_id?: string;
  models_usage?: {
    prompt_tokens: number;
    completion_tokens: number;
  };
  metadata?: {
    content_type?: string;
    source?: string;
    content?: string;
    agent_found?: boolean;
    matched_employee?: {
      employee_profile: Employee;
      assigned_task?: string;
    };
  };
  workflow_id?: string;
  correlation_id?: string;
  tool_name?: string;
  logo?: string;
  description?: string;
  // Task delegation success properties
  task_id?: string;
  agent_id?: string;
  conversation_id?: string;
  agent_session_id?: string;
  global_session_id?: string;
  title?: string;
}

/**
 * Streaming message chunk interface
 */
export interface StreamingMessage {
  id: string;
  content: string;
  isComplete: boolean;
  source?: SenderType;
  timestamp: Date;
  searchSources?: any[];
  employeeSearchType?: string;
}

/**
 * Stream state interface for managing streaming messages
 */
export interface StreamState {
  isStreaming: boolean;
  currentMessage: StreamingMessage | null;
  sessionId: string | null;
  error: string | null;
}
/* ====================== Admin ========================== */

export interface Organization {
  id: string;
  name: string;
  websiteUrl: string;
  industry: string;
  createdBy: string;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  is_primary: boolean;
  is_admin: boolean;
  logo?: string;
}

export interface OrganizationDetailsResponse {
  name: string;
  logo: string | null;
  websiteUrl: string | null;
  industry: string | null;
  id: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  mcpKey: string | null;
  isKeyRevoked: boolean | null;
  isAdmin: boolean;
}

export interface DepartmentList {
  name: string;
  description: string;
  parent_department_id: string | null;
  visibility: string;
  id: string;
  organisationId: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  memberCount: number;
  agentCount: number;
}

export interface AgentCapabilities {
  capabilities: any | null;
  input_modes: any | null;
  output_modes: any | null;
  response_model: any | null;
  id: string;
  created_at: string;
  updated_at: string;
}

export interface Employee {
  id: string;
  name: string;
  description: string;
  avatar: string;
  owner_id: string;
  user_ids: string[];
  owner_type: string;
  template_id: string | null;
  template_owner_id: string | null;
  is_imported: boolean;
  is_bench_employee: boolean;
  is_changes_marketplace: boolean;
  is_a2a: boolean;
  is_customizable: boolean;
  agent_category: string;
  system_message: string;
  model_provider: string;
  model_name: string | null;
  workflow_ids: string[];
  mcp_server_ids: string[];
  agent_topic_type: string;
  subscriptions: any | null;
  visibility: string;
  tags: any | null;
  status: string;
  department: string;
  organization_id: string | null;
  tone: string;
  files: string[];
  urls: string[];
  ruh_credentials: boolean;
  agent_capabilities: AgentCapabilities;
  example_prompts: any | null;
  category: string;
  created_at: string;
  updated_at: string;
  variables: any[];

  // Legacy fields for backward compatibility
  _id?: string;
  role?: string;
  capabilities?: string[];
  isActive?: boolean;
}

// ==========================================
// LLM Actions Interfaces
// ==========================================

/**
 * Request model for user prompt improvement
 */
export interface UserPromptImprovementRequest {
  original_prompt: string;
  mode: "ACT" | "ASK";
}

/**
 * Response model for user prompt improvement
 */
export interface UserPromptImprovementResponse {
  original_prompt: string;
  improved_prompt: string;
  mode: string;
}

// Tour/Guide System Interfaces
export interface TourStep {
  id: string;
  target: string; // CSS selector for the element to highlight
  title: string;
  content: string | React.ReactNode; // Support HTML content
  placement?:
    | "top"
    | "bottom"
    | "left"
    | "right"
    | "center"
    | "top-left"
    | "top-right"
    | "top-center"
    | "bottom-left"
    | "bottom-right"
    | "bottom-center"
    | "left-top"
    | "left-bottom"
    | "left-center"
    | "right-top"
    | "right-bottom"
    | "right-center";
  showSkip?: boolean;
  showPrevious?: boolean;
  showNext?: boolean;
  showFinish?: boolean;
  disableBeacon?: boolean;
  disableOverlay?: boolean;
  offset?: number;
  spotlightPadding?: number | SpotlightPadding; // Support individual padding for all sides
  tooltipOffset?: TooltipOffset; // Custom tooltip positioning
  onEnter?: () => void; // Callback when entering this step
  onExit?: () => void; // Callback when leaving this step
}

// New interfaces for enhanced customization
export interface SpotlightPadding {
  top?: number;
  right?: number;
  bottom?: number;
  left?: number;
}

export interface TooltipOffset {
  x?: number; // Horizontal offset
  y?: number; // Vertical offset
}

export interface TourConfig {
  steps: TourStep[];
  showProgress?: boolean;
  showSkipButton?: boolean;
  continuous?: boolean;
  disableOverlay?: boolean;
  disableScrolling?: boolean;
  hideBackButton?: boolean;
  hideCloseButton?: boolean;
  scrollToFirstStep?: boolean;
  spotlightClicks?: boolean;
  spotlightPadding?: number;
  styles?: {
    options?: {
      primaryColor?: string;
      backgroundColor?: string;
      textColor?: string;
      overlayColor?: string;
      spotlightShadow?: string;
      zIndex?: number;
    };
  };
}

export interface TourState {
  isActive: boolean;
  currentStep: number;
  steps: TourStep[];
  isLoading: boolean;
}

export interface TourCallbacks {
  onStart?: () => void;
  onComplete?: () => void;
  onSkip?: () => void;
  onNext?: (step: TourStep, index: number) => void;
  onPrevious?: (step: TourStep, index: number) => void;
  onBeforeNext?: (step: TourStep, index: number) => boolean;
  onBeforePrevious?: (step: TourStep, index: number) => boolean;
}
