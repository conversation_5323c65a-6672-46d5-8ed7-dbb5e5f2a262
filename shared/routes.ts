// Auth Routes
export const authRoute = `${process.env.NEXT_PUBLIC_AUTH_URL}?redirect_url=${process.env.NEXT_PUBLIC_APP_URL}`;
export const loginRoute = "/authenticate";
export const getAccessTokenRoute = "/auth/access-token";

// Platform Routes
export const dashboardRoute = "/";
export const globalChatRoute = "/chat";
export const employeeWindowRoute = "/employee";
export const chatRoute = "/chat";
export const employeesRoute = "/employees";
export const aiEmployeeRoute = "/ai-employee";
export const settingsRoute = "/admin-settings";
export const agentRoute = "/employees";
export const createEmployeeRoute = "/employees/create";
export const userSettingsRoute = "/user-settings";
export const redirectionAfterCreateEmployee = "/employees";
export const employeeChatRoute = "/employees/chat";
