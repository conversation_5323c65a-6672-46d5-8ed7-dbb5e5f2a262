import { create } from "zustand";
import { persist, createJSONStorage } from "zustand/middleware";
import { OrganizationDetailsResponse } from "@/shared/interfaces";

// Define the state structure
interface OrgState {
  currentOrganization: OrganizationDetailsResponse | null;
  isLoadingOrganization: boolean;
}

// Define the actions available on the store
interface OrgActions {
  setCurrentOrganization: (orgData: OrganizationDetailsResponse | null) => void;
  clearOrganization: () => void;
  setIsLoadingOrganization: (loading: boolean) => void;
}

// Create Zustand store
export const useOrgStore = create<OrgState & OrgActions>()(
  persist(
    (set, get) => ({
      // Initial state
      currentOrganization: null,
      isLoadingOrganization: true,

      // Set current organization details
      setCurrentOrganization: (orgData) => {
        set({
          currentOrganization: orgData,
          isLoadingOrganization: false, // Set loading to false when current org is updated
        });
      },

      // Clear organization
      clearOrganization: () => {
        set({
          currentOrganization: null,
          isLoadingOrganization: false,
        });
      },

      // Set loading state
      setIsLoadingOrganization: (loading) =>
        set({ isLoadingOrganization: loading }),
    }),
    {
      name: "organization-session-storage",
      storage: createJSONStorage(() => localStorage),
    }
  )
);
