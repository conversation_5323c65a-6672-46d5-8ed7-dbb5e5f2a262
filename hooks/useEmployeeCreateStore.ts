import { CreateEmployeeProfileSchema } from "@/lib/schemas/createEmployee";
import {
  EmployeeTone,
  EmployeeVisibility,
  OpenAIModelName,
} from "@/shared/enums";
import { ModelProvider } from "@/shared/enums";
import { EmployeeDepartment } from "@/shared/enums";
import { create } from "zustand";

interface EmployeeCreateState {
  openModal: () => void;
  closeModal: () => void;
  isModalOpen: boolean;
  openPublishModal: () => void;
  closePublishModal: () => void;
  isPublishModalOpen: boolean;
  data: CreateEmployeeProfileSchema;
  setData: (data: Partial<CreateEmployeeProfileSchema>) => void;
  formStep: number;
  checkRequiredField: boolean | null;
  setCheckRequiredField: (data: boolean | null) => void;
  setFormStep: (step: number) => void;
  reset: () => void;
  pendingSaveStep: number | null;
  setPendingSaveStep: (step: number | null) => void;
  originForCreateAndEdit: string;
  setOriginForCreateAndEdit: (step: string) => void;
}

// Define initial state
const initialState: Pick<
  EmployeeCreateState,
  | "isModalOpen"
  | "data"
  | "formStep"
  | "isPublishModalOpen"
  | "pendingSaveStep"
  | "checkRequiredField"
  | "originForCreateAndEdit"
> = {
  isModalOpen: false,
  isPublishModalOpen: false,
  data: {
    name: "",
    agent_topic_type: "",
    description: "",
    avatar: "",
    // department: EmployeeDepartment.ENGINEERING,
    department: "",
    category: EmployeeDepartment.ENGINEERING,
    ruh_credentials: false,
    visibility: EmployeeVisibility.PUBLIC,
    model_provider: "",
    model_name: "",
    system_message: "",
    tone: EmployeeTone.FRIENDLY,
    files: [],
    urls: [],
    mcp_server_ids: [],
    workflow_ids: [],
    variables: [],
    agent_capabilities: {
      capabilities: [],
    },
  },
  formStep: 1,
  checkRequiredField: false,
  pendingSaveStep: null,
  originForCreateAndEdit: "",
};

export const useEmployeeCreateStore = create<EmployeeCreateState>((set) => ({
  ...initialState,
  openModal: () => set({ isModalOpen: true }),
  closeModal: () => set({ isModalOpen: false }),
  openPublishModal: () => set({ isPublishModalOpen: true }),
  closePublishModal: () => set({ isPublishModalOpen: false }),
  setData: (newData) =>
    set((state) => ({
      ...state,
      data: { ...state.data, ...newData },
    })),
  setFormStep: (step) => set({ formStep: step }),
  setOriginForCreateAndEdit: (data: string) =>
    set({ originForCreateAndEdit: data }),
  reset: () => set(initialState),
  setPendingSaveStep: (step) => set({ pendingSaveStep: step }),
  setCheckRequiredField: (data) => set({ checkRequiredField: data }),
}));
