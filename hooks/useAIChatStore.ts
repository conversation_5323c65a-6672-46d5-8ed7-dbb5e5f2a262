import { AgentBase, Employee } from "@/shared/interfaces";
import { create } from "zustand";

export interface Message {
  id: string;
  content: string;
  type: "user" | "ai" | "system";
  timestamp: Date;
  sources?: string[];
  metadata?: Record<string, any>;
}

export interface Source {
  id: string;
  name: string;
  type: "ME" | "GoogleDrive" | "Jira" | "Website";
  isActive: boolean;
  icon?: string;
  url?: string;
}

export interface ConnectedApp {
  id: string;
  name: string;
  icon: string;
  isConnected: boolean;
  permissions: string[];
}

export interface ChatMode {
  type: "ask-ai" | "check-info";
  label: string;
}

interface AIChatState {
  // Messages
  messages: Message[];
  currentConversationId: string | null;
  isTyping: boolean;

  // UI State
  chatMode: ChatMode;
  showWelcomeScreen: boolean;
  sidebarOpen: boolean;

  // Sources
  sources: Source[];
  activeSources: string[];

  // Employees
  employees: Employee[];
  selectedEmployee: string | null;

  // Connected Apps
  connectedApps: ConnectedApp[];

  // User Preferences
  preferences: {
    autoApprove: boolean;
    notifications: boolean;
    theme: "light" | "dark" | "system";
  };
}

interface AIChatActions {
  addMessage: (message: Omit<Message, "id" | "timestamp">) => void;
  clearMessages: () => void;
  setTyping: (isTyping: boolean) => void;

  setChatMode: (mode: ChatMode) => void;
  setShowWelcomeScreen: (show: boolean) => void;
  setSidebarOpen: (open: boolean) => void;

  addSource: (source: Omit<Source, "id">) => void;
  toggleSource: (sourceId: string) => void;
  updateSourceStatus: (sourceId: string, isActive: boolean) => void;

  addEmployee: (employee: Omit<AgentBase, "id">) => void;
  selectEmployee: (employeeId: string | null) => void;
  updateEmployee: (employeeId: string, updates: Partial<Employee>) => void;

  updateAppConnection: (appId: string, isConnected: boolean) => void;
  updateAppPermissions: (appId: string, permissions: string[]) => void;

  updatePreferences: (preferences: Partial<AIChatState["preferences"]>) => void;

  startNewConversation: () => void;
  loadConversation: (conversationId: string) => void;

  resetStore: () => void;
}

type AIChatStore = AIChatState & AIChatActions;

const defaultSources: Source[] = [
  {
    id: "me",
    name: "ME",
    type: "ME",
    isActive: true,
  },
  {
    id: "google-drive",
    name: "Google Drive",
    type: "GoogleDrive",
    isActive: false,
  },
  {
    id: "jira",
    name: "Jira",
    type: "Jira",
    isActive: false,
  },
];

const defaultEmployees: Employee[] = [];

const defaultConnectedApps: ConnectedApp[] = [
  {
    id: "company-help-center",
    name: "Company Help Center",
    icon: "📚",
    isConnected: true,
    permissions: ["read"],
  },
];

export const useAIChatStore = create<AIChatStore>((set, get) => ({
  messages: [],
  currentConversationId: null,
  isTyping: false,

  chatMode: { type: "ask-ai", label: "Ask AI" },
  showWelcomeScreen: true,
  sidebarOpen: false,

  sources: defaultSources,
  activeSources: ["me"],

  employees: [],
  selectedEmployee: null,

  connectedApps: defaultConnectedApps,

  preferences: {
    autoApprove: false,
    notifications: true,
    theme: "system",
  },

  addMessage: (message) => {
    const newMessage: Message = {
      ...message,
      id: crypto.randomUUID(),
      timestamp: new Date(),
    };

    set((state) => ({
      messages: [...state.messages, newMessage],
      showWelcomeScreen: false,
    }));
  },

  clearMessages: () => {
    set({ messages: [], showWelcomeScreen: true });
  },

  setTyping: (isTyping) => {
    set({ isTyping });
  },

  setChatMode: (mode) => {
    set({ chatMode: mode });
  },

  setShowWelcomeScreen: (show) => {
    set({ showWelcomeScreen: show });
  },

  setSidebarOpen: (open) => {
    set({ sidebarOpen: open });
  },

  addSource: (source) => {
    const newSource: Source = {
      ...source,
      id: crypto.randomUUID(),
    };
    set((state) => ({
      sources: [...state.sources, newSource],
    }));
  },

  toggleSource: (sourceId) => {
    set((state) => ({
      activeSources: state.activeSources.includes(sourceId)
        ? state.activeSources.filter((id) => id !== sourceId)
        : [...state.activeSources, sourceId],
    }));
  },

  updateSourceStatus: (sourceId, isActive) => {
    set((state) => ({
      sources: state.sources.map((source) =>
        source.id === sourceId ? { ...source, isActive } : source
      ),
    }));
  },

  addEmployee: (employee) => {
    const newEmployee: any = employee;
    set((state) => ({
      employees: [...state.employees, ...newEmployee],
    }));
  },

  selectEmployee: (employeeId) => {
    set({ selectedEmployee: employeeId });
  },

  updateEmployee: (employeeId, updates) => {
    set((state) => ({
      employees: state.employees.map((employee) =>
        employee._id === employeeId ? { ...employee, ...updates } : employee
      ),
    }));
  },

  updateAppConnection: (appId, isConnected) => {
    set((state) => ({
      connectedApps: state.connectedApps.map((app) =>
        app.id === appId ? { ...app, isConnected } : app
      ),
    }));
  },

  updateAppPermissions: (appId, permissions) => {
    set((state) => ({
      connectedApps: state.connectedApps.map((app) =>
        app.id === appId ? { ...app, permissions } : app
      ),
    }));
  },

  updatePreferences: (preferences) => {
    set((state) => ({
      preferences: { ...state.preferences, ...preferences },
    }));
  },

  startNewConversation: () => {
    set({
      currentConversationId: crypto.randomUUID(),
      messages: [],
      showWelcomeScreen: true,
    });
  },

  loadConversation: (conversationId) => {
    set({
      currentConversationId: conversationId,
      showWelcomeScreen: false,
    });
  },

  resetStore: () => {
    set(() => ({
      messages: [],
      currentConversationId: null,
      isTyping: false,

      chatMode: { type: "ask-ai", label: "Ask AI" },
      showWelcomeScreen: true,
      sidebarOpen: false,

      sources: defaultSources,
      activeSources: ["me"],

      employees: defaultEmployees,
      selectedEmployee: null,

      connectedApps: defaultConnectedApps,

      preferences: {
        autoApprove: false,
        notifications: true,
        theme: "system",
      },
    }));
  },
}));
