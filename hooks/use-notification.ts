// This hook will be used to globally store and manage notification state

import { create } from "zustand";

// Define the notification state interface
interface NotificationState {
  isNotificationOpen: boolean;
  hasUnread: boolean;
  setHasUnread: (value: boolean) => void;
  toggleNotification: () => void;
  openNotification: () => void;
  closeNotification: () => void;
}

// Create the notification store
export const useNotificationStore = create<NotificationState>((set) => ({
  isNotificationOpen: false,
  hasUnread: false,
  setHasUnread: (value: boolean) =>
    set((state) => (state.hasUnread === value ? state : { hasUnread: value })),
  toggleNotification: () =>
    set((state) => ({
      isNotificationOpen: !state.isNotificationOpen,
      hasUnread: false,
    })),
  openNotification: () => set({ isNotificationOpen: true }),
  closeNotification: () => set({ isNotificationOpen: false }),
}));
