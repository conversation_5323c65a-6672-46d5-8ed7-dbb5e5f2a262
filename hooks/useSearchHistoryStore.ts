import { create } from "zustand";

interface SearchHistoryState {
  isSearchHistoryOpen: boolean;
  openSearchHistory: () => void;
  closeSearchHistory: () => void;
  toggleSearchHistory: () => void;
}

export const useSearchHistoryStore = create<SearchHistoryState>((set) => ({
  isSearchHistoryOpen: false,
  openSearchHistory: () => set({ isSearchHistoryOpen: true }),
  closeSearchHistory: () => set({ isSearchHistoryOpen: false }),
  toggleSearchHistory: () =>
    set((state) => ({ isSearchHistoryOpen: !state.isSearchHistoryOpen })),
}));
