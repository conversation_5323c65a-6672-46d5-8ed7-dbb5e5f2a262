"use client";
import { useQuery } from "@tanstack/react-query";
import { subscriptionApi } from "@/app/api/subscription";
import { useOrgStore } from "./use-organization";

export const useSubscription = () => {
  const { currentOrganization } = useOrgStore();
  const {
    data: subscription,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["subscription", currentOrganization?.id],
    queryFn: () =>
      subscriptionApi.getSubscriptionDetails(currentOrganization!.id),
    enabled: !!currentOrganization,
    select: (data) => {
      if (data?.detail) {
        return null;
      }
      return data.subscription;
    },
  });

  return {
    subscription,
    isLoading,
    error,
  };
};