import { create } from "zustand";

interface PricingModalState {
  isOpen: boolean;
  defaultTab?: string; // Add defaultTab to control which tab to show
  openModal: (defaultTab?: string) => void;
  closeModal: () => void;
}

export const usePricingModalStore = create<PricingModalState>((set) => ({
  isOpen: false,
  defaultTab: undefined,
  openModal: (defaultTab?: string) =>
    set({
      isOpen: true,
      defaultTab,
    }),
  closeModal: () =>
    set({
      isOpen: false,
      defaultTab: undefined,
    }),
}));
