import { useEffect } from "react";
import {
  getMessaging,
  onMessage,
  getToken,
  isSupported,
} from "firebase/messaging";
import { initializeApp, getApps } from "firebase/app";
import { firebaseConfig } from "../shared/firebaseConfig";

export function useFirebaseMessaging(
  onForegroundMessage: (payload: any) => void
) {
  useEffect(() => {
    let unsubscribe: (() => void) | undefined;

    async function setupMessaging() {
      // Check if Firebase Messaging is supported in this browser
      if (!(await isSupported())) {
        console.warn("Firebase Messaging is not supported in this browser.");
        return;
      }
      // Initialize Firebase app if not already initialized
      const app = getApps().length
        ? getApps()[0]
        : initializeApp(firebaseConfig);
      const messaging = getMessaging(app);
      // Listen for foreground messages
      unsubscribe = onMessage(messaging, (payload) => {
        onForegroundMessage(payload);
      });
    }

    setupMessaging();
    // Cleanup listener on unmount
    return () => {
      if (unsubscribe) unsubscribe();
    };
  }, [onForegroundMessage]);
}

/**
 * requestFirebaseNotificationPermission
 *
 * Requests browser notification permission and retrieves the FCM token.
 * Returns the token if permission is granted, otherwise null.
 *
 * @returns {Promise<string | null>} FCM token or null
 */
export async function requestFirebaseNotificationPermission(): Promise<
  string | null
> {
  try {
    // Check if Firebase Messaging is supported
    if (!(await isSupported())) {
      console.error("Firebase Messaging is not supported in this browser.");
      return null;
    }
    // Initialize Firebase app if not already initialized
    const app = getApps().length ? getApps()[0] : initializeApp(firebaseConfig);
    const messaging = getMessaging(app);
    // Request browser notification permission
    const permission = await Notification.requestPermission();
    if (permission === "granted") {
      // Get FCM token (requires VAPID key in .env)
      const token = await getToken(messaging, {
        vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
      });
      return token;
    } else {
      console.warn("Notification permission denied by user.");
      return null;
    }
  } catch (error) {
    console.error("Error getting FCM token:", error);
    return null;
  }
}
