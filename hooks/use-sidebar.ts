// This hook will be used to globally store and manage sidebar state
import { create } from "zustand";

// Define the sidebar state interface
interface SidebarState {
  isSecondaryBarOpen: boolean;
  isMobileSidebarOpen: boolean;
  toggleSecondarySidebar: () => void;
  openSecondarySidebar: () => void;
  closeSecondarySidebar: () => void;
  toggleMobileSidebar: () => void;
  openMobileSidebar: () => void;
  closeMobileSidebar: () => void;
}

// Create the sidebar store
export const useSidebarStore = create<SidebarState>((set) => ({
  isSecondaryBarOpen: true,
  isMobileSidebarOpen: false,
  toggleSecondarySidebar: () => set((state) => ({
    isSecondaryBarOpen: !state.isSecondaryBarOpen
  })),
  openSecondarySidebar: () => set({ isSecondaryBarOpen: true }),
  closeSecondarySidebar: () => set({ isSecondaryBarOpen: false }),
  toggleMobileSidebar: () => set((state) => ({
    isMobileSidebarOpen: !state.isMobileSidebarOpen
  })),
  openMobileSidebar: () => set({ isMobileSidebarOpen: true }),
  closeMobileSidebar: () => set({ isMobileSidebarOpen: false }),
}));