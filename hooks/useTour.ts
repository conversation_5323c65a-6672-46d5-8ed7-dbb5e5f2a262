import { useState, useCallback, useEffect, useRef } from "react";
import { TourStep, TourState, TourCallbacks } from "@/shared/interfaces";

export const useTour = (
  initialSteps: TourStep[] = [],
  callbacks?: TourCallbacks
) => {
  const [tourState, setTourState] = useState<TourState>({
    isActive: false,
    currentStep: 0,
    steps: initialSteps,
    isLoading: false,
  });

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const startTour = useCallback(() => {
    if (tourState.steps.length === 0) return;

    setTourState((prev) => ({
      ...prev,
      isActive: true,
      currentStep: 0,
    }));

    // Call onEnter for the first step
    const firstStep = tourState.steps[0];
    firstStep?.onEnter?.();

    callbacks?.onStart?.();
  }, [tourState.steps, callbacks]);

  const endTour = useCallback(() => {
    setTourState((prev) => ({
      ...prev,
      isActive: false,
      currentStep: 0,
    }));

    callbacks?.onComplete?.();
  }, [callbacks]);

  const skipTour = useCallback(() => {
    setTourState((prev) => ({
      ...prev,
      isActive: false,
      currentStep: 0,
    }));

    callbacks?.onSkip?.();
  }, [callbacks]);

  const nextStep = useCallback(() => {
    const currentStepData = tourState.steps[tourState.currentStep];

    // Call onExit for current step
    currentStepData?.onExit?.();

    // Check if we can proceed to next step
    if (
      callbacks?.onBeforeNext?.(currentStepData, tourState.currentStep) ===
      false
    ) {
      return;
    }

    if (tourState.currentStep < tourState.steps.length - 1) {
      setTourState((prev) => ({
        ...prev,
        currentStep: prev.currentStep + 1,
      }));

      // Call onEnter for next step
      const nextStepData = tourState.steps[tourState.currentStep + 1];
      nextStepData?.onEnter?.();

      callbacks?.onNext?.(currentStepData, tourState.currentStep);
    } else {
      endTour();
    }
  }, [tourState.currentStep, tourState.steps, callbacks, endTour]);

  const previousStep = useCallback(() => {
    const currentStepData = tourState.steps[tourState.currentStep];

    // Call onExit for current step
    currentStepData?.onExit?.();

    // Check if we can go to previous step
    if (
      callbacks?.onBeforePrevious?.(currentStepData, tourState.currentStep) ===
      false
    ) {
      return;
    }

    if (tourState.currentStep > 0) {
      setTourState((prev) => ({
        ...prev,
        currentStep: prev.currentStep - 1,
      }));

      // Call onEnter for previous step
      const prevStepData = tourState.steps[tourState.currentStep - 1];
      prevStepData?.onEnter?.();

      callbacks?.onPrevious?.(currentStepData, tourState.currentStep);
    }
  }, [tourState.currentStep, tourState.steps, callbacks]);

  const goToStep = useCallback(
    (stepIndex: number) => {
      if (stepIndex >= 0 && stepIndex < tourState.steps.length) {
        setTourState((prev) => ({
          ...prev,
          currentStep: stepIndex,
        }));
      }
    },
    [tourState.steps.length]
  );

  const updateSteps = useCallback((newSteps: TourStep[]) => {
    setTourState((prev) => ({
      ...prev,
      steps: newSteps,
      currentStep: 0,
    }));
  }, []);

  const scrollToElement = useCallback((selector: string) => {
    const element = document.querySelector(selector);
    if (element) {
      element.scrollIntoView({
        behavior: "smooth",
        block: "center",
        inline: "center",
      });
    }
  }, []);

  // Auto-scroll to current step element
  useEffect(() => {
    if (tourState.isActive && tourState.steps[tourState.currentStep]) {
      const currentStep = tourState.steps[tourState.currentStep];

      // Clear any existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Delay scroll to allow DOM updates
      timeoutRef.current = setTimeout(() => {
        scrollToElement(currentStep.target);
      }, 100);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [
    tourState.isActive,
    tourState.currentStep,
    tourState.steps,
    scrollToElement,
  ]);

  const currentStepData = tourState.steps[tourState.currentStep];
  const isFirstStep = tourState.currentStep === 0;
  const isLastStep = tourState.currentStep === tourState.steps.length - 1;

  return {
    ...tourState,
    currentStepData,
    isFirstStep,
    isLastStep,
    startTour,
    endTour,
    skipTour,
    nextStep,
    previousStep,
    goToStep,
    updateSteps,
    scrollToElement,
  };
};
