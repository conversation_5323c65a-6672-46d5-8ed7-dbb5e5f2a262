"use client";

import { useState, useRef } from "react";
import { gcsApi, uploadToGCS } from "@/app/api/gcs";
import { toast } from "sonner";

export type MultiFileType = "image" | "document" | "audio" | "video" | "any";

export interface MultiFileUploadOptions {
  onSuccess?: (files: UploadedFile[]) => void;
  onError?: (error: Error) => void;
  filePath?: string;
  acceptedFormats?: string;
  maxSizeMB?: number;
  maxCount?: number;
  customSuccessMessage?: string;
  customErrorMessage?: string;
  onUploadStateChange?: (isUploading: boolean) => void;
}

export interface UploadedFile {
  id: string;
  name: string;
  type: string;
  size: number;
  url: string;
  status: "uploaded" | "uploading" | "error";
  error?: string;
}

export const useMultiFileUpload = (options?: MultiFileUploadOptions) => {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Default options
  const {
    onSuccess,
    onError,
    filePath = "uploads",
    acceptedFormats,
    maxSizeMB = 1, // 1MB default
    maxCount = 5,
    customSuccessMessage = "Files uploaded successfully",
    customErrorMessage,
    onUploadStateChange,
  } = options || {};

  const getAcceptValue = () => {
    return acceptedFormats;
  };

  const validateFile = (file: File): { valid: boolean; message?: string } => {
    const fileSizeMB = file.size / (1024 * 1024);
    const acceptedFormats = getAcceptValue();
    const fileType = file.name.split(".").pop();
    if (fileSizeMB > maxSizeMB) {
      return {
        valid: false,
        message: `${file.name} is too large. Maximum size is ${maxSizeMB}MB`,
      };
    }
    if (acceptedFormats) {
      if (!acceptedFormats.includes(fileType || "")) {
        return {
          valid: false,
          message: `${file.name} is not a valid file type`,
        };
      }
    }

    return { valid: true };
  };

  const triggerFileSelect = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const uploadFiles = async (files: File[]): Promise<UploadedFile[]> => {
    setIsUploading(true);
    if (onUploadStateChange) onUploadStateChange(true);
    const uploaded: UploadedFile[] = [];
    try {
      // Accumulate files, prevent duplicates, enforce maxCount
      setUploadedFiles((prev) => {
        // Remove files that are uploading (in case of retry)
        const existing = prev.filter((f) => f.status === "uploaded");
        // Filter out duplicates (same name and size)
        const newFiles = files.filter(
          (file) =>
            !existing.some((f) => f.name === file.name && f.size === file.size)
        );
        // Enforce maxCount
        let allowedFiles = newFiles;
        if (existing.length + newFiles.length > maxCount) {
          allowedFiles = newFiles.slice(0, maxCount - existing.length);
          toast.error(`You can upload a maximum of ${maxCount} files.`);
        }
        // Add uploading placeholders for allowedFiles
        return [
          ...existing,
          ...allowedFiles.map((file) => ({
            id: crypto.randomUUID(),
            name: file.name,
            type: file.type,
            size: file.size,
            url: "",
            status: "uploading" as const,
          })),
        ];
      });
      // Actually upload allowedFiles
      const prevFiles = uploadedFiles.filter((f) => f.status === "uploaded");
      const existing = prevFiles;
      const newFiles = files.filter(
        (file) =>
          !existing.some((f) => f.name === file.name && f.size === file.size)
      );
      let allowedFiles = newFiles;
      if (existing.length + newFiles.length > maxCount) {
        allowedFiles = newFiles.slice(0, maxCount - existing.length);
      }
      await Promise.all(
        allowedFiles.map(async (file) => {
          const id = crypto.randomUUID();
          try {
            const timestamp = new Date().getTime();
            const uniqueFileName = `${timestamp}-${file.name}`;
            const presignedUrlResponse = await gcsApi.getPresignedUrl(
              uniqueFileName,
              file.type,
              filePath
            );
            if (!presignedUrlResponse.url)
              throw new Error("No presigned URL received from server");
            const publicUrl = await uploadToGCS(presignedUrlResponse.url, file);
            setUploadedFiles((prev) =>
              prev.map((f) =>
                f.name === file.name &&
                f.size === file.size &&
                f.status === "uploading"
                  ? { ...f, url: publicUrl, status: "uploaded" }
                  : f
              )
            );
            uploaded.push({
              id,
              name: file.name,
              type: file.type,
              size: file.size,
              url: publicUrl,
              status: "uploaded",
            });
          } catch (error: any) {
            setUploadedFiles((prev) =>
              prev.map((f) =>
                f.name === file.name &&
                f.size === file.size &&
                f.status === "uploading"
                  ? { ...f, status: "error", error: error.message }
                  : f
              )
            );
            uploaded.push({
              id,
              name: file.name,
              type: file.type,
              size: file.size,
              url: "",
              status: "error",
              error: error.message,
            });
          }
        })
      );
      if (onSuccess) onSuccess(uploaded.filter((f) => f.status === "uploaded"));
      // toast.success(customSuccessMessage);
    } catch (error: any) {
      if (onError) onError(error);
      toast.error(
        customErrorMessage || error.message || "Failed to upload files"
      );
    } finally {
      setIsUploading(false);
      if (onUploadStateChange) onUploadStateChange(false);
    }
    return uploaded;
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const files = Array.from(event.target.files || []);
    if (files.length === 0) return;
    // Validate all files, keep only valid ones
    const validFiles: File[] = [];
    files.forEach((file) => {
      const validation = validateFile(file);
      if (!validation.valid) {
        toast.error(validation.message || `Invalid file: ${file.name}`);
      } else {
        validFiles.push(file);
      }
    });
    if (validFiles.length === 0) return;
    await uploadFiles(validFiles);
    if (fileInputRef.current) fileInputRef.current.value = "";
  };

  const removeFile = (fileId: string) => {
    if (fileId === "ALL") {
      setUploadedFiles([]);
    } else {
      setUploadedFiles((prev) => prev.filter((f) => f.id !== fileId));
    }
  };

  return {
    fileInputRef,
    isUploading,
    uploadedFiles,
    triggerFileSelect,
    handleFileChange,
    removeFile,
    acceptValue: getAcceptValue(),
    uploadFiles,
    validateFile,
    maxCount,
  };
};
