import { create } from "zustand";

import { Employee, StreamingMessage, StreamState } from "@/shared/interfaces";
import { SenderType } from "@/shared/enums";
import { LIMIT } from "@/shared/constants";

export interface EmployeeChatAttachment {
  file_url: string;
  file_name: string;
  file_type: string;
  file_size: number;
}

export interface EmployeeChatMessage {
  id: string;
  content: string;
  senderType: SenderType;
  timestamp: Date;
  attachments?: EmployeeChatAttachment[];
  employeeSearchType?: string;
  searchSources?: any[];
  isError?: boolean;
  // Optional workflow properties
  type?: "workflow" | "delegation" | "create-employee";
  isWorkflowEvent?: boolean;
  workflowData?: any;
  // Optional delegation properties
  delegationData?: {
    employee: Employee;
    isApproved: boolean;
    isApproveLoading: boolean;
  };
}

export interface EmployeeChatSession {
  employeeId: string;
  conversationId: string | null;
  sessionId: string | null;
  sessionCreatedAt: Date | null;
  messages: EmployeeChatMessage[];
  isTyping: boolean;
  chatStarted: boolean;
  streamState: StreamState;
  delegationThinkingContent?: string | null;
  inputTokens: any | null;
  outputTokens: any | null;
  // Add pagination state
  pagination: {
    currentPage: number;
    totalPages: number;
    hasNextPage: boolean;
    isLoadingMore: boolean;
    total: number;
    pageSize: number;
    totalRawMessages: number;
  };
}

export interface WorkflowChatMessage extends EmployeeChatMessage {
  type: "workflow";
  isWorkflowEvent?: boolean;
  workflowData: any & {
    stepMessages?: {
      [transitionId: string]: {
        messages: { text: string; status: string }[];
        status: string;
      };
    };
  };
}

export interface DelegationChatMessage extends EmployeeChatMessage {
  type: "delegation";
  delegationData: {
    employee: Employee;
    isApproved: boolean;
    isApproveLoading: boolean;
    // New fields for task delegation success
    isTaskStarted?: boolean;
    taskData?: {
      task_id: string;
      agent_id: string;
      conversation_id: string;
      agent_session_id: string;
      title: string;
    };
  };
}

export interface CreateEmployeeChatMessage extends EmployeeChatMessage {
  type: "create-employee";
  createEmployeeData: {
    message: string;
    taskDescription?: string;
  };
}

function isWorkflowChatMessage(
  msg: EmployeeChatMessage | WorkflowChatMessage
): msg is WorkflowChatMessage {
  return (msg as WorkflowChatMessage).type === "workflow";
}

export function isDelegationChatMessage(
  msg: EmployeeChatMessage | DelegationChatMessage
): msg is DelegationChatMessage {
  return (msg as DelegationChatMessage).type === "delegation";
}

export function isCreateEmployeeChatMessage(
  msg: EmployeeChatMessage | CreateEmployeeChatMessage
): msg is CreateEmployeeChatMessage {
  return (msg as CreateEmployeeChatMessage).type === "create-employee";
}

interface EmployeeState {
  items: Employee[];
  selectedId: string | null;
  isLoading: boolean;
  error: string | null;
}

interface ChatState {
  sessions: Record<string, EmployeeChatSession>;
  currentStreamingMessage: StreamingMessage | null;
}

interface UIState {
  showEmployeeInfo: boolean;
}

interface EmployeeManagementState {
  employee: EmployeeState;
  chat: ChatState;
  ui: UIState;
  isWorkflowStarted: boolean;
  isSplitView: boolean;
  setIsSplitView: (value: boolean) => void;
  workflowStepResults: Record<string, any>;
  setWorkflowStepResult: (transitionId: string, result: any) => void;
  selectedStep: any | null;
  setSelectedStep: (step: any | null) => void;

  // MCP execution state
  mcpExecution: {
    isExecuting: boolean;
    toolName: string | null;
    logo?: string | null;
    description?: string | null;
  };

  // Knowledge fetch state
  knowledgeFetch: {
    isActive: boolean;
  };

  // Actions
  setEmployees: (employees: Employee[]) => void;
  setSelectedEmployeeId: (employeeId: string | null) => void;
  setLoadingEmployees: (loading: boolean) => void;
  setEmployeesError: (error: string | null) => void;

  addMessage: (message: Omit<EmployeeChatMessage, "id" | "timestamp">) => void;
  clearCurrentChat: () => void;
  setTyping: (isTyping: boolean) => void;

  setShowEmployeeInfo: (show: boolean) => void;

  startStreaming: (sessionId: string) => void;
  appendStreamingContent: (
    content: string,
    employeeSearchType: string,
    extra?: any
  ) => void;
  completeStreaming: () => void;
  setStreamError: (error: string | null, isTyping?: boolean) => void;
  resetStream: () => void;

  initializeChatSession: (employeeId: string) => void;
  reset: () => void;

  setSessionData: (
    employeeId: string,
    conversationId: string,
    sessionId: string
  ) => void;
  getSessionData: (employeeId: string) => {
    conversationId: string | null;
    sessionId: string | null;
  };
  resetSessionData: (employeeId: string) => void;
  isSessionExpired: (employeeId: string) => boolean;
  loadChatHistory: (
    employeeId: string,
    messages: EmployeeChatMessage[],
    pagination?: {
      currentPage: number;
      totalPages: number;
      hasNextPage: boolean;
      total: number;
      pageSize: number;
      totalRawMessages: number;
    }
  ) => void;
  setChatStarted: (employeeId: string, chatStarted: boolean) => void;
  setIsWorkflowStarted: (isWorkflowStarted: boolean) => void;

  addWorkflowMessage: (workflowData: any) => void;
  updateWorkflowStepStatus: (
    workflowMessageId: string,
    transitionId: string,
    status: string
  ) => void;

  // Delegation message actions
  addDelegationMessage: (employee: Employee) => void;
  updateDelegationApprovalStatus: (
    delegationMessageId: string,
    isApproved: boolean
  ) => void;
  setDelegationLoading: (
    delegationMessageId: string,
    isLoading: boolean
  ) => void;
  // New action for handling task delegation success
  setDelegationTaskStarted: (
    delegationMessageId: string,
    taskData: {
      task_id: string;
      agent_id: string;
      conversation_id: string;
      agent_session_id: string;
      title: string;
    }
  ) => void;

  // Create Employee message actions
  addCreateEmployeeMessage: (message: string, taskDescription?: string) => void;

  workflowMessageThinking: string;
  setWorkflowMessageThinking: (isThinking: string) => void;

  // Delegation thinking content actions
  setDelegationThinkingContent: (
    employeeId: string,
    content: string | null
  ) => void;
  getDelegationThinkingContent: (employeeId: string) => string | null;

  discoveredEmployees: (Employee | { notFound: true; message: string })[];
  addDiscoveredEmployee: (
    employee: Employee | { notFound: true; message: string }
  ) => void;
  removeDiscoveredEmployee: (employeeId: string) => void;
  clearDiscoveredEmployees: () => void;

  thinkingStatusText: string;
  setThinkingStatusText: (text: string) => void;
  setInputOutputTokens: (
    employeeId: string,
    inputTokens: any,
    outputTokens: any
  ) => void;

  // Add tokens from SSE events
  addTokensFromSSE: (
    employeeId: string,
    promptTokens: number,
    completionTokens: number
  ) => void;

  addWorkflowStepMessage: (
    workflowMessageId: string,
    transitionId: string,
    message: string,
    status: string
  ) => void;

  updateWorkflowApprovalStatus: (
    workflowMessageId: string,
    approvalRequired: boolean
  ) => void;

  updateStepApprovalRequired: (
    workflowMessageId: string,
    transitionId: string,
    approvalRequired: boolean
  ) => void;

  updateWorkflowStatus: (
    workflowMessageId: string,
    workflowStatus: string
  ) => void;

  removeWorkflowStepsWithoutOutput: () => void;

  setWorkflowCancelled: () => void;

  // MCP execution actions
  setMcpExecutionStarted: (
    toolName: string,
    logo?: string,
    description?: string
  ) => void;
  setMcpExecutionEnded: () => void;

  // Knowledge fetch actions
  setKnowledgeFetchStarted: () => void;
  setKnowledgeFetchEnded: () => void;

  // Load more messages actions
  setIsLoadingMoreMessages: (employeeId: string, isLoading: boolean) => void;
  loadMoreMessages: (
    employeeId: string,
    messages: EmployeeChatMessage[],
    metadata: {
      currentPage: number;
      totalPages: number;
      hasNextPage: boolean;
      total: number;
      pageSize: number;
      totalRawMessages: number;
    }
  ) => void;
}

const initialStreamState: StreamState = {
  isStreaming: false,
  currentMessage: null,
  sessionId: null,
  error: null,
};

const initialState = {
  employee: {
    items: [],
    selectedId: null,
    isLoading: false,
    error: null,
  },
  chat: {
    sessions: {},
    currentStreamingMessage: null,
  },
  ui: {
    showEmployeeInfo: true,
  },
  isWorkflowStarted: false,
  workflowMessageThinking: "",
  isSplitView: false,
  workflowStepResults: {},
  selectedStep: null,
  discoveredEmployees: [],
  mcpExecution: {
    isExecuting: false,
    toolName: null,
  },
  knowledgeFetch: {
    isActive: false,
  },
};

type AddMessageInput = Omit<EmployeeChatMessage, "id" | "timestamp">;

export const useEmployeeManagementStore = create<EmployeeManagementState>(
  (set, get) => ({
    ...initialState,
    thinkingStatusText: "",
    setThinkingStatusText: (text) => set({ thinkingStatusText: text }),

    setEmployees: (employees) =>
      set((state) => ({
        employee: { ...state.employee, items: employees },
      })),

    setSelectedEmployeeId: (employeeId) => {
      const { employee } = get();
      if (employee.selectedId !== employeeId) {
        set((state) => ({
          employee: { ...state.employee, selectedId: employeeId },
          ui: {
            ...state.ui,
            showEmployeeInfo:
              !state.chat.sessions[employeeId!]?.chatStarted ||
              state.chat.sessions[employeeId!]?.messages.length === 0,
          },
        }));
      }
    },
    setLoadingEmployees: (loading) =>
      set((state) => ({ employee: { ...state.employee, isLoading: loading } })),
    setEmployeesError: (error) =>
      set((state) => ({ employee: { ...state.employee, error } })),

    addMessage: (message) => {
      const { employee, chat } = get();
      const { selectedId } = employee;
      if (!selectedId) return;

      const newMessage: EmployeeChatMessage = {
        ...message,
        id: crypto.randomUUID(),
        timestamp: new Date(),
      };

      set((state) => {
        const session = state.chat.sessions[selectedId] || {};
        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...session,
                messages: [...(session.messages || []), newMessage],
                chatStarted: true,
                isTyping: true,
                pagination: {
                  ...session.pagination,
                  totalRawMessages: session.pagination.totalRawMessages + 1,
                },
                streamState: {
                  ...state.chat.sessions[selectedId].streamState,
                  error: null,
                },
              },
            },
          },
          ui: { ...state.ui, showEmployeeInfo: false },
        };
      });
    },

    clearCurrentChat: () => {
      const { selectedId } = get().employee;
      if (!selectedId) return;

      set((state) => {
        const session = state.chat.sessions[selectedId] || {};
        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...session,
                messages: [],
                chatStarted: false,
                isTyping: false,
              },
            },
          },
          ui: { ...state.ui, showEmployeeInfo: true },
        };
      });
    },

    setTyping: (isTyping) => {
      const { selectedId } = get().employee;
      if (!selectedId) return;
      set((state) => ({
        chat: {
          ...state.chat,
          sessions: {
            ...state.chat.sessions,
            [selectedId]: {
              ...state.chat.sessions[selectedId],
              isTyping,
            },
          },
        },
      }));
    },

    setShowEmployeeInfo: (show) =>
      set((state) => ({ ui: { ...state.ui, showEmployeeInfo: show } })),

    startStreaming: (sessionId) => {
      const { selectedId } = get().employee;
      if (!selectedId) return;

      const streamingMessage: StreamingMessage = {
        id: crypto.randomUUID(),
        content: "",
        isComplete: false,
        source: SenderType.ASSISTANT,
        timestamp: new Date(),
      };

      set((state) => ({
        chat: {
          ...state.chat,
          currentStreamingMessage: streamingMessage,
          sessions: {
            ...state.chat.sessions,
            [selectedId]: {
              ...state.chat.sessions[selectedId],
              isTyping: true,
              streamState: {
                isStreaming: true,
                currentMessage: streamingMessage,
                sessionId,
                error: null,
              },
            },
          },
        },
      }));
    },

    appendStreamingContent: (content, employeeSearchType, extra = {}) => {
      const { selectedId } = get().employee;
      if (!selectedId) return;

      set((state) => {
        const currentMessage = state.chat.currentStreamingMessage;
        if (!currentMessage) return state;

        let updatedMessage: StreamingMessage = {
          ...currentMessage,
          content: currentMessage.content + content,
        };

        if (extra) {
          updatedMessage = {
            ...updatedMessage,
            ...extra,
          };
        }

        return {
          chat: {
            ...state.chat,
            currentStreamingMessage: updatedMessage,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...state.chat.sessions[selectedId],
                streamState: {
                  ...state.chat.sessions[selectedId].streamState,
                  currentMessage: updatedMessage,
                },
              },
            },
          },
        };
      });
    },

    completeStreaming: () => {
      const { employee, chat } = get();
      const { selectedId } = employee;
      const { currentStreamingMessage } = chat;

      if (!selectedId || !currentStreamingMessage) return;

      if (currentStreamingMessage.content.trim()) {
        const completedMessage: EmployeeChatMessage = {
          id: currentStreamingMessage.id,
          content: currentStreamingMessage.content,
          timestamp: currentStreamingMessage.timestamp,
          senderType: SenderType.ASSISTANT,
          employeeSearchType:
            currentStreamingMessage.employeeSearchType as string,
          searchSources: currentStreamingMessage.searchSources,
        };

        set((state) => ({
          chat: {
            ...state.chat,
            currentStreamingMessage: null,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...state.chat.sessions[selectedId],
                messages: [
                  ...state.chat.sessions[selectedId].messages,
                  completedMessage,
                ],
                isTyping: false,
                chatStarted: true,
                streamState: initialStreamState,
                pagination: {
                  ...state.chat.sessions[selectedId].pagination,
                  totalRawMessages:
                    state.chat.sessions[selectedId].pagination
                      .totalRawMessages + 1,
                },
              },
            },
          },
          ui: { ...state.ui, showEmployeeInfo: false },
        }));
      } else {
        get().resetStream();
      }
    },

    setStreamError: (error, isTyping = false) => {
      const { selectedId } = get().employee;
      if (!selectedId) return;
      set((state) => ({
        chat: {
          ...state.chat,
          sessions: {
            ...state.chat.sessions,
            [selectedId]: {
              ...state.chat.sessions[selectedId],
              isTyping: isTyping,
              streamState: {
                ...state.chat.sessions[selectedId].streamState,
                error,
                isStreaming: false,
              },
            },
          },
        },
      }));
    },

    resetStream: () => {
      const { selectedId } = get().employee;
      if (!selectedId) return;
      set((state) => ({
        chat: {
          ...state.chat,
          currentStreamingMessage: null,
          sessions: {
            ...state.chat.sessions,
            [selectedId]: {
              ...state.chat.sessions[selectedId],
              isTyping: false,
              streamState: initialStreamState,
            },
          },
        },
      }));
    },

    initializeChatSession: (employeeId) => {
      if (!employeeId) return;
      const { chat } = get();
      if (!chat.sessions[employeeId]) {
        set((state) => ({
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [employeeId]: {
                employeeId,
                conversationId: null,
                sessionId: null,
                sessionCreatedAt: null,
                messages: [],
                isTyping: false,
                chatStarted: false,
                streamState: initialStreamState,
                delegationThinkingContent: null,
                inputTokens: null,
                outputTokens: null,
                pagination: {
                  currentPage: 1,
                  totalPages: 1,
                  hasNextPage: false,
                  isLoadingMore: false,
                  total: 0,
                  pageSize: LIMIT,
                  totalRawMessages: 0,
                },
              },
            },
          },
        }));
      }
    },

    // Session management
    setSessionData: (employeeId, conversationId, sessionId) => {
      set((state) => {
        const session = state.chat.sessions[employeeId] || {};
        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [employeeId]: {
                ...session,
                employeeId,
                conversationId,
                sessionId,
                sessionCreatedAt: new Date(),
                messages: session.messages || [],
                isTyping: session.isTyping || false,
                chatStarted: session.chatStarted || false,
                streamState: session.streamState || initialStreamState,
                delegationThinkingContent:
                  session.delegationThinkingContent || null,
                pagination: session.pagination || {
                  currentPage: 1,
                  totalPages: 1,
                  hasNextPage: false,
                  isLoadingMore: false,
                  total: 0,
                },
              },
            },
          },
        };
      });
    },

    resetSessionData: (employeeId) => {
      if (!employeeId) return;
      const { chat } = get();
      const session = chat.sessions[employeeId];
      if (!session) return;

      set((state) => ({
        chat: {
          ...state.chat,
          sessions: {
            ...state.chat.sessions,
            [employeeId]: {
              ...state.chat.sessions[employeeId],
              employeeId,
              conversationId: null,
              sessionId: null,
              sessionCreatedAt: null,
              messages: [],
              isTyping: false,
              chatStarted: false,
              streamState: initialStreamState,
              delegationThinkingContent: null,
              pagination: {
                currentPage: 1,
                totalPages: 1,
                hasNextPage: false,
                isLoadingMore: false,
                total: 0,
                pageSize: LIMIT,
                totalRawMessages: 0,
              },
            },
          },
        },
      }));
    },
    getSessionData: (employeeId) => {
      const { chat } = get();
      const session = chat.sessions[employeeId];
      return {
        conversationId: session?.conversationId || null,
        sessionId: session?.sessionId || null,
        messages: session?.messages || [],
      };
    },

    isSessionExpired: (employeeId) => {
      const { chat } = get();
      const session = chat.sessions[employeeId];
      if (!session?.sessionCreatedAt) return true;

      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
      return session.sessionCreatedAt < oneHourAgo;
    },

    // Chat history
    loadChatHistory: (employeeId, messages, pagination) => {
      set((state) => {
        const session = state.chat.sessions[employeeId] || {};
        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [employeeId]: {
                ...session,
                employeeId,
                messages,
                chatStarted: messages.length > 0,
                isTyping: session.isTyping || false,
                streamState: session.streamState || initialStreamState,
                conversationId: session.conversationId || null,
                sessionId: session.sessionId || null,
                sessionCreatedAt: session.sessionCreatedAt || null,
                pagination: pagination
                  ? {
                      currentPage: pagination.currentPage,
                      totalPages: pagination.totalPages,
                      hasNextPage: pagination.hasNextPage,
                      isLoadingMore: false,
                      total: pagination.total,
                      pageSize: pagination.pageSize,
                      totalRawMessages: pagination.totalRawMessages,
                    }
                  : {
                      currentPage: 1,
                      totalPages: 1,
                      hasNextPage: false,
                      isLoadingMore: false,
                      total: messages.length,
                      pageSize: LIMIT,
                      totalRawMessages: 0,
                    },
              },
            },
          },
        };
      });
    },

    setChatStarted: (employeeId, chatStarted) => {
      set((state) => ({
        chat: {
          ...state.chat,
          sessions: {
            ...state.chat.sessions,
            [employeeId]: { ...state.chat.sessions[employeeId], chatStarted },
          },
        },
      }));
    },

    reset: () => set(initialState),
    setIsWorkflowStarted: (isWorkflowStarted) =>
      set((state) => ({
        isWorkflowStarted: isWorkflowStarted,
      })),

    addWorkflowMessage: (workflowData) => {
      const { selectedId } = get().employee;
      if (!selectedId) return;
      // Initialize stepMessages for each available node
      const stepMessages: Record<
        string,
        { messages: string[]; status: string }
      > = {};
      if (workflowData.available_nodes) {
        (workflowData.available_nodes as any[]).forEach((node: any) => {
          stepMessages[node.transition_id] = {
            messages: [],
            status: "pending",
          };
        });
      }
      const newMessage: WorkflowChatMessage = {
        id: crypto.randomUUID(),
        type: "workflow",
        senderType: SenderType.ASSISTANT,
        workflowData: {
          ...workflowData,
          stepMessages,
          workflowStatus: workflowData.workflowStatus || "running", // Initialize with default status
        },
        timestamp: new Date(),
        content: "", // Not used for workflow messages
      };
      set((state) => {
        const session = state.chat.sessions[selectedId] || {};
        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...session,
                messages: [...(session.messages || []), newMessage],
                chatStarted: true,
                isTyping: false,
                pagination: {
                  ...session.pagination,
                  totalRawMessages: session.pagination.totalRawMessages + 1,
                },
              },
            },
          },
        };
      });
    },

    updateWorkflowStepStatus: (workflowMessageId, transitionId, status) => {
      const { selectedId } = get().employee;
      if (!selectedId) return;
      set((state) => {
        const session = state.chat.sessions[selectedId];
        if (!session) return state;
        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...session,
                messages: session.messages.map((msg) => {
                  if (isWorkflowChatMessage(msg)) {
                    if (msg.id === workflowMessageId) {
                      return {
                        ...msg,
                        workflowData: {
                          ...msg.workflowData,
                          stepStatus: {
                            ...msg.workflowData.stepStatus,
                            [transitionId]: status,
                          },
                        },
                      };
                    }
                  }

                  return msg;
                }),
              },
            },
          },
        };
      });
    },

    setWorkflowMessageThinking: (isThinking) => {
      set((state) => ({
        workflowMessageThinking: isThinking,
      }));
    },

    setIsSplitView: (value) => set(() => ({ isSplitView: value })),

    setWorkflowStepResult: (transitionId, result) =>
      set((state) => ({
        workflowStepResults: {
          ...state.workflowStepResults,
          [transitionId]: result,
        },
      })),

    setSelectedStep: (step) => set(() => ({ selectedStep: step })),

    setDelegationThinkingContent: (employeeId, content) => {
      set((state) => {
        const session = state.chat.sessions[employeeId];
        if (!session) return state;
        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [employeeId]: {
                ...session,
                delegationThinkingContent: content,
              },
            },
          },
        };
      });
    },

    getDelegationThinkingContent: (employeeId) => {
      const { chat } = get();
      return chat.sessions[employeeId]?.delegationThinkingContent || null;
    },
    addDiscoveredEmployee: (employee) =>
      set((state) => {
        if ("id" in employee) {
          if (
            state.discoveredEmployees.some(
              (e) => "id" in e && e.id === employee.id
            )
          ) {
            return {};
          }
        }
        return {
          discoveredEmployees: [...state.discoveredEmployees, employee],
        };
      }),
    removeDiscoveredEmployee: (employeeId) =>
      set((state) => ({
        discoveredEmployees: state.discoveredEmployees.filter(
          (e) => !("id" in e) || e.id !== employeeId
        ),
      })),
    clearDiscoveredEmployees: () => set({ discoveredEmployees: [] }),
    setInputOutputTokens: (employeeId, inputTokens, outputTokens) =>
      set((state) => ({
        chat: {
          ...state.chat,
          sessions: {
            ...state.chat.sessions,
            [employeeId]: {
              ...state.chat.sessions[employeeId],
              inputTokens,
              outputTokens,
            },
          },
        },
      })),

    addTokensFromSSE: (employeeId, promptTokens, completionTokens) =>
      set((state) => {
        const session = state.chat.sessions[employeeId];
        if (!session) return state;

        const currentInputTokens = session.inputTokens || 0;
        const currentOutputTokens = session.outputTokens || 0;

        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [employeeId]: {
                ...session,
                inputTokens: currentInputTokens + promptTokens,
                outputTokens: currentOutputTokens + completionTokens,
              },
            },
          },
        };
      }),

    addWorkflowStepMessage: (
      workflowMessageId,
      transitionId,
      message,
      status
    ) => {
      set((state) => {
        const { selectedId } = state.employee;
        if (!selectedId) return state;
        const session = state.chat.sessions[selectedId];
        if (!session) return state;
        const workflowMsgIdx = session.messages.findIndex(
          (msg) => msg.id === workflowMessageId
        );
        if (workflowMsgIdx === -1) return state;
        const workflowMsg = session.messages[workflowMsgIdx];
        if (!workflowMsg.workflowData.stepMessages[transitionId]) {
          workflowMsg.workflowData.stepMessages[transitionId] = {
            messages: [],
            status: "pending",
          };
        }
        workflowMsg.workflowData.stepMessages[transitionId].messages.push({
          text: message,
          status,
        });
        workflowMsg.workflowData.stepMessages[transitionId].status = status;
        // Return a shallow copy to trigger state update
        return { ...state };
      });
    },

    updateWorkflowApprovalStatus: (workflowMessageId, approvalRequired) => {
      set((state) => {
        const { selectedId } = state.employee;
        if (!selectedId) return state;
        const session = state.chat.sessions[selectedId];
        if (!session) return state;

        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...session,
                messages: session.messages.map((msg) => {
                  if (
                    isWorkflowChatMessage(msg) &&
                    msg.id === workflowMessageId
                  ) {
                    return {
                      ...msg,
                      workflowData: {
                        ...msg.workflowData,
                        approvalRequired,
                      },
                    };
                  }
                  return msg;
                }),
              },
            },
          },
        };
      });
    },

    updateStepApprovalRequired: (
      workflowMessageId,
      transitionId,
      approvalRequired
    ) => {
      set((state) => {
        const { selectedId } = state.employee;
        if (!selectedId) return state;
        const session = state.chat.sessions[selectedId];
        if (!session) return state;

        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...session,
                messages: session.messages.map((msg) => {
                  if (
                    isWorkflowChatMessage(msg) &&
                    msg.id === workflowMessageId
                  ) {
                    return {
                      ...msg,
                      workflowData: {
                        ...msg.workflowData,
                        stepApprovalRequired: approvalRequired
                          ? transitionId
                          : null,
                      },
                    };
                  }
                  return msg;
                }),
              },
            },
          },
        };
      });
    },

    updateWorkflowStatus: (workflowMessageId, workflowStatus) => {
      set((state) => {
        const { selectedId } = state.employee;
        if (!selectedId) return state;
        const session = state.chat.sessions[selectedId];
        if (!session) return state;

        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...session,
                messages: session.messages.map((msg) => {
                  if (
                    isWorkflowChatMessage(msg) &&
                    msg.id === workflowMessageId
                  ) {
                    return {
                      ...msg,
                      workflowData: {
                        ...msg.workflowData,
                        workflowStatus,
                      },
                    };
                  }
                  return msg;
                }),
              },
            },
          },
        };
      });
    },

    removeWorkflowStepsWithoutOutput: () => {
      const { selectedId } = get().employee;
      if (!selectedId) return;

      set((state) => {
        const session = state.chat.sessions[selectedId];
        if (!session) return state;

        // Find the latest workflow message
        const workflowMessages = session.messages.filter((msg) =>
          isWorkflowChatMessage(msg)
        );

        if (workflowMessages.length === 0) return state;

        const latestWorkflowMessage = workflowMessages[
          workflowMessages.length - 1
        ] as WorkflowChatMessage;
        const {
          available_nodes = [],
          stepStatus = {},
          stepMessages = {},
        } = latestWorkflowMessage.workflowData || {};

        // Filter steps that have meaningful output or completion
        const stepsWithOutput = available_nodes.filter((step: any) => {
          const status = stepStatus[step.transition_id];
          const hasMessages =
            stepMessages[step.transition_id]?.messages?.length > 0;

          // Keep steps that:
          // 1. Are completed and have messages (actual output)
          // 2. Are time_logged and have messages (logged results)
          // 3. Failed (to show what went wrong)
          // Remove steps that are:
          // - pending (never started)
          // - started but no output yet
          // - connecting but no output yet
          // - paused but no output yet
          return (
            (status === "completed" && hasMessages) ||
            (status === "time_logged" && hasMessages) ||
            status === "failed"
          );
        });

        // Update the workflow message with filtered steps
        const updatedMessages = session.messages.map((msg) => {
          if (
            isWorkflowChatMessage(msg) &&
            msg.id === latestWorkflowMessage.id
          ) {
            return {
              ...msg,
              workflowData: {
                ...msg.workflowData,
                available_nodes: stepsWithOutput,
              },
            };
          }
          return msg;
        });

        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...session,
                messages: updatedMessages,
              },
            },
          },
        };
      });
    },

    setWorkflowCancelled: () => {
      const { selectedId } = get().employee;
      if (!selectedId) return;

      set((state) => {
        const session = state.chat.sessions[selectedId];
        if (!session) return state;

        // Find the latest workflow message
        const workflowMessages = session.messages.filter((msg) =>
          isWorkflowChatMessage(msg)
        );

        if (workflowMessages.length === 0) return state;

        const latestWorkflowMessage = workflowMessages[
          workflowMessages.length - 1
        ] as WorkflowChatMessage;

        // Update the workflow message to mark it as cancelled
        const updatedMessages = session.messages.map((msg) => {
          if (
            isWorkflowChatMessage(msg) &&
            msg.id === latestWorkflowMessage.id
          ) {
            return {
              ...msg,
              workflowData: {
                ...msg.workflowData,
                cancelled: true,
              },
            };
          }
          return msg;
        });

        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...session,
                messages: updatedMessages,
              },
            },
          },
        };
      });
    },

    // Delegation message actions
    addDelegationMessage: (employee) => {
      const { selectedId } = get().employee;
      if (!selectedId) return;
      const newMessage: DelegationChatMessage = {
        id: crypto.randomUUID(),
        type: "delegation",
        senderType: SenderType.ASSISTANT,
        delegationData: {
          employee,
          isApproved: false,
          isApproveLoading: false,
          isTaskStarted: false,
          taskData: undefined,
        },
        timestamp: new Date(),
        content: "",
      };
      set((state) => {
        const session = state.chat.sessions[selectedId] || {};
        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...session,
                messages: [...(session.messages || []), newMessage],
                chatStarted: true,
                isTyping: false,
                pagination: {
                  ...session.pagination,
                  totalRawMessages: session.pagination.totalRawMessages + 1,
                },
              },
            },
          },
        };
      });
    },

    updateDelegationApprovalStatus: (delegationMessageId, isApproved) => {
      const { selectedId } = get().employee;
      if (!selectedId) return;
      set((state) => {
        const session = state.chat.sessions[selectedId];
        if (!session) return state;
        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...session,
                messages: session.messages.map((msg) => {
                  if (isDelegationChatMessage(msg)) {
                    if (msg.id === delegationMessageId) {
                      return {
                        ...msg,
                        delegationData: {
                          ...msg.delegationData,
                          isApproved,
                        },
                      };
                    }
                  }

                  return msg;
                }),
              },
            },
          },
        };
      });
    },

    setDelegationLoading: (delegationMessageId, isLoading) => {
      const { selectedId } = get().employee;
      if (!selectedId) return;
      set((state) => {
        const session = state.chat.sessions[selectedId];
        if (!session) return state;
        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...session,
                messages: session.messages.map((msg) => {
                  if (isDelegationChatMessage(msg)) {
                    if (msg.id === delegationMessageId) {
                      return {
                        ...msg,
                        delegationData: {
                          ...msg.delegationData,
                          isApproveLoading: isLoading,
                        },
                      };
                    }
                  }
                  return msg;
                }),
              },
            },
          },
        };
      });
    },

    setDelegationTaskStarted: (delegationMessageId, taskData) => {
      const { selectedId } = get().employee;
      if (!selectedId) return;
      set((state) => {
        const session = state.chat.sessions[selectedId];
        if (!session) return state;
        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...session,
                messages: session.messages.map((msg) => {
                  if (isDelegationChatMessage(msg)) {
                    if (msg.id === delegationMessageId) {
                      return {
                        ...msg,
                        delegationData: {
                          ...msg.delegationData,
                          isTaskStarted: true,
                          isApproveLoading: false,
                          taskData,
                        },
                      };
                    }
                  }
                  return msg;
                }),
              },
            },
          },
        };
      });
    },

    addCreateEmployeeMessage: (message, taskDescription) => {
      const { selectedId } = get().employee;
      if (!selectedId) return;
      const newMessage: CreateEmployeeChatMessage = {
        id: crypto.randomUUID(),
        type: "create-employee",
        senderType: SenderType.ASSISTANT,
        createEmployeeData: {
          message,
          taskDescription,
        },
        timestamp: new Date(),
        content: "",
      };
      set((state) => {
        const session = state.chat.sessions[selectedId] || {};
        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [selectedId]: {
                ...session,
                messages: [...(session.messages || []), newMessage],
                chatStarted: true,
                isTyping: false,
                pagination: {
                  ...session.pagination,
                  totalRawMessages: session.pagination.totalRawMessages + 1,
                },
              },
            },
          },
        };
      });
    },

    // MCP execution actions
    setMcpExecutionStarted: (toolName, logo, description) => {
      set((state) => ({
        mcpExecution: {
          isExecuting: true,
          toolName,
          logo,
          description,
        },
      }));
    },
    setMcpExecutionEnded: () => {
      set((state) => ({
        mcpExecution: {
          isExecuting: false,
          toolName: null,
          logo: null,
          description: null,
        },
      }));
    },

    // Knowledge fetch actions
    setKnowledgeFetchStarted: () => {
      set((state) => ({
        knowledgeFetch: {
          isActive: true,
        },
      }));
    },
    setKnowledgeFetchEnded: () => {
      set((state) => ({
        knowledgeFetch: {
          isActive: false,
        },
      }));
    },

    // Load more messages actions
    setIsLoadingMoreMessages: (employeeId, isLoading) => {
      set((state) => ({
        chat: {
          ...state.chat,
          sessions: {
            ...state.chat.sessions,
            [employeeId]: {
              ...state.chat.sessions[employeeId],
              pagination: {
                ...state.chat.sessions[employeeId].pagination,
                isLoadingMore: isLoading,
              },
            },
          },
        },
      }));
    },
    loadMoreMessages: (employeeId, messages, metadata) => {
      set((state) => {
        const session = state.chat.sessions[employeeId];
        if (!session) return state;
        return {
          chat: {
            ...state.chat,
            sessions: {
              ...state.chat.sessions,
              [employeeId]: {
                ...session,
                // Prepend older messages to the beginning of the array
                messages: [...messages, ...session.messages],
                pagination: {
                  ...session.pagination,
                  currentPage: metadata.currentPage,
                  totalPages: metadata.totalPages,
                  hasNextPage: metadata.hasNextPage,
                  total: metadata.total,
                  pageSize: metadata.pageSize,
                  isLoadingMore: false,
                  totalRawMessages: metadata.totalRawMessages,
                },
              },
            },
          },
        };
      });
    },
  })
);
