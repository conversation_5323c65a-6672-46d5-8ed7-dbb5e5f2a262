interface UseWorkflowSSEStreamProps {
  correlationId: string | null;
  enabled?: boolean;
  onError?: (error: string) => void;
  onComplete?: () => void;
}

import { useCallback, useEffect, useRef, useState } from "react";
import { useEmployeeManagementStore } from "./useEmployeeManagementStore";
import { SenderType, SSEEventType, StreamMessageType } from "@/shared/enums";
import { SSEEvent, SSEStreamData } from "@/shared/interfaces";
import { useWorkflowStore } from "./use-workflow";
import { toast } from "sonner";

export const useWorkflowSSEStream = ({
  correlationId,
  enabled = true,
  onError,
  onComplete,
}: UseWorkflowSSEStreamProps) => {
  const eventSourceRef = useRef<EventSource | null>(null);
  const [isSseConnected, setIsSseConnected] = useState(false);

  const {
    startStreaming,
    appendStreamingContent,
    completeStreaming,
    setStreamError,
    resetStream,
    chat: { sessions, currentStreamingMessage },
    employee: { selectedId },
    setTyping,
    updateWorkflowStepStatus,
    setWorkflowMessageThinking,
    setIsWorkflowStarted,
    setWorkflowStepResult,
    addWorkflowStepMessage,
    removeWorkflowStepsWithoutOutput,
    setWorkflowCancelled,
    updateWorkflowApprovalStatus,
    updateStepApprovalRequired,
    updateWorkflowStatus,
  } = useEmployeeManagementStore();

  const { setActiveCorrelationId } = useWorkflowStore();

  const streamState = selectedId ? sessions[selectedId]?.streamState : null;

  const parseSSEData = useCallback((data: string): SSEStreamData | null => {
    try {
      return JSON.parse(data);
    } catch (error) {
      console.error("Failed to parse SSE data:", error);
      return null;
    }
  }, []);

  // Throttle content updates to improve performance during rapid streaming
  const contentBufferRef = useRef<string>("");
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const flushContentBuffer = useCallback(() => {
    if (contentBufferRef.current) {
      appendStreamingContent(contentBufferRef.current, SenderType.ASSISTANT);
      contentBufferRef.current = "";
    }
  }, [appendStreamingContent]);

  const throttledAppendContent = useCallback(
    (content: string) => {
      contentBufferRef.current += content;
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      updateTimeoutRef.current = setTimeout(flushContentBuffer, 16); // ~60fps
    },
    [flushContentBuffer]
  );

  const stopSSEConnection = useCallback(() => {
    if (!isSseConnected && !eventSourceRef.current) return; // Already stopped, prevent loop
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    setIsSseConnected(false);
    resetStream();
    setActiveCorrelationId(null);
  }, [isSseConnected, resetStream, setActiveCorrelationId]);

  const handleSSEEvent = useCallback(
    (event: MessageEvent) => {
      const eventData: SSEEvent = {
        event: event.type,
        data: event.data,
      };

      // Handle keep-alive
      if (event.type === SSEEventType.KEEP_ALIVE) {
        return;
      }

      // Handle connection event
      if (event.type === SSEEventType.CONNECTION) {
        return;
      }

      // Handle workflow-update event for step progress
      if (event.type === SSEEventType.WORKFLOW_UPDATE) {
        // Parse event data for transition_id and status
        const parsedData = parseSSEData(eventData.data);
        // Use type assertion since transition_id is not in SSEStreamData interface
        const transitionId = (parsedData as any)?.transition_id;
        const stepStatus = (parsedData as any)?.status;
        const message = (parsedData as any)?.message;
        const approvalRequired = (parsedData as any)?.approval_required;
        const workflowStatus = (parsedData as any)?.workflow_status;

        // Store result for completed step
        if (
          (parsedData as any)?.status === "completed" &&
          transitionId &&
          (parsedData as any)?.result
        ) {
          setWorkflowStepResult(transitionId, (parsedData as any).result);
        }
        if (message) {
          setWorkflowMessageThinking(message);
        }
        if (transitionId && stepStatus) {
          // Find the latest workflow message in the current session
          if (selectedId && sessions[selectedId]?.messages) {
            const workflowMessages = sessions[selectedId].messages.filter(
              (msg) => (msg as any).type === "workflow"
            );
            if (workflowMessages.length > 0) {
              const latestWorkflowMessage =
                workflowMessages[workflowMessages.length - 1];
              updateWorkflowStepStatus(
                latestWorkflowMessage.id,
                transitionId,
                stepStatus
              );
              if (message) {
                addWorkflowStepMessage(
                  latestWorkflowMessage.id,
                  transitionId,
                  message,
                  stepStatus
                );
              }

              // Update approval status if provided in the response
              if (typeof approvalRequired === "boolean") {
                updateWorkflowApprovalStatus(
                  latestWorkflowMessage.id,
                  approvalRequired
                );

                // Also track which specific step needs approval
                updateStepApprovalRequired(
                  latestWorkflowMessage.id,
                  transitionId,
                  approvalRequired
                );
              }

              // Update workflow status if provided in the response
              if (workflowStatus) {
                updateWorkflowStatus(latestWorkflowMessage.id, workflowStatus);
              }
            }
          }
        }
        return;
      }

      // Handle workflow-completed event
      if (event.type === SSEEventType.WORKFLOW_COMPLETED) {
        flushContentBuffer();
        completeStreaming();
        stopSSEConnection();
        setWorkflowMessageThinking("");
        setIsWorkflowStarted(false);
        return;
      }

      // Handle workflow-failed event
      if (event.type === SSEEventType.WORKFLOW_FAILED) {
        flushContentBuffer();
        completeStreaming();
        setTyping(false);
        setStreamError("Workflow failed");
        stopSSEConnection();
        setIsWorkflowStarted(false);
        return;
      }

      // Handle workflow-cancelled event
      if (event.type === SSEEventType.WORKFLOW_CANCELLED) {
        flushContentBuffer();
        completeStreaming();
        setTyping(false);
        setStreamError("Workflow cancelled");
        stopSSEConnection();
        setIsWorkflowStarted(false);

        // Mark workflow as cancelled
        setWorkflowCancelled();

        // Remove workflow steps that don't have output
        removeWorkflowStepsWithoutOutput();

        // Show toast notification for workflow cancellation
        toast.error("Workflow execution stopped");

        return;
      }

      if (event.type === SSEEventType.MESSAGE_STREAM_STARTED) {
        const parsedData = parseSSEData(eventData.data);
        if (parsedData?.status === "started" && correlationId) {
          startStreaming(correlationId);
        }
        return;
      }

      if (event.type === SSEEventType.MESSAGE_STREAMING) {
        const parsedData = parseSSEData(eventData.data);
        if (
          parsedData?.content &&
          parsedData?.message_type === StreamMessageType.STREAMING_CHUNK
        ) {
          throttledAppendContent(parsedData.content);
        }
        return;
      }

      if (event.type === SSEEventType.MESSAGE_END) {
        const parsedData = parseSSEData(eventData.data);
        if (parsedData?.status === "completed") {
          // Flush any remaining content before completing
          flushContentBuffer();
          completeStreaming();
          setTyping(false);
          onComplete?.();
        }
        return;
      }
    },
    [
      correlationId,
      parseSSEData,
      startStreaming,
      throttledAppendContent,
      flushContentBuffer,
      completeStreaming,
      setStreamError,
      setTyping,
      onError,
      onComplete,
      stopSSEConnection,
      updateWorkflowStepStatus,
      selectedId,
      sessions,
      setWorkflowStepResult,
      addWorkflowStepMessage,
      setWorkflowMessageThinking,
      setIsWorkflowStarted,
      removeWorkflowStepsWithoutOutput,
      setWorkflowCancelled,
      updateWorkflowApprovalStatus,
      updateWorkflowStatus,
      updateStepApprovalRequired,
    ]
  );

  const startSSEConnection = useCallback(
    async (id: string) => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
      try {
        const baseUrl = process.env.NEXT_PUBLIC_WORKFLOW_EXECUTION_URL;
        const sseUrl = `${baseUrl}/workflow-execute/stream/${id}`;
        const eventSource = new EventSource(sseUrl);
        eventSourceRef.current = eventSource;
        eventSource.addEventListener(SSEEventType.KEEP_ALIVE, handleSSEEvent);
        eventSource.addEventListener(SSEEventType.CONNECTION, handleSSEEvent);
        eventSource.addEventListener(
          SSEEventType.WORKFLOW_UPDATE,
          handleSSEEvent
        );
        eventSource.addEventListener(
          SSEEventType.WORKFLOW_COMPLETED,
          handleSSEEvent
        );
        eventSource.addEventListener(
          SSEEventType.WORKFLOW_FAILED,
          handleSSEEvent
        );
        eventSource.addEventListener(
          SSEEventType.WORKFLOW_CANCELLED,
          handleSSEEvent
        );
        eventSource.addEventListener(
          SSEEventType.MESSAGE_STREAM_STARTED,
          handleSSEEvent
        );
        eventSource.addEventListener(
          SSEEventType.MESSAGE_STREAMING,
          handleSSEEvent
        );
        eventSource.addEventListener(SSEEventType.MESSAGE_END, handleSSEEvent);
        eventSource.onerror = (error) => {
          console.error("SSE connection error:", error);
          stopSSEConnection();
        };
        eventSource.onopen = () => {
          setIsSseConnected(true);
        };
      } catch (error) {
        console.error("Failed to create SSE connection:", error);
        stopSSEConnection();
      }
    },
    [handleSSEEvent, setStreamError, setTyping, onError, stopSSEConnection]
  );

  // Only create connection if correlationId changes and enabled
  useEffect(() => {
    if (!enabled || !correlationId || eventSourceRef.current || isSseConnected)
      return;
    startSSEConnection(correlationId);
  }, [correlationId, enabled, startSSEConnection]);

  return {
    isSseConnected,
  };
};
