import { useCallback, useEffect, useRef, useState } from "react";
import {
  useEmployeeManagementStore,
  isDelegationChatMessage,
  DelegationChatMessage,
  isCreateEmployeeChatMessage,
  CreateEmployeeChatMessage,
} from "./useEmployeeManagementStore";
import {
  SenderType,
  SSEEventType,
  StreamMessageContentType,
  StreamMessageSource,
  StreamMessageType,
} from "@/shared/enums";
import { SSEEvent, SSEStreamData, Employee } from "@/shared/interfaces";
import { useWorkflowStore } from "./use-workflow";
import { sanitizeString } from "@/services/helper";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";
import { LOCAL_STORAGE_KEYS } from "@/shared/constants";

interface UseSSEStreamProps {
  sessionId: string | null;
  enabled?: boolean;
  onError?: (error: string) => void;
  onComplete?: () => void;
}

export const useSSEStream = ({
  sessionId,
  enabled = true,
  onError,
  onComplete,
}: UseSSEStreamProps) => {
  const eventSourceRef = useRef<EventSource | null>(null);
  const [isSseConnected, setIsSseConnected] = useState(false);
  const queryClient = useQueryClient();

  const {
    appendStreamingContent,
    completeStreaming,
    setStreamError,
    startStreaming,
    setTyping,
    resetStream,
    setDelegationThinkingContent,
    addDelegationMessage,
    addCreateEmployeeMessage,
    setThinkingStatusText,
    setMcpExecutionStarted,
    setMcpExecutionEnded,
    setKnowledgeFetchStarted,
    setKnowledgeFetchEnded,
    chat: { sessions, currentStreamingMessage },
    employee: { selectedId },
    addWorkflowMessage,
    setIsWorkflowStarted,
    clearDiscoveredEmployees,
    setDelegationTaskStarted,
    addTokensFromSSE,
  } = useEmployeeManagementStore();
  const { setCurrentWorkflow, setActiveCorrelationId } = useWorkflowStore();

  const streamState = selectedId ? sessions[selectedId]?.streamState : null;

  const parseSSEData = useCallback((data: string): SSEStreamData | null => {
    try {
      return JSON.parse(data);
    } catch (error) {
      return null;
    }
  }, []);

  // Throttle content updates to improve performance during rapid streaming
  const contentBufferRef = useRef<string>("");
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const flushContentBuffer = useCallback(() => {
    if (contentBufferRef.current) {
      appendStreamingContent(contentBufferRef.current, SenderType.ASSISTANT);
      contentBufferRef.current = "";
    }
  }, [appendStreamingContent]);

  const throttledAppendContent = useCallback(
    (content: string) => {
      contentBufferRef.current += content;

      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }

      updateTimeoutRef.current = setTimeout(flushContentBuffer, 16); // ~60fps
    },
    [flushContentBuffer]
  );

  const handleSSEEvent = useCallback(
    (event: MessageEvent) => {
      const eventData: SSEEvent = {
        event: event.type,
        data: event.data,
      };
      // Always get the latest allWorkflows from the store to avoid race conditions
      const { allWorkflows } = useWorkflowStore.getState();
      // Parse data once
      const parsedData = parseSSEData(eventData.data);

      switch (event.type) {
        case SSEEventType.KEEP_ALIVE:
          return;
        case SSEEventType.MESSAGE_STREAM_STARTED:
          setThinkingStatusText("Preparing response");
          if (parsedData?.status === "started" && sessionId) {
            startStreaming(sessionId);
            if (selectedId) setDelegationThinkingContent(selectedId, null);
          }
          return;
        case SSEEventType.MESSAGE_STREAMING:
          // Extract and accumulate tokens from non-streaming-chunk events
          if (
            parsedData?.message_type !== StreamMessageType.STREAMING_CHUNK &&
            parsedData?.models_usage &&
            selectedId
          ) {
            const { prompt_tokens = 0, completion_tokens = 0 } =
              parsedData.models_usage;
            if (prompt_tokens > 0 || completion_tokens > 0) {
              addTokensFromSSE(selectedId, prompt_tokens, completion_tokens);
            }
          }

          // Handle delegation thinking content
          if (
            parsedData?.metadata?.content_type ===
              StreamMessageContentType.DELEGATION &&
            (parsedData?.source === StreamMessageSource.ORCHESTRATOR_EMPLOYEE ||
              parsedData?.source ===
                StreamMessageSource.GENERAL_KNOWLEDGE_EMPLOYEE)
          ) {
            if (selectedId)
              setDelegationThinkingContent(
                selectedId,
                parsedData?.metadata?.content || ""
              );
            return;
          }
          // Handle response
          if (
            parsedData?.metadata?.content_type ===
              StreamMessageContentType.RESPONSE &&
            parsedData?.source === StreamMessageSource.ORCHESTRATOR_EMPLOYEE
          ) {
            throttledAppendContent(parsedData.metadata?.content || "");
            if (selectedId) setDelegationThinkingContent(selectedId, null);
            setThinkingStatusText("Responding");
            return;
          }
          if (
            parsedData?.source === StreamMessageSource.DISCOVERY_MASTER_EMPLOYEE
          ) {
            if (parsedData?.metadata?.agent_found === true) {
              // Insert employee card: extract employee_profile from parsedData
              const employeeProfile =
                parsedData.metadata.matched_employee?.employee_profile;
              if (employeeProfile) {
                // Add 2 seconds delay to load the delegation card, similar to workflow
                setTimeout(() => {
                  addDelegationMessage({
                    ...employeeProfile,
                    assigned_task:
                      parsedData.metadata?.matched_employee?.assigned_task,
                  });
                }, 4000);
              }
              return;
            } else if (parsedData?.metadata?.agent_found === false) {
              // No employee found, show create employee card
              const taskDescription =
                (parsedData.metadata as any)?.task_description || "";
              const message =
                (parsedData.metadata as any)?.message ||
                "No suitable employee found for this task. Would you like to create a new employee?";

              setTimeout(() => {
                addCreateEmployeeMessage(message, taskDescription);
              }, 4000);
              return;
            }
          }
          // Handle streaming chunk
          if (
            parsedData?.content &&
            parsedData?.message_type === StreamMessageType.STREAMING_CHUNK
          ) {
            throttledAppendContent(parsedData.content);
            if (selectedId) setDelegationThinkingContent(selectedId, null);

            setThinkingStatusText("Fetching response");
            return;
          }
          if (
            (parsedData?.source === StreamMessageSource.WEB_SEARCH_EMPLOYEE ||
              parsedData?.source ===
                StreamMessageSource.KNOWLEDGE_BASE_EMPLOYEE) &&
            parsedData?.message_type === "text"
          ) {
            let parsedContent;
            try {
              parsedContent = JSON.parse(parsedData.content || "");
            } catch (e) {
              parsedContent = { content: parsedData.content || "" };
            }
            appendStreamingContent(
              parsedContent.content || "",
              SenderType.ASSISTANT,
              {
                employeeSearchType: parsedData.source as string,
                searchSources: parsedContent.sources || [],
              }
            );
            if (selectedId) setDelegationThinkingContent(selectedId, null);
            setThinkingStatusText("Responding");
            return;
          }
          return;
        case SSEEventType.WORKFLOW_EXECUTION_STARTED:
          if (parsedData?.status === "started") {
            const workflowSteps =
              allWorkflows.find(
                (workflow) => workflow.id === parsedData.workflow_id
              )?.available_nodes || [];
            setCurrentWorkflow(
              selectedId || "",
              (parsedData.workflow_id || "") +
                (parsedData.correlation_id || "") || null
            );
            //add wait for 2 seconds to    load the workflow steps
            setTimeout(() => {
              addWorkflowMessage({
                available_nodes: workflowSteps,
                stepStatus: {},
                approvalRequired: false,
                approved: false,
                selectedWorkflowId: parsedData.workflow_id,
                correlationId: parsedData.correlation_id,
                workflowStatus: "running", // Initialize with running status
              });
              setActiveCorrelationId(parsedData.correlation_id || null);
              setIsWorkflowStarted(true);
            }, 2000);
            return;
          }
          return;
        case SSEEventType.MESSAGE_END:
          if (parsedData?.status === "completed") {
            // Flush any remaining content before completing
            flushContentBuffer();
            completeStreaming();
            setTyping(false);
            if (selectedId) setDelegationThinkingContent(selectedId, null);
            setMcpExecutionEnded(); // End MCP execution when message completes
            setKnowledgeFetchEnded(); // End knowledge fetch when message completes
            onComplete?.();
            setThinkingStatusText("");
          }
          return;
        case SSEEventType.MCP_EXECUTION_STARTED:
          if (parsedData?.tool_name) {
            setMcpExecutionStarted(
              parsedData.tool_name,
              parsedData.logo,
              parsedData.description
            );
            setThinkingStatusText(
              `Executing ${sanitizeString(parsedData.tool_name)} tool`
            );
          }
          return;
        case SSEEventType.KNOWLEDGE_FETCH_STARTED:
          setThinkingStatusText("Knowledge fetching");
          setKnowledgeFetchStarted();
          return;
        case SSEEventType.TASK_DELEGATION_SUCCESS:
          if (parsedData?.status === "success") {
            // Find the delegation message that needs to be updated
            const store = useEmployeeManagementStore.getState();
            const currentSession = selectedId
              ? store.chat.sessions[selectedId]
              : null;
            if (currentSession) {
              // Find the most recent delegation message that's been approved but not yet started
              const delegationMessage = currentSession.messages
                .slice()
                .reverse()
                .find((msg) => {
                  if (isDelegationChatMessage(msg)) {
                    return (
                      msg.delegationData.isApproved &&
                      !msg.delegationData.isTaskStarted
                    );
                  }
                  return false;
                }) as DelegationChatMessage | undefined;

              if (delegationMessage) {
                setDelegationTaskStarted(delegationMessage.id, {
                  task_id: parsedData.task_id!,
                  agent_id: parsedData.agent_id!,
                  conversation_id: parsedData.conversation_id!,
                  agent_session_id: parsedData.agent_session_id!,
                  title: parsedData.title!,
                });
              }
            }
          }
          toast.success("Task started successfully");
          // Invalidate and refetch the global chat history to show the new task
          queryClient.invalidateQueries({ queryKey: ["globalChatHistory"] });
          return;
        default:
          return;
      }
    },
    [
      sessionId,
      parseSSEData,
      startStreaming,
      throttledAppendContent,
      flushContentBuffer,
      completeStreaming,
      setStreamError,
      setTyping,
      onError,
      onComplete,
      setDelegationThinkingContent,
      selectedId,
      addDelegationMessage,
      addCreateEmployeeMessage,
      clearDiscoveredEmployees,
      setThinkingStatusText,
      queryClient,
      setDelegationTaskStarted,
      setMcpExecutionStarted,
      setMcpExecutionEnded,
      setKnowledgeFetchStarted,
      setKnowledgeFetchEnded,
      addTokensFromSSE,
    ]
  );

  const startSSEConnection = useCallback(
    async (sessionId: string) => {
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }

      try {
        const baseUrl = process.env.NEXT_PUBLIC_AGENT_URL;
        const sseUrl = `${baseUrl}agents/sessions/${sessionId}/chat/stream`;
        const eventSource = new EventSource(sseUrl);
        eventSourceRef.current = eventSource;

        eventSource.addEventListener(SSEEventType.KEEP_ALIVE, handleSSEEvent);
        eventSource.addEventListener(
          SSEEventType.MESSAGE_STREAM_STARTED,
          handleSSEEvent
        );
        eventSource.addEventListener(
          SSEEventType.MESSAGE_STREAMING,
          handleSSEEvent
        );
        eventSource.addEventListener(
          SSEEventType.WORKFLOW_EXECUTION_STARTED,
          handleSSEEvent
        );
        eventSource.addEventListener(SSEEventType.MESSAGE_END, handleSSEEvent);
        eventSource.addEventListener(
          SSEEventType.MCP_EXECUTION_STARTED,
          handleSSEEvent
        );
        eventSource.addEventListener(
          SSEEventType.KNOWLEDGE_FETCH_STARTED,
          handleSSEEvent
        );
        eventSource.addEventListener(
          SSEEventType.TASK_DELEGATION_SUCCESS,
          handleSSEEvent
        );

        eventSource.onerror = (error: any) => {
          // If the browser is offline, the 'offline' event handler manages the UI.
          if (!navigator.onLine) {
            return;
          }
          const parsedData = JSON.parse(error.data);
          if (parsedData?.error_message) {
            setStreamError(parsedData.error_message);
          }
          setIsSseConnected(false);
          setTyping(false);
        };

        eventSource.onopen = () => {
          setIsSseConnected(true);
          // On successful connection, clear any previous errors.
          const pendingMessage = localStorage.getItem(
            LOCAL_STORAGE_KEYS.RUH_GLOBAL_AGENT_PENDING_MESSAGE
          );
          setStreamError(null, pendingMessage ? true : false);
        };
      } catch (error) {
        const errorMessage =
          "Failed to establish streaming connection. Please check your connection and try again.";
        setStreamError(errorMessage);
        setTyping(false);
        onError?.(errorMessage);
      }
    },
    [handleSSEEvent, setStreamError, setTyping, onError]
  );

  const stopSSEConnection = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    setIsSseConnected(false);
    resetStream();
    clearDiscoveredEmployees();
    setKnowledgeFetchEnded();
  }, [resetStream, clearDiscoveredEmployees, setKnowledgeFetchEnded]);

  // Auto-connect when sessionId is available and enabled
  useEffect(() => {
    if (!enabled || !sessionId) {
      return;
    }

    startSSEConnection(sessionId);

    // Event listeners to handle internet connectivity changes.
    const handleOffline = () => {
      setStreamError("You appear to be offline. Please check your connection.");
    };

    const handleOnline = () => {
      // When connection is restored, clear the offline message
      // and allow the stream to reconnect automatically.
      setStreamError(null);
    };

    window.addEventListener("offline", handleOffline);
    window.addEventListener("online", handleOnline);

    return () => {
      stopSSEConnection();
      window.removeEventListener("offline", handleOffline);
      window.removeEventListener("online", handleOnline);
    };
  }, [sessionId, enabled, startSSEConnection, stopSSEConnection]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (updateTimeoutRef.current) {
        clearTimeout(updateTimeoutRef.current);
      }
      stopSSEConnection();
    };
  }, [stopSSEConnection]);

  return {
    isStreaming: streamState?.isStreaming || false,
    currentStreamingMessage,
    streamError: streamState?.error || null,
    isSseConnected,
  };
};
