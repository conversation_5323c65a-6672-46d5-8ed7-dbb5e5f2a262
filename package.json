{"name": "ruh-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@google-cloud/storage": "^7.16.0", "@hookform/resolvers": "^5.0.1", "@livekit/components-react": "^2.9.2", "@livekit/components-styles": "^1.1.5", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.74.2", "@tanstack/react-table": "^8.21.3", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-react": "^8.6.0", "firebase": "^11.6.0", "framer-motion": "^12.19.1", "livekit-client": "^2.11.2", "livekit-server-sdk": "^2.12.0", "lucide-react": "^0.538.0", "mixpanel-browser": "^2.67.0", "moment": "^2.30.1", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "react-markdown": "^10.1.0", "react-pdf": "^9.2.1", "react-player": "^2.16.0", "react-speech-recognition": "^4.0.1", "recharts": "2.15.4", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tslib": "^2.8.1", "tw-animate-css": "^1.2.5", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tanstack/eslint-plugin-query": "^5.73.3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/axios": "^0.14.4", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-speech-recognition": "^3.9.6", "@types/testing-library__user-event": "^4.1.1", "eslint": "^9", "eslint-config-next": "15.2.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.13", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}, "eslintConfig": {"extends": []}, "packageManager": "yarn@4.9.1"}